import asyncio
import os
import signal
import sys
from typing import Set

from dotenv import load_dotenv
from lucas_common_components.logging import setup_logger
from lucas_common_components.nacos.core.nacos_manager import NacosConfigManager
from lucas_common_components.router.router_manager import ProtocolType, RouterManager
from tortoise import Tortoise

from src.config.config_model import AIChatAppConfig
from src.infrastructure.db.db_connection import DatabaseConnection

dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
load_dotenv(dotenv_path)


class Server:
    """应用程序启动器类"""

    def __init__(self):
        """初始化启动器"""
        self.logger = setup_logger(__name__)
        self.logger.info("FastAPI 应用程序已初始化")
        self._signal_handlers: Set[signal.Signals] = set()
        self.router_manager = RouterManager(gateway_port=8080)
        # 注册配置
        NacosConfigManager.get_instance().register_config(AIChatAppConfig)

    async def init_config(self):
        """初始化配置"""
        try:
            # await config_manager.init()
            self.logger.info("Nacos 配置初始化成功")
        except Exception as e:
            self.logger.error(f"Nacos 配置初始化失败: {e}")
            raise

    async def init_db(self):
        """初始化 Tortoise ORM 数据库连接池"""
        try:
            # 获取 Tortoise ORM 所需的配置
            config = DatabaseConnection.get_tortoise_config()

            await Tortoise.init(config=config)
            self.logger.info("Tortoise ORM 初始化成功")

            # 可选：自动生成数据库表（开发阶段可用）
            await Tortoise.generate_schemas(safe=True)
            self.logger.info("数据库表生成成功")

        except Exception as e:
            self.logger.error(f"Tortoise ORM 初始化失败: {e}")
            raise

    async def close_db(self):
        """关闭 Tortoise ORM 连接"""
        await Tortoise.close_connections()
        self.logger.info("Tortoise ORM 连接已关闭")
        
        # 关闭 PostgreSQL checkpoint 连接池
        try:
            from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient
            await PGCheckpointClient.close_saver()
            self.logger.info("PostgreSQL checkpoint 连接池已关闭")
        except Exception as e:
            self.logger.error(f"关闭 PostgreSQL checkpoint 连接池失败: {e}")

    async def register_router(self):
        from src.adapter.router.ai_chat_router import router as ai_chat

        self.router_manager.register_router(
            router=ai_chat, name="ai_chat", protocol=[ProtocolType.GATEWAY]
        )

        from src.adapter.swagger import router as swagger_router

        self.router_manager.register_router(
            router=swagger_router,
            name="swagger_router",
            protocol=[ProtocolType.GATEWAY],
        )

        # 注册新的专业录取分数线数据路由
        from src.adapter.router.major_admission_router import (
            router as major_admission_router,
        )

        self.router_manager.register_router(
            router=major_admission_router,
            name="major_admission_router",
            protocol=[ProtocolType.GATEWAY],
        )

        # 注册等效分计算路由
        from src.adapter.router.equivalent_score_controller import (
            router as equivalent_score_router,
        )

        self.router_manager.register_router(
            router=equivalent_score_router,
            name="equivalent_score_router",
            protocol=[ProtocolType.GATEWAY],
        )

        # 注册健康检查路由
        from src.adapter.router.health_router import router as health_router

        self.router_manager.register_router(
            router=health_router,
            name="health_router",
            protocol=[ProtocolType.GATEWAY],
        )

        # 注册等效分计算路由
        # from src.adapter.api.equivalent_score_controller import (
        #     router as equivalent_score_router,
        # )
        #
        # self.router_manager.register_router(
        #     router=equivalent_score_router,
        #     name="equivalent_score_router",
        #     protocol=[ProtocolType.GATEWAY],
        # )

        # # 注册模块化流程工作流路由
        # from src.adapter.api.workflow_router import router as workflow_router
        #
        # self.router_manager.register_router(
        #     router=workflow_router,
        #     name="workflow_router",
        #     protocol=[ProtocolType.GATEWAY],
        # )

    async def run(self):
        """异步运行应用程序"""
        try:
            await self.init_config()  # 初始化配置
            await self.init_db()  # 初始化 Tortoise ORM
            await self.register_router()

            try:
                await self.router_manager.start()
            finally:
                await self.router_manager.shutdown()
                await self.close_db()  # 关闭数据库连接池
        except Exception as e:
            self.logger.error(f"应用程序运行失败: {str(e)}", exc_info=True)
            raise


def main():
    """主函数"""
    try:
        if sys.platform == "win32":
            # Use WindowsSelectorEventLoopPolicy for Windows
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
            loop = asyncio.new_event_loop()
        else:
            loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        starter = Server()

        # 优雅关停的回调
        is_shutting_down = False

        async def shutdown():
            nonlocal is_shutting_down
            if is_shutting_down:
                return
            is_shutting_down = True
            starter.logger.info("收到关停信号，开始优雅关停...")
            try:
                await starter.router_manager.shutdown()
                starter.logger.info("router_manager 已优雅关闭")
            except Exception as e:
                starter.logger.error(f"router_manager 关闭异常: {e}")
            try:
                await starter.close_db()
                starter.logger.info("数据库连接已优雅关闭")
            except Exception as e:
                starter.logger.error(f"数据库关闭异常: {e}")
            starter.logger.info("优雅关停流程结束")

        # 信号处理器
        def handle_signal():
            asyncio.ensure_future(shutdown())

        if sys.platform != "win32":
            for sig in (signal.SIGINT, signal.SIGTERM):
                loop.add_signal_handler(sig, handle_signal)

        try:
            loop.run_until_complete(starter.run())
        except KeyboardInterrupt:
            # 兼容 Windows 下 Ctrl+C
            loop.run_until_complete(shutdown())
        finally:
            # 等待所有未完成的任务再关闭事件循环，防止 RuntimeError
            pending = [t for t in asyncio.all_tasks(loop) if not t.done()]
            if pending:
                loop.run_until_complete(
                    asyncio.gather(*pending, return_exceptions=True)
                )
            if not loop.is_closed():
                loop.close()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()
