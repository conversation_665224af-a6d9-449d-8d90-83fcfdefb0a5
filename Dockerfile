# 基础镜像
FROM beiyikeji-registry.cn-beijing.cr.aliyuncs.com/common/python:3.12-slim

# 设置工作目录
WORKDIR /home/<USER>

# 将本地代码复制到容器中
COPY . /home/<USER>

# 确保.env文件存在并有正确的权限
RUN touch /home/<USER>/.env && chmod 644 /home/<USER>/.env

## 安装依赖
RUN pip install -r requirements.txt -i https://68034dc1dc197ec05c5f2350:<EMAIL>/6801b53d8330212176aef352/pypi/repo-dipup --extra-index-url https://mirrors.aliyun.com/pypi/simple/

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["python", "main.py"]