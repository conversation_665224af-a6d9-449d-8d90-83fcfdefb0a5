#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试MajorCutoffExpert的功能
"""

import asyncio
import logging
import sys
import os
import time
from typing import Dict, Any, List

from lucas_common_components.logging import setup_logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.app.zhiyuan.experts import MajorCutoffExpert
from src.app.zhiyuan.models.model import ZhiYuanState
from langchain_core.messages import HumanMessage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"major_cutoff_test_{int(time.time())}.log"),
    ],
)

logger = setup_logger(__name__)


class MockStreamWriter:
    """模拟StreamWriter，用于捕获MajorCutoffExpert的输出"""

    def __init__(self):
        self.outputs: List[Dict[str, Any]] = []

    def __call__(self, data):
        if data:
            self.outputs.append(data)
            if "data" in data and hasattr(data["data"], "text"):
                logger.info(f"收到输出: {data['data'].text[:100]}...")
            else:
                logger.info(f"收到输出: {data}")


async def test_expert():
    """
    测试MajorCutoffExpert的功能
    """
    logger.info("开始测试MajorCutoffExpert")

    # 创建专家实例
    expert = MajorCutoffExpert()

    # 测试查询列表
    test_queries = [
        "清华大学计算机专业2023年在江苏的分数线是多少",
        "清华大学2023年在河北的分数线",
        "清华大学计算机专业在内蒙古的录取分数线是多少",
        "对比一下清华大学和北京大学计算机专业的分数线",
    ]

    for i, query in enumerate(test_queries):
        logger.info(f"测试查询 {i + 1}: {query}")

        # 创建模拟StreamWriter
        writer = MockStreamWriter()

        # 构造测试状态
        test_state = ZhiYuanState(
            conversationId=f"test_conversation_{i}",
            messages=[HumanMessage(content=query)],
            isInit=False,
            nextAgents=[],
            userInfo={},
            messageId=f"test_message_{i}",
            bizRuntimeBO=None,
            guideGotoDemo="",
        )

        # 调用专家
        start_time = time.time()
        command = await expert(test_state, writer)
        end_time = time.time()

        logger.info(f"专家处理耗时: {end_time - start_time:.2f}秒")
        logger.info(f"专家返回命令: {command}")
        logger.info(f"专家输出数量: {len(writer.outputs)}")

        # 打印输出摘要
        if writer.outputs:
            all_text = ""
            for output in writer.outputs:
                if "data" in output and hasattr(output["data"], "text"):
                    all_text += output["data"].text

            logger.info(f"输出摘要: {all_text[:200]}...")
            print(f"\n--- 专家回答 ---\n{all_text}\n--- 回答结束 ---\n")

        # 在测试之间添加延迟
        await asyncio.sleep(2)


if __name__ == "__main__":
    try:
        logger.info("开始MajorCutoffExpert测试")

        # 运行测试
        asyncio.run(test_expert())

        logger.info("MajorCutoffExpert测试完成")
    except Exception as e:
        logger.exception(f"测试脚本执行异常: {str(e)}")
        sys.exit(1)
