"""
模块化流程工作流测试脚本

该脚本用于测试模块化流程工作流的正确性。
"""

import asyncio
import os
import sys
import argparse
import logging
from dotenv import load_dotenv
from lucas_common_components.logging import setup_logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.adapter.workflow.modular_pipeline_workflow import run_modular_pipeline_workflow

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = setup_logger(__name__)

# 加载环境变量
load_dotenv()


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="模块化流程工作流测试")
    parser.add_argument(
        "--query",
        type=str,
        default="请解释什么是大型语言模型以及它们的工作原理",
        help="测试查询",
    )
    parser.add_argument("--profile", type=str, default="", help="用户Profile文件路径")
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_args()

    # 读取Profile内容（如果提供了文件路径）
    profile_content = ""
    if args.profile and os.path.exists(args.profile):
        with open(args.profile, "r", encoding="utf-8") as f:
            profile_content = f.read()
    else:
        profile_content = """
        姓名：李明
        年龄：30
        职业：软件工程师
        兴趣：人工智能、机器学习、编程
        教育背景：计算机科学学士
        """

    logger.info(f"开始测试模块化流程工作流，查询: {args.query}")

    # 运行工作流
    result = run_modular_pipeline_workflow(
        query=args.query, student_profile=profile_content
    )

    # 打印结果
    logger.info("工作流执行完成")
    logger.info("最终输出: %s", result.get("final_output", ""))

    if result.get("errors"):
        logger.warning("执行过程中出现的错误:")
        for error in result.get("errors", []):
            logger.warning("- %s", error)

    # 打印每个节点的输出
    logger.info("\n====== 各节点输出 ======")
    logger.info("节点1输出: %s", result.get("node1_output", "")[:100] + "...")
    logger.info("LLM1输出: %s", result.get("llm1_output", "")[:100] + "...")
    logger.info("质量检查输出: %s", result.get("qa_check_output", "")[:100] + "...")
    logger.info("LLM1-HQA输出: %s", result.get("llm1_hqa_output", "")[:100] + "...")
    logger.info("LLM11输出: %s", result.get("llm11_output", "")[:100] + "...")


if __name__ == "__main__":
    asyncio.run(main())
