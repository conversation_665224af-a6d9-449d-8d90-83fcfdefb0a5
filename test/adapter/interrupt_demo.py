# -*- coding: utf-8 -*-
"""
Async-for + interrupt + resume 完整示例 (FastAPI-Free 纯脚本)
==========================================================

功能：
1. 用 `LangGraph` 构建一个简单工作流：`gate` 节点检测 "审核" 关键词并触发
   `interrupt`，`reply` 节点调用 LLM 回复。
2. 第一次以 `async for` 消费 `graph.astream()`；当捕获 `ActionRequest` 后停止。
3. 模拟人工审核完毕，再用 `resume=True` 重新开启异步流并打印最终回复。

依赖安装：
    pip install langgraph langchain-openai asyncio
"""

import asyncio
import functools
import uuid
from typing import TypedDict, List, Dict, Any, TypeVar

from langchain_core.runnables import RunnableLambda
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph
from langgraph.types import interrupt as do_interrupt, Command

# 定义类型变量
T = TypeVar("T")
R = TypeVar("R")


# 装饰器：将异步函数转换为同步函数
def async_to_sync(async_func):
    """
    装饰器：将异步函数转换为同步函数

    Args:
        async_func: 要转换的异步函数

    Returns:
        转换后的同步函数
    """

    @functools.wraps(async_func)
    def sync_wrapper(*args, **kwargs):
        return asyncio.run(async_func(*args, **kwargs))

    return sync_wrapper


# 使用模拟的 LLM 响应，避免 API 密钥问题
def mock_llm_response(text):
    return f"这是对 '{text}' 的模拟回复。审核已通过，可以继续处理。"


# 不使用真实的 API 调用
# llm = Tongyi(model_name="qwen-turbo", dashscope_api_key="sk-df50aa96d0ec4d9a83b79570419fc9ba", temperature=0)


# 子图的状态模式
class ChildState(TypedDict):
    messages: List[dict]  # 子图使用的消息列表
    internal_status: str  # 子图内部使用的状态，父图不需要知道


# 子图类
class ChildGraphWrapper:
    """
    子图类：封装子图的创建、编译和执行

    这个类将子图的创建、编译和执行封装在一起，提供了一个简洁的接口来使用子图。
    通过 __call__ 方法，可以直接将类实例作为可调用对象使用，简化了子图的调用过程。
    """

    def __init__(self, use_checkpointer: bool = True):
        """
        初始化并编译子图

        Args:
            use_checkpointer: 是否使用 checkpointer 来支持中断和恢复
        """
        # 创建子图
        self.graph = StateGraph(state_schema=ChildState)

        # 添加节点
        check_keyword = RunnableLambda(self._check_keyword_node)
        self.graph.add_node("check_keyword", check_keyword)

        # 设置入口和出口
        self.graph.set_entry_point("check_keyword")
        self.graph.set_finish_point("check_keyword")

        # 编译子图
        self.compiled_graph = self.graph.compile(checkpointer=use_checkpointer)

        print("子图已初始化并编译完成")

    def _check_keyword_node(self, state: ChildState) -> Dict[str, Any]:
        """
        子图的节点函数，检查关键词并可能触发中断

        Args:
            state: 子图状态

        Returns:
            更新后的子图状态
        """
        text = state["messages"][-1]["content"]
        print(f"子图处理: {text}")
        if "审核" in text:
            print("子图中触发中断")
            do_interrupt({"mentor_action": "questionnaire"})
        return {"internal_status": "已检测关键词"}

    async def __call__(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步调用子图，使用 astream 流式执行

        这个方法使得 ChildGraphWrapper 实例可以像函数一样被调用，例如：
        child_output = await child_graph(child_input)

        Args:
            state: 输入状态（可以是父图状态，会自动转换为子图状态）

        Returns:
            处理后的父图状态
        """
        # 将输入状态转换为子图状态
        if "messages" in state and "internal_status" not in state:
            # 如果是父图状态，转换为子图状态
            child_input = {"messages": state["messages"], "internal_status": ""}
        else:
            # 如果已经是子图状态，直接使用
            child_input = state

        print("开始流式调用子图...")
        output = None
        async for chunk in self.compiled_graph.astream(child_input):
            print(f"子图流式输出: {chunk}")
            output = chunk

        # 如果是父图状态，将子图输出转换回父图状态
        if "result" in state:
            internal_status = (
                output.get("internal_status", "无状态") if output else "无输出"
            )
            return {
                "messages": state["messages"],
                "result": f"子图处理完成: {internal_status}",
            }
        else:
            # 如果是子图状态，直接返回子图输出
            return output


# 创建子图实例
child_graph_wrapper = ChildGraphWrapper()

# ---- 构建父图 -----------------------------------------------------------


# 父图的状态模式
class ParentState(TypedDict):
    messages: List[dict]  # 父图使用的消息列表
    result: str  # 父图特有的结果字段，子图不需要知道


# 修改回复节点函数，使用父图的状态模式
def reply_node_for_parent(state: ParentState) -> Dict[str, Any]:
    msgs = state["messages"]
    text = "\n".join([m["content"] for m in msgs])
    print(f"父图回复处理: {text}")
    print(f"父图状态: {state['result']}")

    # 使用模拟的 LLM 响应
    ai_msg = mock_llm_response(text)
    print(f"生成的回复: {ai_msg}")

    return {
        "messages": msgs + [{"role": "assistant", "content": ai_msg}],
        "result": state["result"] + " -> 已生成回复",
    }


# 父图
parent_graph = StateGraph(state_schema=ParentState)
parent_graph.add_node(
    "child_gate", child_graph_wrapper
)  # 直接使用子图包装器的同步调用方法
parent_graph.add_node("reply", RunnableLambda(reply_node_for_parent))
parent_graph.add_edge("child_gate", "reply")
parent_graph.set_entry_point("child_gate")

# 创建内存 checkpointer 用于支持中断和恢复
checkpointer = MemorySaver()
compiled_parent_graph = parent_graph.compile(checkpointer=checkpointer)


# ---- 4. 演示 async for + interrupt ---------------------------------------
async def main_async():
    print("\n=== 第一次调用：可能触发中断 ===")
    thread_id = str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}

    try:
        # 第一次调用，可能触发中断
        result = None
        # 注意：现在我们使用 ParentState 状态模式，需要提供 result 字段的初始值
        async for chunk in compiled_parent_graph.astream(
            {
                "messages": [{"role": "user", "content": "这条消息需要审核"}],
                "result": "",  # 初始化父图特有的结果字段
            },
            config=config,
            subgraphs=True,
            stream_mode=["custom", "values"],
        ):
            print("[chunk]", chunk)
            result = chunk

        # 检查是否有中断信号
        if result and "__interrupt__" in result:
            interrupt_data = result["__interrupt__"][0]
            print("\n⚠️ 收到中断信号:", interrupt_data)

            # ---- 5. 模拟人工审核通过，resume ----
            print("\n=== 人工审核通过，开始 resume ===")

            # 恢复执行并传递人工审核结果
            async for chunk in compiled_parent_graph.astream(
                Command(resume={"approved": True}), config=config
            ):
                if isinstance(chunk, dict) and "messages" in chunk:
                    print("[stream]", chunk["messages"][-1])
                else:
                    print("[resume chunk]", chunk)

            # 获取最终状态
            final_state = compiled_parent_graph.get_state(config)
            print("\n最终父图状态:", final_state.values)
        else:
            print("没有触发中断，流程结束")

    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback

        traceback.print_exc()


# 主函数调用
if __name__ == "__main__":
    # 直接使用 asyncio.run 运行异步主函数
    asyncio.run(main_async())
