 #!/bin/bash

# 测试MajorCutoffExpert的API调用脚本

# 设置API基础URL（根据实际环境修改）
API_BASE="http://localhost:8080"

# # 1. 创建会话
# echo "创建会话..."
# CONVERSATION_ID=$(curl -s -X POST "$API_BASE/aichat/create" \
#   -H "Content-Type: application/json" \
#   -H 'Lucas-uniq-UserId: 14' \
#   -d '{
#     "input": "我想了解大学专业录取分数线"
#   }' | grep -o '"conversation_id":"[^"]*"' | cut -d'"' -f4)

# if [ -z "$CONVERSATION_ID" ]; then
#   echo "创建会话失败，请确保服务已启动"
#   exit 1
# fi

# echo "会话ID: $CONVERSATION_ID"

# 2. 发送专业录取分数线查询
CONVERSATION_ID="1919358918656524289"
echo "发送专业录取分数线查询..."
curl -N -X POST "$API_BASE/aichat/chat" \
  -H "Content-Type: application/json" \
  -H 'Lucas-uniq-UserId: 14' \
  -d '{
    "conversation_id": "'$CONVERSATION_ID'",
    "messages": [
      {
        "text": "清华大学计算机专业2023年在江苏的分数线是多少",
        "role": "user"
      }
    ]
  }'

echo -e "\n\n"

# 3. 发送多学校对比查询
echo "发送多学校对比查询..."
curl -N -X POST "$API_BASE/aichat/chat" \
  -H "Content-Type: application/json" \
  -H 'Lucas-uniq-UserId: 14' \
  -d '{
    "conversation_id": "'$CONVERSATION_ID'",
    "messages": [
      {
        "text": "清华大学计算机专业在内蒙古的录取分数线是多少",
        "role": "user"
      }
    ]
  }'

echo -e "\n\n"

# 4. 发送多专业查询
echo "发送多专业查询..."
curl -N -X POST "$API_BASE/aichat/chat" \
  -H "Content-Type: application/json" \
  -H 'Lucas-uniq-UserId: 14' \
  -d '{
    "conversation_id": "'$CONVERSATION_ID'",
    "messages": [
      {
        "text": "清华大学2023年在河北的分数线",
        "role": "user"
      }
    ]
  }'

echo -e "\n\n"