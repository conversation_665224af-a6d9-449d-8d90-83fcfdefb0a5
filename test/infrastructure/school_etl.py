import asyncio

import pandas as pd
from dotenv import load_dotenv
from tortoise import Tortoise

from src.config.tortoise_config import TORTOISE_ORM

# 导入模型和CRUD
from src.infrastructure.db.crud.ai.SchoolRepository import SchoolRepository

load_dotenv()


# 读取Excel
def read_data_from_xlsx(filepath: str, sheet_name=0) -> list[dict]:
    try:
        df = pd.read_excel(filepath, sheet_name=sheet_name, dtype=str)
        df = df.where(pd.notnull(df), None)
        return df.to_dict(orient="records")
    except Exception as e:
        print(f"ERROR: {e}")
        return []


def get_bool(val):
    if val is None:
        return False
    return str(val).strip() == "是"


def get_str(val):
    if val is None:
        return None
    return str(val).strip()


def transform_row(row: dict) -> dict:
    return {
        "name": get_str(row.get("代号及名称")),
        "province": get_str(row.get("院校省份")),
        "city": get_str(row.get("院校城市")),
        "college_type": get_str(row.get("水平")),
        "is_985": get_bool(row.get("985")),
        "is_211": get_bool(row.get("211")),
        "is_dual_class": get_bool(row.get("双一流")),
        "college_category": get_str(row.get("类型")),
        "college_property": get_str(row.get("性质")),
        "intro": get_str(row.get("DeepSeek-R1")),
    }


def find_school_name_key(row):
    for k in row.keys():
        if "代号" in k and "名称" in k:
            return k
    return None


async def load_data_to_db(xlsx_data_rows: list[dict]):
    await Tortoise.init(config=TORTOISE_ORM)
    print("Tortoise ORM initialized successfully.")

    created, updated, skipped = 0, 0, 0
    for idx, row in enumerate(xlsx_data_rows, 1):
        if idx <= 5:
            print(f"Row {idx} keys: {list(row.keys())}")
            school_name_key = find_school_name_key(row)
            print(f"Row {idx} school_name_key: {school_name_key}")
            if school_name_key:
                print(f"Row {idx} school_name value: {row.get(school_name_key)}")
            else:
                print(f"Row {idx} 没有找到学校名称字段")
        school_name_key = find_school_name_key(row)
        if not school_name_key:
            print("找不到学校名称字段，当前行keys:", list(row.keys()))
            continue
        school_name = get_str(row.get(school_name_key))
        if not school_name:
            skipped += 1
            continue
        data = transform_row(row)
        try:
            print(f"Try upsert: {school_name}, data: {data}")
            school_obj, is_created = await SchoolRepository.upsert_school(
                match_keys={"name": school_name}, defaults=data
            )
            print(f"Result: school_obj={school_obj}, is_created={is_created}")
            if is_created:
                created += 1
            else:
                updated += 1
        except Exception as e:
            print(f"Error on row {idx} ({school_name}): {e}")
            skipped += 1
    print(f"Created: {created}, Updated: {updated}, Skipped: {skipped}")
    await Tortoise.close_connections()


if __name__ == "__main__":
    XLSX_FILE_PATH = "D:/edge_下载/院校清单.xlsx"  # 修改为你的实际路径
    xlsx_data = read_data_from_xlsx(XLSX_FILE_PATH)
    if xlsx_data:
        asyncio.run(load_data_to_db(xlsx_data))
    else:
        print("No data loaded from Excel.")
