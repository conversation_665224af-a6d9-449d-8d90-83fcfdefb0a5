# #!/usr/bin/env python3
# """
# 测试循环查询功能实现
# """
#
# import asyncio
# import sys
# import os
#
# import pytest
#
#
# # 添加项目根目录到 Python 路径
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
#
#
# from lucas_common_components.logging import setup_logger
#
# # 设置日志
# logger = setup_logger(name=__name__, level="DEBUG")
#
#
# @pytest.mark.asyncio
# async def test_question_extraction():
#     """测试问题提取功能"""
#
#     print("=== 测试问题提取功能 ===\n")
#
#     # 模拟LLM1输出
#     mock_llm1_output = """
#     # 质量评估报告
#
#     基于对学生画像的分析，我发现以下几个需要进一步验证的问题：
#
#     1. 关于计算机专业的就业前景，需要了解2024年最新的市场需求情况
#     2. 人工智能方向的发展趋势如何，是否还有增长空间
#     3. 北京地区985高校的录取分数线变化趋势
#     4. 内向性格的学生在技术岗位上的适应性如何
#
#     这些问题需要通过搜索来获得最新的权威信息。
#     """
#
#     try:
#         print("1. 测试LLM问题提取:")
#         questions = await _extract_query_questions(mock_llm1_output)
#
#         print(f"   提取到 {len(questions)} 个问题:")
#         for i, q in enumerate(questions):
#             print(f"   {i + 1}. {q}")
#
#         if len(questions) >= 3:
#             print("   ✅ 问题提取成功")
#         else:
#             print("   ⚠️ 提取的问题数量较少")
#
#         return questions
#
#     except Exception as e:
#         print(f"   ❌ 问题提取失败: {e}")
#         import traceback
#
#         print(f"   错误详情: {traceback.format_exc()}")
#         return []
#
#
# @pytest.mark.asyncio
# async def test_loop_query():
#     """测试循环查询功能"""
#
#     print("\n=== 测试循环查询功能 ===\n")
#
#     # 测试问题列表
#     test_questions = [
#         "2024年计算机专业就业前景如何",
#         "人工智能行业发展趋势分析",
#         "985高校录取分数线变化情况",
#     ]
#
#     print("2. 测试循环查询:")
#     print(f"   查询问题数量: {len(test_questions)}")
#     for i, q in enumerate(test_questions):
#         print(f"   问题 {i + 1}: {q}")
#
#     print("\n📋 注意：以下是循环查询的详细日志输出，请关注每个步骤的执行情况：")
#     print("=" * 80)
#
#     try:
#         search_results = await _loop_query_with_bocha(test_questions)
#
#         print("=" * 80)
#         print("\n📊 循环查询测试结果总结:")
#         print(f"   返回结果数量: {len(search_results)}")
#
#         for i, result in enumerate(search_results):
#             question = result.get("question", "")
#             success = result.get("success", False)
#             total = result.get("total", 0)
#             duration = result.get("query_duration", 0)
#
#             print(f"\n   结果 {i + 1}:")
#             print(f"     问题: {question}")
#             print(f"     成功: {'✅' if success else '❌'}")
#             print(f"     结果数: {total}")
#             print(f"     耗时: {duration:.2f}秒")
#
#             if success and result.get("results"):
#                 first_result = result["results"][0]
#                 content_preview = first_result.get("content", "")[:100] + "..."
#                 print(f"     内容预览: {content_preview}")
#             elif not success:
#                 error = result.get("error", "未知错误")
#                 error_type = result.get("error_type", "未知类型")
#                 print(f"     错误类型: {error_type}")
#                 print(f"     错误信息: {error}")
#
#         successful_count = sum(1 for r in search_results if r.get("success", False))
#         success_rate = (
#             successful_count / len(search_results) * 100 if search_results else 0
#         )
#
#         print("\n📈 循环查询统计:")
#         print(f"   总查询数: {len(search_results)}")
#         print(f"   成功查询: {successful_count}")
#         print(f"   成功率: {success_rate:.1f}%")
#
#         if search_results:
#             print("   ✅ 循环查询功能测试完成")
#         else:
#             print("   ⚠️ 没有返回查询结果")
#
#         return search_results
#
#     except Exception as e:
#         print("=" * 80)
#         print(f"   ❌ 循环查询失败: {e}")
#         import traceback
#
#         print(f"   错误详情: {traceback.format_exc()}")
#         return []
#
#
# @pytest.mark.asyncio
# async def test_qa_lst_integration():
#     """测试QA-LST整合功能"""
#
#     print("\n=== 测试QA-LST整合功能 ===\n")
#
#     # 模拟数据
#     mock_llm1_output = """
#     # 质量评估报告
#
#     经过分析，发现以下几个关键问题：
#     1. 需要验证计算机专业的最新就业数据
#     2. 人工智能领域的发展前景需要更新信息
#     3. 学生的个人特质与专业匹配度需要进一步评估
#     """
#
#     mock_search_results = [
#         {
#             "question": "2024年计算机专业就业前景如何",
#             "success": True,
#             "results": [
#                 {
#                     "content": "根据最新统计，2024年计算机专业毕业生就业率达到95%以上，平均起薪较去年上涨8%。",
#                     "source": "教育部就业统计",
#                     "title": "2024年计算机专业就业报告",
#                     "url": "https://example.com/report1",
#                 }
#             ],
#             "total": 1,
#         },
#         {
#             "question": "人工智能行业发展趋势分析",
#             "success": False,
#             "error": "API调用失败",
#             "results": [],
#             "total": 0,
#         },
#     ]
#
#     try:
#         print("3. 测试QA-LST整合:")
#         print(f"   LLM1输出长度: {len(mock_llm1_output)}")
#         print(f"   搜索结果数量: {len(mock_search_results)}")
#
#         integrated_output = await _qa_lst_integration(
#             mock_llm1_output, mock_search_results
#         )
#
#         print("\n   整合结果:")
#         print(f"   输出长度: {len(integrated_output)}")
#         print(f"   输出预览: {integrated_output[:300]}...")
#
#         if integrated_output and len(integrated_output) > 100:
#             print("   ✅ QA-LST整合成功")
#         else:
#             print("   ⚠️ 整合结果较短")
#
#         return integrated_output
#
#     except Exception as e:
#         print(f"   ❌ QA-LST整合失败: {e}")
#         import traceback
#
#         print(f"   错误详情: {traceback.format_exc()}")
#         return ""
#
#
# @pytest.mark.asyncio
# async def test_full_qa_check_process():
#     """测试完整的QA检查流程"""
#
#     print("\n=== 测试完整QA检查流程 ===\n")
#
#     # 模拟状态
#     mock_state = {
#         "student_profile": "测试用户画像",
#         "node0_output": "节点0输出内容",
#         "node1_output": "节点1输出内容",
#         "llm1_output": """
#         # LLM1质量评估报告
#
#         基于前面的分析，我认为需要进一步验证以下问题：
#
#         1. 计算机专业2024年的就业市场情况
#         2. 人工智能技术发展对相关专业的影响
#         3. 学生选择的专业与个人兴趣的匹配度
#
#         建议通过搜索获取最新的权威数据来支撑分析结论。
#         """,
#         "errors": [],
#     }
#
#     try:
#         print("4. 测试完整QA检查流程:")
#         print(f"   输入状态键: {list(mock_state.keys())}")
#         print(f"   LLM1输出长度: {len(mock_state['llm1_output'])}")
#
#         result_state = await qa_check_process(mock_state)
#
#         print("\n   QA检查结果:")
#         print(f"   返回状态键: {list(result_state.keys())}")
#
#         qa_output = result_state.get("qa_check_output")
#         search_results = result_state.get("search_results", [])
#         query_questions = result_state.get("query_questions", [])
#         errors = result_state.get("errors", [])
#
#         print(f"   QA输出长度: {len(str(qa_output)) if qa_output else 0}")
#         print(f"   搜索结果数量: {len(search_results)}")
#         print(f"   查询问题数量: {len(query_questions)}")
#         print(f"   错误数量: {len(errors)}")
#
#         if qa_output and len(str(qa_output)) > 100:
#             print("   ✅ 完整QA检查流程成功")
#
#             # 显示部分结果
#             print("\n   QA输出预览:")
#             print(f"   {str(qa_output)[:400]}...")
#
#         else:
#             print("   ⚠️ QA检查输出较短或为空")
#
#         if errors:
#             print("\n   发现错误:")
#             for error in errors:
#                 print(f"     - {error}")
#
#         return result_state
#
#     except Exception as e:
#         print(f"   ❌ 完整QA检查流程失败: {e}")
#         import traceback
#
#         print(f"   错误详情: {traceback.format_exc()}")
#         return mock_state
#
#
# async def main():
#     """主测试函数"""
#
#     print("🚀 开始测试循环查询功能实现\n")
#
#     # 测试各个组件
#     questions = await test_question_extraction()
#
#     if questions:
#         search_results = await test_loop_query()
#     else:
#         search_results = []
#
#     await test_qa_lst_integration()
#
#     await test_full_qa_check_process()
#
#     print("\n🎉 循环查询功能测试完成")
#
#     print("\n📋 测试总结:")
#     print(f"✅ 问题提取功能 - {'通过' if questions else '需要检查'}")
#     print(f"✅ 循环查询功能 - {'通过' if search_results else '需要检查Bocha API配置'}")
#     print("✅ QA-LST整合功能 - 通过")
#     print("✅ 完整QA检查流程 - 通过")
#
#     print("\n💡 使用建议:")
#     print("1. 确保设置了 BOCHA_API_KEY 环境变量")
#     print("2. 检查 Bocha API 的网络连接")
#     print("3. 验证 LLM 服务的可用性")
#     print("4. 根据实际需要调整查询问题的数量和类型")
#
#
# if __name__ == "__main__":
#     asyncio.run(main())
