#!/usr/bin/env python3
"""
快速运行循环查询测试的脚本
"""

import asyncio
import sys
import os

from src.domain.generate_report.query_process import _loop_query_with_bocha

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


async def quick_test():
    """快速测试循环查询功能"""

    print("🚀 快速测试循环查询功能")
    print("=" * 60)

    # 检查环境配置
    api_key = os.getenv("BOCHA_API_KEY")
    base_url = os.getenv("BOCHA_BASE_URL", "https://api.bochaai.com")

    print("📋 环境检查:")
    print(f"   BOCHA_API_KEY: {'✅ 已配置' if api_key else '❌ 未配置'}")
    print(f"   BOCHA_BASE_URL: {base_url}")

    if not api_key:
        print("\n⚠️ 警告: 未配置BOCHA_API_KEY，将使用后备方案")
        print("   如需测试真实API，请设置环境变量:")
        print("   export BOCHA_API_KEY=your_api_key")

    # 简单的测试问题 - 只测试1个问题避免API限额浪费
    test_questions = ["2024年计算机专业就业前景"]

    print("\n🔍 测试问题:")
    for i, q in enumerate(test_questions):
        print(f"   {i + 1}. {q}")

    print("\n📝 开始循环查询（使用Bocha Web Search API）...")
    print("=" * 60)

    # 执行循环查询
    results = await _loop_query_with_bocha(test_questions)

    print("=" * 60)
    print("✅ 循环查询完成!")

    # 显示结果
    print("\n📊 结果统计:")
    print(f"   查询问题数: {len(test_questions)}")
    print(f"   返回结果数: {len(results)}")

    successful = sum(1 for r in results if r.get("success", False))
    print(f"   成功查询数: {successful}")
    print(
        f"   成功率: {successful / len(results) * 100:.1f}%"
        if results
        else "   成功率: 0%"
    )

    return results


if __name__ == "__main__":
    print("🎯 循环查询功能快速测试")
    print("=" * 60)
    print("💡 这个脚本将快速测试循环查询功能并显示详细日志")
    print("   请观察日志输出中的每个步骤执行情况")
    print("=" * 60)

    asyncio.run(quick_test())
