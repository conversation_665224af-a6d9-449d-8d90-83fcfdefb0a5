import pytest
from unittest.mock import Mock, AsyncMock, patch
from Tea.exceptions import TeaException
from src.infrastructure.opensearch import (
    AliyunOpenSearchBasicClient,
    Config
)


class TestConfig:
    """测试配置类"""
    
    def test_config_creation(self):
        """测试配置对象创建"""
        config = Config(
            endpoint="test-endpoint.aliyuncs.com",
            protocol="HTTPS",
            access_key_id="test-ak-id",
            access_key_secret="test-ak-secret",
            user_agent="test-agent"
        )
        
        assert config.endpoint == "test-endpoint.aliyuncs.com"
        assert config.protocol == "HTTPS"
        assert config.access_key_id == "test-ak-id"
        assert config.access_key_secret == "test-ak-secret"
        assert config.user_agent == "test-agent"
        assert config.type is None  # 初始值为None，在客户端初始化时设置默认值


class TestAliyunOpenSearchBasicClient:
    """测试底层基础客户端"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.config = Config(
            endpoint="test-endpoint.aliyuncs.com",
            protocol="HTTPS",
            access_key_id="test-ak-id",
            access_key_secret="test-ak-secret"
        )
    
    def test_client_initialization(self):
        """测试客户端初始化"""
        with patch('alibabacloud_credentials.client.Client'):
            client = AliyunOpenSearchBasicClient(self.config)
            assert client._endpoint == "test-endpoint.aliyuncs.com"
            assert client._protocol == "HTTPS"
    
    def test_client_initialization_with_invalid_config(self):
        """测试无效配置的客户端初始化"""
        with pytest.raises(TeaException) as exc_info:
            AliyunOpenSearchBasicClient(None)
        
        assert "config" in str(exc_info.value)
    
    def test_build_runtime_config(self):
        """测试运行时配置构建"""
        with patch('alibabacloud_credentials.client.Client'):
            client = AliyunOpenSearchBasicClient(self.config)
            
            # 创建mock的runtime对象
            mock_runtime = Mock()
            mock_runtime.validate.return_value = None
            mock_runtime.read_timeout = 30000
            mock_runtime.connect_timeout = 10000
            mock_runtime.http_proxy = None
            mock_runtime.https_proxy = None
            mock_runtime.no_proxy = None
            mock_runtime.max_idle_conns = 50
            mock_runtime.autoretry = True
            mock_runtime.max_attempts = 3
            mock_runtime.backoff_policy = "linear"
            mock_runtime.backoff_period = 1
            mock_runtime.ignore_ssl = False
            
            runtime_config = client._build_runtime_config(mock_runtime)
            
            assert runtime_config["readTimeout"] == 30000
            assert runtime_config["connectTimeout"] == 10000
            assert runtime_config["retry"]["retryable"] == True
            assert runtime_config["retry"]["maxAttempts"] == 3
    
    def test_build_request_headers(self):
        """测试请求头构建"""
        with patch('alibabacloud_credentials.client.Client'):
            client = AliyunOpenSearchBasicClient(self.config)
            
            # Mock相关工具函数
            with patch('alibabacloud_tea_util.client.Client.get_user_agent', return_value="test-agent"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_date', return_value="test-date"), \
                 patch('alibabacloud_tea_util.client.Client.get_nonce', return_value="test-nonce"):
                
                headers = client._request_builder.build_request_headers({"Custom-Header": "custom-value"})
                
                assert headers["user-agent"] == "test-agent"
                assert headers["Content-Type"] == "application/json"
                assert headers["Date"] == "test-date"
                assert headers["X-Opensearch-Nonce"] == "test-nonce"
                assert headers["Custom-Header"] == "custom-value"
    
    @pytest.mark.asyncio
    async def test_execute_request_success(self):
        """测试成功的请求执行"""
        with patch('alibabacloud_credentials.client.Client') as mock_cred_client:
            # Mock认证客户端
            mock_cred_instance = Mock()
            mock_cred_instance.get_access_key_id.return_value = "test-ak"
            mock_cred_instance.get_access_key_secret.return_value = "test-sk"
            mock_cred_instance.get_security_token.return_value = None
            mock_cred_client.return_value = mock_cred_instance
            
            client = AliyunOpenSearchBasicClient(self.config)
            
            # Mock响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.headers = {"Content-Type": "application/json"}
            mock_response.body = Mock()
            
            # 创建异步mock函数
            async def mock_async_do_action(*args, **kwargs):
                return mock_response
            
            # Mock相关工具函数
            with patch('alibabacloud_tea_util.client.Client.read_as_string', return_value='{"result": "success"}'), \
                 patch('alibabacloud_tea_util.client.Client.parse_json', return_value={"result": "success"}), \
                 patch('alibabacloud_tea_util.client.Client.assert_as_map', return_value={"result": "success"}), \
                 patch('Tea.core.TeaCore.async_do_action', side_effect=mock_async_do_action), \
                 patch('Tea.core.TeaCore.allow_retry', return_value=True), \
                 patch('alibabacloud_tea_util.client.Client.get_user_agent', return_value="test-agent"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_date', return_value="test-date"), \
                 patch('alibabacloud_tea_util.client.Client.get_nonce', return_value="test-nonce"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_signature', return_value="test-signature"), \
                 patch('alibabacloud_tea_util.client.Client.to_jsonstring', return_value='{"test": "body"}'), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_content_md5', return_value="test-md5"):
                
                results = []
                async for result in client.execute_request(
                    method="POST",
                    pathname="/test/path",
                    query={"param": "value"},
                    body={"test": "data"}
                ):
                    results.append(result)
                
                assert len(results) == 1
                assert results[0]["body"]["result"] == "success"
    
    @pytest.mark.asyncio
    async def test_execute_request_error_response(self):
        """测试错误响应处理"""
        with patch('alibabacloud_credentials.client.Client') as mock_cred_client:
            # Mock认证客户端
            mock_cred_instance = Mock()
            mock_cred_instance.get_access_key_id.return_value = "test-ak"
            mock_cred_instance.get_access_key_secret.return_value = "test-sk"
            mock_cred_instance.get_security_token.return_value = None
            mock_cred_client.return_value = mock_cred_instance
            
            client = AliyunOpenSearchBasicClient(self.config)
            
            # Mock错误响应
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.status_message = "Bad Request"
            mock_response.body = Mock()
            
            # 创建异步mock函数
            async def mock_async_do_action(*args, **kwargs):
                return mock_response
            
            with patch('alibabacloud_tea_util.client.Client.read_as_string', return_value='{"error": "bad request"}'), \
                 patch('alibabacloud_tea_util.client.Client.is_4xx', return_value=True), \
                 patch('Tea.core.TeaCore.async_do_action', side_effect=mock_async_do_action), \
                 patch('Tea.core.TeaCore.allow_retry', return_value=True), \
                 patch('alibabacloud_tea_util.client.Client.get_user_agent', return_value="test-agent"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_date', return_value="test-date"), \
                 patch('alibabacloud_tea_util.client.Client.get_nonce', return_value="test-nonce"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_signature', return_value="test-signature"):
                
                with pytest.raises(TeaException) as exc_info:
                    async for result in client.execute_request(
                        method="POST",
                        pathname="/test/path"
                    ):
                        pass
                
                assert exc_info.value.args[0]["code"] == 400
                assert "Bad Request" in exc_info.value.args[0]["message"]
    
    @pytest.mark.asyncio
    async def test_execute_sync_request(self):
        """测试同步请求执行"""
        with patch('alibabacloud_credentials.client.Client') as mock_cred_client:
            # Mock认证客户端
            mock_cred_instance = Mock()
            mock_cred_instance.get_access_key_id.return_value = "test-ak"
            mock_cred_instance.get_access_key_secret.return_value = "test-sk"
            mock_cred_instance.get_security_token.return_value = None
            mock_cred_client.return_value = mock_cred_instance
            
            client = AliyunOpenSearchBasicClient(self.config)
            
            # Mock响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.headers = {"Content-Type": "application/json"}
            mock_response.body = Mock()
            
            # 创建异步mock函数
            async def mock_async_do_action(*args, **kwargs):
                return mock_response
            
            with patch('alibabacloud_tea_util.client.Client.read_as_string', return_value='{"result": "success"}'), \
                 patch('alibabacloud_tea_util.client.Client.parse_json', return_value={"result": "success"}), \
                 patch('alibabacloud_tea_util.client.Client.assert_as_map', return_value={"result": "success"}), \
                 patch('Tea.core.TeaCore.async_do_action', side_effect=mock_async_do_action), \
                 patch('Tea.core.TeaCore.allow_retry', return_value=True), \
                 patch('alibabacloud_tea_util.client.Client.get_user_agent', return_value="test-agent"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_date', return_value="test-date"), \
                 patch('alibabacloud_tea_util.client.Client.get_nonce', return_value="test-nonce"), \
                 patch('alibabacloud_opensearch_util.opensearch_util.OpensearchUtil.get_signature', return_value="test-signature"):
                
                result = await client.execute_sync_request(
                    method="GET",
                    pathname="/test/path"
                )
                
                assert result["body"]["result"] == "success" 