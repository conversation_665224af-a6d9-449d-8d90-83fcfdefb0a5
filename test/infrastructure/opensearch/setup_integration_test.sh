#!/bin/bash

# 阿里云OpenSearch集成测试配置脚本
# 使用方法：source test/infrastructure/opensearch/setup_integration_test.sh

echo "🔧 OpenSearch集成测试环境检查"
echo "================================"

echo ""
echo "1️⃣ 检查阿里云VPN连接..."
if ping -c 1 -W 3000 mse-a6991b72-nacos-ans.mse.aliyuncs.com > /dev/null 2>&1; then
    echo "✅ Nacos服务器可达"
else
    echo "❌ Nacos服务器不可达，请检查VPN连接"
    echo "   请确保已连接阿里云VPN"
    exit 1
fi

echo ""
echo "2️⃣ 测试Nacos配置获取..."
cd /Users/<USER>/Documents/Y-AI/yai-chat

python3 test/infrastructure/opensearch/check_nacos_config.py

if [ $? -eq 0 ]; then
    echo ""
    echo "3️⃣ 运行集成测试..."
    echo "pytest test/infrastructure/opensearch/test_real_opensearch_integration.py -v -s -m integration"
    echo ""
    echo "🎉 环境检查通过！可以运行集成测试了"
else
    echo ""
    echo "❌ 环境检查失败，请检查配置"
fi 