import pytest
from unittest.mock import Mock, AsyncMock, patch
import json
from src.infrastructure.opensearch import <PERSON>yunOpenSearchLLMClient, Config


class TestAliyunOpenSearchLLMClient:
    """测试高层LLM客户端"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.config = Config(
            endpoint="test-endpoint.aliyuncs.com",
            protocol="HTTPS",
            access_key_id="test-ak-id",
            access_key_secret="test-ak-secret"
        )
    
    @pytest.mark.asyncio
    async def test_searchDoc_success(self):
        """测试知识搜索成功场景"""
        with patch('src.infrastructure.opensearch.llm_client.search_client.AliyunOpenSearchBasicClient') as mock_basic_client:
            # Mock底层客户端
            mock_client_instance = Mock()
            mock_basic_client.return_value = mock_client_instance
            
            # Mock异步生成器
            async def mock_execute_request(*args, **kwargs):
                yield {"body": {"result": {"documents": [{"title": "test doc"}]}}}
            
            mock_client_instance.execute_request = mock_execute_request
            
            # 创建LLM客户端
            llm_client = AliyunOpenSearchLLMClient(self.config)
            
            # 执行搜索
            results = []
            async for result in llm_client.searchDoc(
                app_name="test-app",
                body={"question": {"text": "test query", "type": "TEXT"}},
                use_llm=True
            ):
                results.append(result)
            
            assert len(results) == 1
            assert "documents" in results[0]
            
            # 验证调用参数
            mock_basic_client.assert_called_once_with(self.config)
    
    @pytest.mark.asyncio
    async def test_multi_search_success(self):
        """测试多路搜索成功场景"""
        with patch('src.infrastructure.opensearch.llm_client.search_client.AliyunOpenSearchBasicClient') as mock_basic_client:
            # Mock底层客户端
            mock_client_instance = Mock()
            mock_basic_client.return_value = mock_client_instance
            
            # Mock异步生成器
            async def mock_execute_request(*args, **kwargs):
                yield {"body": {"result": {"data": [{"answer": "test answer"}]}}}
            
            mock_client_instance.execute_request = mock_execute_request
            
            # 创建LLM客户端
            llm_client = AliyunOpenSearchLLMClient(self.config)
            
            # 执行多路搜索
            results = []
            async for result in llm_client.multi_search(
                app_name="test-app",
                body={"question": {"text": "test query", "type": "TEXT"}},
                query_params={"param1": "value1"},
                use_llm=True
            ):
                results.append(result)
            
            assert len(results) == 1
            assert "data" in results[0]
    
    @pytest.mark.asyncio
    async def test_stream_multi_search_success(self):
        """测试流式多路搜索成功场景"""
        with patch('src.infrastructure.opensearch.llm_client.search_client.AliyunOpenSearchBasicClient') as mock_basic_client:
            # Mock底层客户端
            mock_client_instance = Mock()
            mock_basic_client.return_value = mock_client_instance
            
            # Mock流式响应
            async def mock_execute_request(*args, **kwargs):
                # 验证stream参数为True
                assert kwargs.get('stream') == True
                yield {"result": {"data": [{"answer": "partial answer"}]}}
                yield {"result": {"data": [{"answer": "complete answer"}]}}
            
            mock_client_instance.execute_request = mock_execute_request
            
            # 创建LLM客户端
            llm_client = AliyunOpenSearchLLMClient(self.config)
            
            # 执行流式搜索
            results = []
            async for result in llm_client.stream_multi_search(
                app_name="test-app",
                body={"question": {"text": "test query", "type": "TEXT"}},
                use_llm=True
            ):
                results.append(result)
            
            assert len(results) == 2
            assert all(result.startswith("data:") for result in results)
    
    @pytest.mark.asyncio
    async def test_stream_incremental_search_success(self):
        """测试流式增量搜索成功场景"""
        with patch('src.infrastructure.opensearch.llm_client.search_client.AliyunOpenSearchBasicClient') as mock_basic_client:
            # Mock底层客户端
            mock_client_instance = Mock()
            mock_basic_client.return_value = mock_client_instance
            
            # Mock流式响应数据
            stream_responses = [
                {"result": {"data": [{"answer": "Hello"}]}},
                {"result": {"data": [{"answer": "Hello world"}]}},
                {"result": {"data": [{"answer": "Hello world!"}]}}
            ]
            
            async def mock_execute_request(*args, **kwargs):
                for response in stream_responses:
                    yield response
            
            mock_client_instance.execute_request = mock_execute_request
            
            # 创建LLM客户端
            llm_client = AliyunOpenSearchLLMClient(self.config)
            
            # Mock stream_multi_search方法
            async def mock_stream_multi_search(*args, **kwargs):
                for response in stream_responses:
                    yield f"data:{json.dumps(response, ensure_ascii=False)}"
            
            llm_client.stream_multi_search = mock_stream_multi_search
            
            # 执行增量搜索
            results = []
            async for result in llm_client.stream_incremental_search(
                app_name="test-app",
                body={"question": {"text": "test query", "type": "TEXT"}},
                use_llm=True
            ):
                results.append(result)
            
            # 验证增量输出
            # 第一个响应："Hello"，第二个响应："Hello world"，第三个响应："Hello world!"
            # 增量应该是：第一个完整内容"Hello"，然后是增量" world"，最后是增量"!"
            expected_increments = ["Hello", " world", "!"]
            assert results == expected_increments
    
    @pytest.mark.asyncio
    async def test_searchDoc_with_llm_disabled(self):
        """测试禁用LLM的知识搜索"""
        with patch('src.infrastructure.opensearch.llm_client.search_client.AliyunOpenSearchBasicClient') as mock_basic_client:
            # Mock底层客户端
            mock_client_instance = Mock()
            mock_basic_client.return_value = mock_client_instance
            
            # Mock异步生成器
            async def mock_execute_request(*args, **kwargs):
                # 验证body中的LLM设置
                body = kwargs.get('body', {})
                assert body.get('options', {}).get('chat', {}).get('disable') == True
                yield {"body": {"result": {"documents": []}}}
            
            mock_client_instance.execute_request = mock_execute_request
            
            # 创建LLM客户端
            llm_client = AliyunOpenSearchLLMClient(self.config)
            
            # 执行搜索（禁用LLM）
            results = []
            async for result in llm_client.searchDoc(
                app_name="test-app",
                body={"question": {"text": "test query", "type": "TEXT"}},
                use_llm=False  # 禁用LLM
            ):
                results.append(result)
            
            assert len(results) == 1
    
    @pytest.mark.asyncio
    async def test_searchDoc_error_handling(self):
        """测试知识搜索错误处理"""
        with patch('src.infrastructure.opensearch.llm_client.search_client.AliyunOpenSearchBasicClient') as mock_basic_client:
            # Mock底层客户端抛出异常
            mock_client_instance = Mock()
            mock_basic_client.return_value = mock_client_instance
            
            async def mock_execute_request(*args, **kwargs):
                raise Exception("Network error")
                yield  # 这行永远不会执行，但让它成为一个异步生成器
            
            mock_client_instance.execute_request = mock_execute_request
            
            # 创建LLM客户端
            llm_client = AliyunOpenSearchLLMClient(self.config)
            
            # 执行搜索
            results = []
            async for result in llm_client.searchDoc(
                app_name="test-app",
                body={"question": {"text": "test query", "type": "TEXT"}}
            ):
                results.append(result)
            
            # 验证错误处理
            assert len(results) == 1
            assert "error" in results[0]
            assert "Network error" in results[0]["error"]
    
    @pytest.mark.asyncio
    async def test_stream_incremental_search_with_malformed_json(self):
        """测试增量搜索处理格式错误的JSON"""
        llm_client = AliyunOpenSearchLLMClient(self.config)
        
        # Mock stream_multi_search返回格式错误的数据
        async def mock_stream_multi_search(*args, **kwargs):
            yield "data:invalid json"
            yield "data:{\"result\": {\"data\": [{\"answer\": \"valid\"}]}}"
        
        llm_client.stream_multi_search = mock_stream_multi_search
        
        # 执行增量搜索
        results = []
        async for result in llm_client.stream_incremental_search(
            app_name="test-app",
            body={"question": {"text": "test query", "type": "TEXT"}}
        ):
            results.append(result)
        
        # 应该只返回有效的增量内容，忽略格式错误的JSON
        assert results == ["valid"]
    
    @pytest.mark.asyncio
    async def test_stream_incremental_search_with_done_signal(self):
        """测试增量搜索处理完成信号"""
        llm_client = AliyunOpenSearchLLMClient(self.config)
        
        # Mock stream_multi_search返回完成信号
        async def mock_stream_multi_search(*args, **kwargs):
            yield f"data:{json.dumps({'result': {'data': [{'answer': 'Hello'}]}})}"
            yield "data:[done]"
        
        llm_client.stream_multi_search = mock_stream_multi_search
        
        # 执行增量搜索
        results = []
        async for result in llm_client.stream_incremental_search(
            app_name="test-app",
            body={"question": {"text": "test query", "type": "TEXT"}}
        ):
            results.append(result)
        
        # 验证完成信号被正确处理
        assert "data:[done]" in results
    
 