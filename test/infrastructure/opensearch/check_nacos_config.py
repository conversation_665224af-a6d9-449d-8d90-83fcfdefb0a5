#!/usr/bin/env python3
"""
检查Nacos配置获取是否正常
"""

import sys
import os
from multiprocessing import freeze_support

def main():
    # 添加项目根目录到Python路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

    try:
        from lucas_common_components.nacos.core.nacos_manager import NacosConfigManager
        from src.config.config_model import AIChatAppConfig
        
        print("📡 正在连接Nacos...")
        NacosConfigManager.get_instance().register_config(AIChatAppConfig)
        
        print("📋 正在获取配置...")
        config = AIChatAppConfig.get_instance()
        opensearch_config = config.openSearchConfig
        
        print("✅ Nacos连接成功！")
        print(f"   - Endpoint: {opensearch_config.endpoint}")
        print(f"   - Protocol: {opensearch_config.protocol}")
        print(f"   - App Name: {opensearch_config.app_name}")
        print(f"   - Access Key ID: {opensearch_config.access_key_id[:8]}...")
        
        if all([
            opensearch_config.endpoint,
            opensearch_config.access_key_id,
            opensearch_config.access_key_secret,
            opensearch_config.app_name
        ]):
            print("✅ OpenSearch配置完整")
            return 0
        else:
            print("❌ OpenSearch配置不完整")
            return 1
            
    except Exception as e:
        print(f"❌ Nacos配置获取失败: {e}")
        return 1

if __name__ == '__main__':
    freeze_support()
    sys.exit(main()) 