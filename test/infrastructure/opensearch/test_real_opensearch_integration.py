"""
阿里云OpenSearch真实集成测试

通过Nacos获取配置，测试重构后的OpenSearch客户端

运行方式：
pytest test/infrastructure/opensearch/test_real_opensearch_integration.py -v -s -m integration
"""

import pytest
import asyncio
from lucas_common_components.nacos.core.nacos_manager import NacosConfigManager
from src.config.config_model import AIChatAppConfig
from src.infrastructure.opensearch import Config, AliyunOpenSearchLLMClient


class TestRealOpenSearchIntegration:
    """真实阿里云OpenSearch集成测试"""
    
    @pytest.fixture(scope="class")
    def opensearch_config(self):
        """从Nacos获取OpenSearch配置"""
        try:
            # 注册配置
            NacosConfigManager.get_instance().register_config(AIChatAppConfig)
            
            # 获取配置
            config = AIChatAppConfig.get_instance()
            opensearch_config = config.openSearchConfig
            
            if not all([
                opensearch_config.endpoint,
                opensearch_config.access_key_id,
                opensearch_config.access_key_secret,
                opensearch_config.app_name
            ]):
                pytest.skip("Nacos中缺少完整的OpenSearch配置")
            
            return {
                "config": Config(
                    endpoint=opensearch_config.endpoint,
                    protocol=opensearch_config.protocol,
                    access_key_id=opensearch_config.access_key_id,
                    access_key_secret=opensearch_config.access_key_secret,
                ),
                "app_name": opensearch_config.app_name
            }
        except Exception as e:
            pytest.skip(f"无法从Nacos获取配置: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_search_doc(self, opensearch_config):
        """测试真实的知识搜索"""
        config = opensearch_config["config"]
        app_name = opensearch_config["app_name"]
        
        client = AliyunOpenSearchLLMClient(config)
        
        # 构建测试查询
        body = {
            "question": {
                "text": "什么是人工智能？",
                "type": "TEXT"
            },
            "options": {
                "retrieve": {
                    "doc": {
                        "top_n": 3,
                        "disable": False
                    }
                }
            }
        }
        
        print(f"\n🔍 测试知识搜索...")
        print(f"查询: {body['question']['text']}")
        
        results = []
        async for result in client.searchDoc(app_name, body, use_llm=False):
            results.append(result)
            print(f"📄 搜索结果: {result}")
        
        assert len(results) > 0, "应该返回搜索结果"
        print(f"✅ 知识搜索测试通过，返回 {len(results)} 个结果")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_multi_search_with_llm(self, opensearch_config):
        """测试真实的多路搜索（启用LLM）"""
        config = opensearch_config["config"]
        app_name = opensearch_config["app_name"]
        
        client = AliyunOpenSearchLLMClient(config)
        
        # 构建测试查询
        body = {
            "question": {
                "text": "推荐一些计算机专业的大学",
                "type": "TEXT"
            },
            "options": {
                "retrieve": {
                    "doc": {
                        "top_n": 5,
                        "disable": False
                    }
                }
            }
        }
        
        print(f"\n🤖 测试多路搜索（LLM）...")
        print(f"查询: {body['question']['text']}")
        
        results = []
        async for result in client.multi_search(app_name, body, use_llm=True):
            results.append(result)
            print(f"🎯 LLM结果: {result}")
        
        assert len(results) > 0, "应该返回LLM处理结果"
        print(f"✅ 多路搜索（LLM）测试通过，返回 {len(results)} 个结果")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_stream_multi_search(self, opensearch_config):
        """测试真实的流式多路搜索"""
        config = opensearch_config["config"]
        app_name = opensearch_config["app_name"]
        
        client = AliyunOpenSearchLLMClient(config)
        
        # 构建测试查询
        body = {
            "question": {
                "text": "介绍一下机器学习的基本概念",
                "type": "TEXT"
            },
            "options": {
                "retrieve": {
                    "doc": {
                        "top_n": 3,
                        "disable": False
                    }
                }
            }
        }
        
        print(f"\n🌊 测试流式多路搜索...")
        print(f"查询: {body['question']['text']}")
        
        stream_count = 0
        async for stream_data in client.stream_multi_search(app_name, body, use_llm=True):
            stream_count += 1
            print(f"📡 流式数据 {stream_count}: {stream_data[:100]}...")
            
            # 限制输出数量，避免测试时间过长
            if stream_count >= 10:
                break
        
        assert stream_count > 0, "应该返回流式数据"
        print(f"✅ 流式多路搜索测试通过，接收到 {stream_count} 个流式数据包")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_stream_incremental_search(self, opensearch_config):
        """测试真实的流式增量搜索（打字机效果）"""
        config = opensearch_config["config"]
        app_name = opensearch_config["app_name"]
        
        client = AliyunOpenSearchLLMClient(config)
        
        # 构建测试查询
        body = {
            "question": {
                "text": "什么是深度学习？",
                "type": "TEXT"
            },
            "options": {
                "retrieve": {
                    "doc": {
                        "top_n": 3,
                        "disable": False
                    }
                }
            }
        }
        
        print(f"\n⌨️ 测试流式增量搜索（打字机效果）...")
        print(f"查询: {body['question']['text']}")
        print(f"回答: ", end="", flush=True)
        
        incremental_count = 0
        full_answer = ""
        
        async for increment in client.stream_incremental_search(app_name, body, use_llm=True):
            if increment == "data:[done]":
                print(f"\n🏁 流式输出完成")
                break
            
            if increment:
                incremental_count += 1
                full_answer += increment
                print(increment, end="", flush=True)
                
                # 限制输出，避免测试时间过长
                if incremental_count >= 50:
                    print(f"\n⏸️ 达到测试限制，停止接收")
                    break
        
        print(f"\n")
        assert incremental_count > 0, "应该返回增量内容"
        assert len(full_answer) > 0, "应该组成完整答案"
        print(f"✅ 流式增量搜索测试通过")
        print(f"📊 接收到 {incremental_count} 个增量片段")
        print(f"📝 完整答案长度: {len(full_answer)} 字符")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_error_handling(self, opensearch_config):
        """测试真实环境下的错误处理"""
        config = opensearch_config["config"]
        
        client = AliyunOpenSearchLLMClient(config)
        
        # 使用不存在的app_name测试错误处理
        fake_app_name = "non_existent_app_12345"
        
        body = {
            "question": {
                "text": "测试错误处理",
                "type": "TEXT"
            }
        }
        
        print(f"\n❌ 测试错误处理...")
        print(f"使用不存在的应用: {fake_app_name}")
        
        error_caught = False
        async for result in client.searchDoc(fake_app_name, body):
            if isinstance(result, dict) and "error" in result:
                error_caught = True
                print(f"🚨 捕获到错误: {result['error']}")
                break
        
        # 注意：这里不一定会抛出异常，可能返回错误结果
        print(f"✅ 错误处理测试完成")


# 运行说明
if __name__ == "__main__":
    print("""
🧪 阿里云OpenSearch真实集成测试

📋 运行前准备：
1. 确保Nacos配置中心已配置OpenSearch相关参数：
   - openSearchConfig.endpoint
   - openSearchConfig.access_key_id
   - openSearchConfig.access_key_secret
   - openSearchConfig.app_name
   - openSearchConfig.protocol

2. 运行测试：
   pytest test/infrastructure/opensearch/test_real_opensearch_integration.py -v -s -m integration

⚠️ 注意：
- 此测试会产生真实的API调用费用
- 请确保OpenSearch应用已正确配置
- 建议在测试环境中运行
- 测试会自动从Nacos获取配置，无需手动设置环境变量
""") 