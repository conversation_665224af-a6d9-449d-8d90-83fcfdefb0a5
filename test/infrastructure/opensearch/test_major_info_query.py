import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from src.exe.query.MajorInfoQueryExe import MajorInfoQueryExe


@pytest.fixture
def mock_opensearch_client():
    """Fixture to create a mock OpenSearchLLMClient"""
    with patch("src.exe.query.MajorInfoQueryExe.OpenSearchLLMClient") as mock_client:
        # Create a mock instance
        mock_instance = MagicMock()
        # Create an async mock for multi_search
        mock_instance.multi_search = AsyncMock()
        # Configure the mock to return our test data
        mock_instance.multi_search.return_value = [
            {"data": [{"answer": "Test answer 1"}, {"answer": "Test answer 2"}]}
        ]
        # Make the constructor return our mock instance
        mock_client.return_value = mock_instance
        yield mock_instance


@pytest.mark.asyncio
async def test_search_major_info_success(mock_opensearch_client):
    """Test successful major info search"""
    query_text = "测试查询"
    results = []

    async for result in MajorInfoQueryExe.search_major_info(query_text):
        results.append(result)

    # Verify the results
    assert len(results) == 2
    assert results[0] == "Test answer 1"
    assert results[1] == "Test answer 2"

    # Verify the client was called with correct parameters
    mock_opensearch_client.multi_search.assert_called_once()
    call_args = mock_opensearch_client.multi_search.call_args
    assert call_args[1]["app_name"] == "test"
    assert call_args[1]["body"]["question"]["text"] == query_text


@pytest.mark.asyncio
async def test_search_major_info_empty_response(mock_opensearch_client):
    """Test search with empty response"""
    mock_opensearch_client.multi_search.return_value = [{"data": []}]

    query_text = "测试查询"
    results = []

    async for result in MajorInfoQueryExe.search_major_info(query_text):
        results.append(result)

    assert len(results) == 0


@pytest.mark.asyncio
async def test_search_major_info_error(mock_opensearch_client):
    """Test search with error"""
    mock_opensearch_client.multi_search.side_effect = Exception("Test error")

    query_text = "测试查询"
    results = []

    async for result in MajorInfoQueryExe.search_major_info(query_text):
        results.append(result)

    assert len(results) == 1
    assert results[0] is None
