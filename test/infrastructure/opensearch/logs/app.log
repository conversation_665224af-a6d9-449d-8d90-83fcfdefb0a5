2025-05-28 13:09:21.069 | ERROR   | MajorInfoQueryExe:92 | [1927593009507991552] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 13:09:21.268 | ERROR   | MajorInfoQueryExe:92 | [1927593010338463745] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 13:09:21.290 | ERROR   | MajorInfoQueryExe:92 | [1927593010430738433] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 13:09:56.838 | ERROR   | MajorInfoQueryExe:92 | [1927593159529857025] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 13:10:20.997 | ERROR   | MajorInfoQueryExe:92 | [1927593260864241664] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 13:10:21.234 | ERROR   | MajorInfoQueryExe:92 | [1927593261854097409] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 13:10:21.243 | ERROR   | MajorInfoQueryExe:92 | [1927593261891846145] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 21:28:10.163 | ERROR   | MajorInfoQueryExe:92 | [1927718541226672128] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 21:28:10.343 | ERROR   | MajorInfoQueryExe:92 | [1927718541977452545] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
2025-05-28 21:28:10.360 | ERROR   | MajorInfoQueryExe:92 | [1927718542048755713] | Exception during major search: 'async for' requires an object with __aiter__ method, got coroutine
Traceback (most recent call last):
  File "D:\code\PyCharm\yai-chat\src\exe\query\MajorInfoQueryExe.py", line 80, in search_major_info
    async for response in client.multi_search(app_name="test", body=body):
                          ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'async for' requires an object with __aiter__ method, got coroutine
