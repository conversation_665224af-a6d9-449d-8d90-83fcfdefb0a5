import asyncio
import os
import sys
import pytest
from lucas_common_components.logging.logger import setup_logger
import warnings

from lucas_common_components.nacos.core.nacos_manager import NacosConfigManager

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from tortoise import Tortoise
from datetime import datetime
import json

from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.infrastructure.db.db_connection import DatabaseConnection
from src.config.config_model import AIChatAppConfig

# 配置日志
logger = setup_logger(__name__)

# 忽略特定的警告
warnings.filterwarnings(
    "ignore", message="Exception ignored in.*_DeleteDummyThreadOnDel.__del__"
)

# 配置 pytest 的异步测试
pytestmark = pytest.mark.asyncio(scope="function")


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    try:
        # 取消所有待处理的任务
        pending = asyncio.all_tasks(loop)
        for task in pending:
            task.cancel()
        # 运行事件循环直到所有任务都被取消
        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        # 关闭事件循环
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
    except Exception:
        pass


@pytest.fixture
def sample_conversation_data():
    return {
        "id": int(datetime.now().timestamp() * 1000),  # Use timestamp as unique ID
        "title": "Test Conversation",
        "uni_user_id": 123,
        "created_at": datetime.now(),
        "context": json.dumps({"text": "Test context"}),  # 添加必需的context字段
    }


async def init_db():
    """初始化 Tortoise ORM 数据库连接池"""
    try:
        NacosConfigManager.get_instance().register_config(AIChatAppConfig)
        # 获取 Tortoise ORM 所需的配置
        config = DatabaseConnection.get_tortoise_config()

        await Tortoise.init(config=config)
        logger.info("Tortoise ORM 初始化成功")

    except Exception as e:
        logger.error(f"Tortoise ORM 初始化失败: {e}")
        raise


@pytest.mark.asyncio
async def test_create_conversation(sample_conversation_data):
    """初始化数据库连接"""
    await init_db()
    data = sample_conversation_data
    conversation = await AIConversationRepository.create(data)
    assert conversation is not None
    assert conversation.title == data["title"]
    assert conversation.uni_user_id == data["uni_user_id"]
    await conversation.delete()


@pytest.mark.asyncio
async def test_get_conversation_by_id(sample_conversation_data):
    data = sample_conversation_data
    conversation = await AIConversationRepository.create(data)
    retrieved = await AIConversationRepository.get_by_id(conversation.id)
    assert retrieved is not None
    assert retrieved.id == conversation.id
    await conversation.delete()


@pytest.mark.asyncio
async def test_update_conversation(sample_conversation_data):
    data = sample_conversation_data
    conversation = await AIConversationRepository.create(data)
    update_data = {"title": "Updated Title"}
    updated = await AIConversationRepository.update(conversation.id, update_data)
    assert updated is not None
    assert updated.title == "Updated Title"
    await conversation.delete()


@pytest.mark.asyncio
async def test_delete_conversation(sample_conversation_data):
    """初始化数据库连接"""

    data = sample_conversation_data
    conversation = await AIConversationRepository.create(data)
    deleted_count = await AIConversationRepository.delete(conversation.id)
    assert deleted_count == 1
    retrieved = await AIConversationRepository.get_by_id(conversation.id)
    assert retrieved is None


@pytest.mark.asyncio
async def test_get_all_conversations(sample_conversation_data):
    """初始化数据库连接"""

    data = sample_conversation_data
    conversation1 = await AIConversationRepository.create(data)
    conversation2 = await AIConversationRepository.create(
        {**data, "id": 123456790, "title": "Another Conversation"}  # 使用不同的ID
    )
    conversations = await AIConversationRepository.get_all()
    assert len(conversations) >= 2
    await conversation1.delete()
    await conversation2.delete()


@pytest.mark.asyncio
async def test_get_conversations_by_title(sample_conversation_data):
    """初始化数据库连接"""

    data = sample_conversation_data
    conversation = await AIConversationRepository.create(data)
    conversations = await AIConversationRepository.get_by_title(data["title"])
    assert any(conv.id == conversation.id for conv in conversations)
    await conversation.delete()


@pytest.mark.asyncio
async def test_get_conversations_by_user_id(sample_conversation_data):
    """初始化数据库连接"""

    data = sample_conversation_data
    conversation = await AIConversationRepository.create(data)
    conversations = await AIConversationRepository.get_by_user_id(data["uni_user_id"])
    assert any(conv.id == conversation.id for conv in conversations)
    await conversation.delete()
