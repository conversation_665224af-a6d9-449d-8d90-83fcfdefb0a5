import asyncio
import os
import sys

import pytest
from lucas_common_components.logging.logger import setup_logger
from lucas_common_components.trace.trace_context import sf

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from tortoise import Tortoise
from datetime import datetime

from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository
from src.infrastructure.db.db_connection import DatabaseConnection
from src.config.config_model import AIChatAppConfig
from lucas_common_components.nacos.core.nacos_manager import NacosConfigManager

# 配置日志
logger = setup_logger(__name__)

# 配置 pytest 的异步测试
pytestmark = pytest.mark.asyncio(scope="function")


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    try:
        # 取消所有待处理的任务
        pending = asyncio.all_tasks(loop)
        for task in pending:
            task.cancel()
        # 运行事件循环直到所有任务都被取消
        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        # 关闭事件循环
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
    except Exception:
        pass


async def init_test_db():
    """初始化 Tortoise ORM 数据库连接池"""
    try:
        NacosConfigManager.get_instance().register_config(AIChatAppConfig)
        # 获取 Tortoise ORM 所需的配置
        config = DatabaseConnection.get_tortoise_config()

        await Tortoise.init(config=config)
        logger.info("Tortoise ORM 初始化成功")

    except Exception as e:
        logger.error(f"Tortoise ORM 初始化失败: {e}")
        raise


@pytest.fixture
def sample_message_data():
    return {
        "id": int(sf.generate()),
        "conversation_id": 123456789,
        "role": "user",
        "content": {"text": "Hello, AI!"},
        "created_at": datetime.now(),
    }


@pytest.mark.asyncio
async def test_create_message(sample_message_data):
    """测试创建消息"""
    await init_test_db()
    message = await AIMessageRepository.create(sample_message_data)
    assert message is not None
    assert message.conversation_id == sample_message_data["conversation_id"]
    assert message.role == sample_message_data["role"]
    assert message.content == sample_message_data["content"]
    await message.delete()


@pytest.mark.asyncio
async def test_get_message_by_id(sample_message_data):
    """测试通过ID获取消息"""

    message = await AIMessageRepository.create(sample_message_data)
    retrieved = await AIMessageRepository.get_by_id(message.id)
    assert retrieved is not None
    assert retrieved.id == message.id
    assert retrieved.content == sample_message_data["content"]
    await message.delete()


@pytest.mark.asyncio
async def test_update_message(sample_message_data):
    """测试更新消息"""
    message = await AIMessageRepository.create(sample_message_data)
    update_data = {"content": {"text": "Updated content"}}
    updated = await AIMessageRepository.update(message.id, update_data)
    assert updated is not None
    assert updated.content == update_data["content"]
    assert updated.role == sample_message_data["role"]
    await message.delete()


@pytest.mark.asyncio
async def test_delete_message(sample_message_data):
    """测试删除消息"""
    message = await AIMessageRepository.create(sample_message_data)
    deleted_count = await AIMessageRepository.delete(message.id)
    assert deleted_count == 1
    retrieved = await AIMessageRepository.get_by_id(message.id)
    assert retrieved is None


@pytest.mark.asyncio
async def test_get_all_messages(sample_message_data):
    """测试获取所有消息"""
    message1 = await AIMessageRepository.create(sample_message_data)
    message2 = await AIMessageRepository.create(
        {**sample_message_data, "content": {"text": "Second message"}}
    )
    messages = await AIMessageRepository.get_all()
    assert len(messages) >= 2
    await message1.delete()
    await message2.delete()


@pytest.mark.asyncio
async def test_get_messages_by_conversation_id(sample_message_data):
    """测试通过会话ID获取消息"""
    message1 = await AIMessageRepository.create(sample_message_data)
    message2 = await AIMessageRepository.create(
        {
            **sample_message_data,
            "conversation_id": 123456790,
            "content": {"text": "Different conversation"},
        }
    )
    conversation_messages = await AIMessageRepository.get_by_conversation_id(
        sample_message_data["conversation_id"]
    )
    assert len(conversation_messages) >= 1
    assert all(
        msg.conversation_id == sample_message_data["conversation_id"]
        for msg in conversation_messages
    )
    await message1.delete()
    await message2.delete()


@pytest.mark.asyncio
async def test_get_messages_by_role(sample_message_data):
    """测试通过角色获取消息"""
    user_message = await AIMessageRepository.create(sample_message_data)
    assistant_message = await AIMessageRepository.create(
        {
            **sample_message_data,
            "role": "assistant",
            "content": {"text": "I am an AI assistant"},
        }
    )
    user_messages = await AIMessageRepository.get_by_role("user")
    assistant_messages = await AIMessageRepository.get_by_role("assistant")
    assert len(user_messages) >= 1
    assert len(assistant_messages) >= 1
    assert all(msg.role == "user" for msg in user_messages)
    assert all(msg.role == "assistant" for msg in assistant_messages)
    await user_message.delete()
    await assistant_message.delete()


@pytest.mark.asyncio
async def test_message_creation_time(sample_message_data):
    """测试消息创建时间"""
    message = await AIMessageRepository.create(sample_message_data)
    assert message.created_at is not None
    assert isinstance(message.created_at, datetime)
    await message.delete()


@pytest.mark.asyncio
async def test_bulk_create_messages():
    """测试批量创建消息"""
    messages_data = [
        {
            "conversation_id": 123456789,
            "role": "user",
            "content": {"text": f"Message {i}"},
            "created_at": datetime.now(),
        }
        for i in range(3)
    ]
    messages = await AIMessageRepository.bulk_create(messages_data)
    assert len(messages) == 3
    for message in messages:
        await message.delete()


@pytest.mark.asyncio
async def test_get_nonexistent_message():
    """测试获取不存在的消息"""
    message = await AIMessageRepository.get_by_id(999999)
    assert message is None


@pytest.mark.asyncio
async def test_update_nonexistent_message():
    """测试更新不存在的消息"""
    updated = await AIMessageRepository.update(
        999999, {"content": {"text": "New content"}}
    )
    assert updated is None
