import requests
import pandas as pd
import json
import time
import math
import re  # For parsing CollegeTags

# --- 配置参数 ---
APPKEY = "AUTNWQ6XGR3SDWAR3WPKF8WM37RV9K9D"
BASE_URL = "https://api.gugudata.com/location/college"
PAGE_SIZE = 20  # API文档说明最大值为20
KEYWORDS = ""  # 空字符串表示获取所有数据，您可以按需修改
OUTPUT_EXCEL_FILE = "guguschool_data.xlsx"

# --- PostgreSQL 表字段名 (与之前创建的表对应) ---
# 这是为了确保DataFrame的列名与数据库表字段名一致
# data_id, school_uuid, college_name, province, city, district, coordinate,
# college_type, is_985, is_211, is_dual_class, college_category, college_tags,
# edu_level, college_property, college_code, ranking, ranking_in_category,
# web_site, call_number, email, address, branch_list, cover_image, intro,
# expenses, old_name, short_name, major_list, is_deleted, fetched_at


def parse_college_tags(tags_str):
    """
    将API返回的CollegeTags字符串（如 "[tag1, tag2, tag3]"）
    转换为PostgreSQL TEXT[] 类型的字面量字符串（如 "{tag1,tag2,tag3}"）。
    如果标签本身包含逗号或引号，需要更复杂的处理，但这里假设标签是简单的。
    """
    if not tags_str or tags_str == "[]":
        return "{}"
    try:
        # 尝试移除首尾的方括号，然后按逗号分割
        # 使用正则表达式确保正确处理各种空格情况并去除空字符串
        tags_list = [
            tag.strip()
            for tag in re.split(r"\s*,\s*", tags_str.strip("[]"))
            if tag.strip()
        ]
        if not tags_list:
            return "{}"
        # 为每个标签加上双引号，以处理可能存在的特殊字符或空格
        # PostgreSQL数组字面量中，元素若含逗号、花括号、引号、反斜杠等需用双引号包围
        formatted_tags = [
            f'"{tag.replace('"', '""')}"' for tag in tags_list
        ]  # 转义标签内的双引号
        return "{" + ",".join(formatted_tags) + "}"
    except Exception:
        # 如果解析失败，返回一个空数组的表示或原始字符串（取决于你的偏好）
        # 为了安全导入，返回空数组的字面量
        return "{}"


def fetch_page_data(page_index, pagesize, keywords):
    """获取指定页的数据"""
    params = {
        "appkey": APPKEY,
        "keywords": keywords,
        "pagesize": pagesize,
        "pageindex": page_index,
        "keywordstrict": "false",  # 默认为false
        # 其他筛选参数可以按需添加
        # "collegecategory": "",
        # "collegetype": "",
        # "is985": "",
        # "is211": "",
        # "isdualclass": "",
        # "edulevel": "",
        # "collegeproperty": "",
    }
    try:
        response = requests.get(BASE_URL, params=params, timeout=30)  # 增加超时
        response.raise_for_status()  # 如果HTTP请求返回了不成功的状态码，则抛出HTTPError异常
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求API失败 (Page {page_index}): {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"解析API响应JSON失败 (Page {page_index}): {e}")
        print(f"原始响应文本: {response.text[:500]}...")  # 打印部分响应内容
        return None


def process_api_record(api_record, fetched_at_str):
    """将单条API记录转换为字典，以匹配PG表结构"""
    processed_record = {
        "data_id": api_record.get("DataId"),
        "school_uuid": api_record.get("SchoolUUID"),
        "college_name": api_record.get("CollegeName"),
        "province": api_record.get("Province"),
        "city": api_record.get("City"),
        "district": api_record.get("District"),
        "coordinate": api_record.get("Coordinate"),
        "college_type": api_record.get("CollegeType"),
        "is_985": api_record.get("Is985"),
        "is_211": api_record.get("Is211"),
        "is_dual_class": api_record.get("IsDualClass"),
        "college_category": api_record.get("CollegeCategory"),
        "college_tags": parse_college_tags(
            api_record.get("CollegeTags", "")
        ),  # 处理CollegeTags
        "edu_level": api_record.get("EduLevel"),
        "college_property": api_record.get("CollegeProperty"),
        "college_code": api_record.get("CollegeCode"),
        "ranking": api_record.get("Ranking"),
        "ranking_in_category": api_record.get("RankingInCategory"),
        "web_site": api_record.get("WebSite"),
        "call_number": api_record.get("CallNumber"),
        "email": api_record.get("Email"),
        "address": api_record.get("Address"),
        "branch_list": json.dumps(api_record.get("BranchList", [])),  # 存为JSON字符串
        "cover_image": api_record.get("CoverImage"),
        "intro": api_record.get("Intro"),
        "expenses": api_record.get("Expenses"),
        "old_name": api_record.get("OldName"),
        "short_name": api_record.get("ShortName"),
        "major_list": json.dumps(api_record.get("MajorList", [])),  # 存为JSON字符串
        "is_deleted": api_record.get("IsDeleted", False),
        "fetched_at": fetched_at_str,  # 来自DataStatus.ResponseDateTime
    }
    return processed_record


def main():
    all_schools_data = []
    current_page = 1
    total_pages = 1  # 初始假设至少1页

    print("开始从GuguData API获取学校数据...")

    # 首先获取第一页数据以确定总数和总页数
    print(f"正在获取第 {current_page} 页数据...")
    initial_response = fetch_page_data(current_page, PAGE_SIZE, KEYWORDS)

    if (
        not initial_response
        or initial_response.get("DataStatus", {}).get("StatusCode") != 100
    ):
        print("获取初始数据失败或API返回错误状态码，脚本终止。")
        if initial_response:
            print(f"API状态: {initial_response.get('DataStatus')}")
        return

    data_total_count = initial_response["DataStatus"].get("DataTotalCount", 0)
    if data_total_count == 0:
        print("API返回总数据量为0，没有数据可获取。")
        return

    total_pages = math.ceil(data_total_count / PAGE_SIZE)
    print(f"总共 {data_total_count} 条数据，分为 {total_pages} 页。")

    # 处理第一页的数据
    response_datetime = initial_response["DataStatus"].get("ResponseDateTime")
    for school_raw in initial_response.get("Data", []):
        all_schools_data.append(process_api_record(school_raw, response_datetime))

    time.sleep(0.5)  # 礼貌性等待

    # 获取剩余页的数据
    for page_num in range(current_page + 1, total_pages + 1):
        print(f"正在获取第 {page_num} / {total_pages} 页数据...")
        page_data_json = fetch_page_data(page_num, PAGE_SIZE, KEYWORDS)

        if (
            page_data_json
            and page_data_json.get("DataStatus", {}).get("StatusCode") == 100
        ):
            response_datetime = page_data_json["DataStatus"].get("ResponseDateTime")
            schools_on_page = page_data_json.get("Data", [])
            if not schools_on_page:
                print(f"警告: 第 {page_num} 页没有返回数据，尽管状态码为100。")
            for school_raw in schools_on_page:
                all_schools_data.append(
                    process_api_record(school_raw, response_datetime)
                )
        else:
            print(f"获取第 {page_num} 页数据失败或API返回错误，跳过此页。")
            if page_data_json:
                print(f"API状态: {page_data_json.get('DataStatus')}")

        time.sleep(0.5)  # 礼貌性等待，避免请求过于频繁

    if not all_schools_data:
        print("未能获取到任何学校数据。")
        return

    print(f"\n成功获取 {len(all_schools_data)} 条学校数据。")

    # 创建Pandas DataFrame
    df = pd.DataFrame(all_schools_data)

    # 确保列顺序与PG表（或期望的Excel顺序）一致 (可选，但有助于检查)
    column_order = [
        "data_id",
        "school_uuid",
        "college_name",
        "province",
        "city",
        "district",
        "coordinate",
        "college_type",
        "is_985",
        "is_211",
        "is_dual_class",
        "college_category",
        "college_tags",
        "edu_level",
        "college_property",
        "college_code",
        "ranking",
        "ranking_in_category",
        "web_site",
        "call_number",
        "email",
        "address",
        "branch_list",
        "cover_image",
        "intro",
        "expenses",
        "old_name",
        "short_name",
        "major_list",
        "is_deleted",
        "fetched_at",
    ]
    # 仅保留实际获取到的列，并按指定顺序排列，防止某些列完全没数据导致KeyError
    df = df[[col for col in column_order if col in df.columns]]

    # 保存到Excel
    try:
        df.to_excel(OUTPUT_EXCEL_FILE, index=False, sheet_name="guguschool")
        print(f"数据已成功保存到 {OUTPUT_EXCEL_FILE}")
    except Exception as e:
        print(f"保存到Excel文件失败: {e}")


if __name__ == "__main__":
    main()
