import asyncio
import os
import sys
import warnings

import pytest
from lucas_common_components.logging.logger import setup_logger
from lucas_common_components.nacos.core.nacos_manager import (
    NacosConfigManager,
)

from src.app.zhiyuan.models.model import UserInfoDTO

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))


from src.infrastructure.rpc.account_client import AccountClient
from src.config.config_model import AIChatAppConfig

# 配置日志
logger = setup_logger(__name__)

# 忽略特定的警告
warnings.filterwarnings(
    "ignore", message="Exception ignored in.*_DeleteDummyThreadOnDel.__del__"
)

# 配置 pytest 的异步测试
pytestmark = pytest.mark.asyncio(scope="function")


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    try:
        # 取消所有待处理的任务
        pending = asyncio.all_tasks(loop)
        for task in pending:
            task.cancel()
        # 运行事件循环直到所有任务都被取消
        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        # 关闭事件循环
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.close()
    except Exception:
        pass


@pytest.mark.asyncio
async def test_get_profile_success():
    """测试成功获取用户信息"""

    uni_userid = "14"
    # 准备模拟响应
    NacosConfigManager.get_instance().register_config(AIChatAppConfig)
    # 执行测试
    client = AccountClient()
    result = await client.get_profile(uni_userid)

    # 验证结果
    assert result is not None
    assert isinstance(result, UserInfoDTO)
