import asyncio
from pathlib import Path
from src.exe.cmd.AdmissionPolicyCommandExe import AdmissionPolicyCommandExe


async def main():
    # Get the current directory
    current_dir = Path(__file__).parent.parent.parent

    # Path to the document file
    file_path = (
        current_dir
        / "src"
        / "txt"
        / "山东省2024年普通高等学校考试 招生（夏季高考）工作实施办法"
    )

    # Title for the document
    key = "山东省"

    # Import the document
    success = await AdmissionPolicyCommandExe.import_document(str(file_path), key)

    if success:
        print("Document imported successfully!")
    else:
        print("Failed to import document.")


if __name__ == "__main__":
    asyncio.run(main())
