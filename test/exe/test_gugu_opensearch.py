#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GuguData OpenSearch集成测试脚本
用于测试从GuguData获取数据并推送到OpenSearch的功能
"""

import asyncio
import logging
import sys
import os
import time

from lucas_common_components.logging import setup_logger

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.exe.command.GuguDataOpenSearchCommandExe import GuguDataOpenSearchCommandExe

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"gugu_opensearch_test_{int(time.time())}.log"),
    ],
)

logger = setup_logger(__name__)


async def test_fetch_and_push_single_school():
    """
    测试采集单个学校的数据并推送到OpenSearch
    """
    logger.info("开始测试采集单个学校数据并推送到OpenSearch")

    # OpenSearch配置
    opensearch_config = {
        "endpoint": "opensearch-cn-shanghai.aliyuncs.com",
        "protocol": "HTTP",
        "access_key_id": "LTAI5tNSguCAeUvnXK4N8aNQ",
        "access_key_secret": "******************************",
        "app_name": "test",
    }

    # 创建执行器实例
    exe = GuguDataOpenSearchCommandExe(opensearch_config)

    # 测试采集和推送
    school_name = "清华大学"
    year = 2023

    start_time = time.time()
    result = await exe.fetch_school_and_push(school_name=school_name, year=year)
    end_time = time.time()

    # 打印结果
    logger.info(
        f"测试结果: 成功={result.get('success', False)}, 消息={result.get('message', '')}"
    )
    logger.info(f"处理时间: {end_time - start_time:.2f}秒")

    if result.get("success"):
        fetch_result = result.get("fetch_result", {})
        push_result = result.get("push_result", {})

        logger.info(
            f"获取数据: 总数={fetch_result.get('total_count', 0)}, 实际获取数={fetch_result.get('fetched_count', 0)}"
        )
        logger.info(f"推送数据: 总数={push_result.get('total_count', 0)}")

    return result


async def run_tests():
    """
    运行所有测试
    """
    tests = [
        test_fetch_and_push_single_school(),
    ]

    results = []
    for test in tests:
        try:
            result = await test
            results.append(result)
        except Exception as e:
            logger.exception(f"测试执行异常: {str(e)}")

    return results


if __name__ == "__main__":
    try:
        logger.info("开始GuguData OpenSearch集成测试")

        # 运行测试
        asyncio.run(run_tests())

        logger.info("GuguData OpenSearch集成测试完成")
    except Exception as e:
        logger.exception(f"测试脚本执行异常: {str(e)}")
        sys.exit(1)
