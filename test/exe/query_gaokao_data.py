#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
This script provides functions to query Gaokao score segment data from a Neo4j graph database.
"""

import pandas as pd
from neo4j import GraphDatabase
import json
import traceback

# Neo4j 连接配置 - 与excelgdb.py保持一致
neo4j_uri = "bolt://gds-2ze79n4u801cdnln149870.graphdb.rds.aliyuncs.com:8182"
neo4j_user = "test_admin"
neo4j_password = "EaV4u372ax"


class GaokaoScoreQuery:
    def __init__(
        self,
        uri: str = neo4j_uri,
        user: str = neo4j_user,
        password: str = neo4j_password,
    ):
        """Initialize the connection to Neo4j."""
        print(f"Connecting to Neo4j at {uri}")
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        # 测试连接
        try:
            with self.driver.session() as session:
                result = session.run("MATCH (n) RETURN count(n) as count")
                count = result.data()[0]["count"] if result.data() else 0
                print(f"Connection successful. Total nodes in database: {count}")
        except Exception as e:
            print(f"Error connecting to Neo4j: {e}")
            traceback.print_exc()

    def close(self):
        """Close the Neo4j driver connection."""
        self.driver.close()

    def _run_query(self, query, params=None):
        """Execute a query and return results."""
        try:
            with self.driver.session() as session:
                print(f"Running query: {query}")
                print(f"With parameters: {params}")
                result = session.run(query, params or {})
                data = result.data()
                print(f"Query returned {len(data)} records")
                return data
        except Exception as e:
            print(f"Error executing query: {e}")
            traceback.print_exc()
            return []

    def query_by_score_range(
        self,
        min_score: int,
        max_score: int,
        year: int = None,
        province: str = None,
        subject: str = None,
    ):
        """
        Query Gaokao score segments by score range.

        Args:
            min_score: Minimum score (inclusive)
            max_score: Maximum score (inclusive)
            year: Optional filter by year
            province: Optional filter by province
            subject: Optional filter by subject

        Returns:
            List of dictionaries with node data
        """
        query = """
        MATCH (s:GaokaoScoreSegment)
        WHERE toInteger(s.score) >= $min_score AND toInteger(s.score) <= $max_score
        """

        params = {"min_score": min_score, "max_score": max_score}

        # Add optional filters
        if year is not None:
            query += " AND s.year = $year"
            params["year"] = year

        if province is not None:
            query += " AND s.province = $province"
            params["province"] = province

        if subject is not None:
            query += " AND s.subject = $subject"
            params["subject"] = subject

        query += " RETURN s ORDER BY toInteger(s.score) DESC"

        result = self._run_query(query, params)
        return [record["s"] for record in result]

    def query_by_rank_range(
        self,
        min_rank: int,
        max_rank: int,
        year: int = None,
        province: str = None,
        subject: str = None,
    ):
        """
        Query Gaokao score segments by rank range.

        Args:
            min_rank: Minimum rank (inclusive)
            max_rank: Maximum rank (inclusive)
            year: Optional filter by year
            province: Optional filter by province
            subject: Optional filter by subject

        Returns:
            List of dictionaries with node data
        """
        query = """
        MATCH (s:GaokaoScoreSegment)
        WHERE toInteger(s.rank) >= $min_rank AND toInteger(s.rank) <= $max_rank
        """

        params = {"min_rank": min_rank, "max_rank": max_rank}

        # Add optional filters
        if year is not None:
            query += " AND s.year = $year"
            params["year"] = year

        if province is not None:
            query += " AND s.province = $province"
            params["province"] = province

        if subject is not None:
            query += " AND s.subject = $subject"
            params["subject"] = subject

        query += " RETURN s ORDER BY toInteger(s.rank)"

        result = self._run_query(query, params)
        return [record["s"] for record in result if "s" in record]

    def get_available_provinces(self):
        """Get a list of all available provinces in the database."""
        query = """
        MATCH (s:GaokaoScoreSegment)
        RETURN DISTINCT s.province AS province
        ORDER BY province
        """
        result = self._run_query(query)
        return [record["province"] for record in result if "province" in record]

    def get_available_years(self):
        """Get a list of all available years in the database."""
        query = """
        MATCH (s:GaokaoScoreSegment)
        RETURN DISTINCT s.year AS year
        ORDER BY year DESC
        """
        result = self._run_query(query)
        return [record["year"] for record in result if "year" in record]

    def get_available_subjects(self):
        """Get a list of all available subjects in the database."""
        query = """
        MATCH (s:GaokaoScoreSegment)
        RETURN DISTINCT s.subject AS subject
        ORDER BY subject
        """
        result = self._run_query(query)
        return [record["subject"] for record in result if "subject" in record]

    def list_all_nodes(self, limit=10):
        """List all GaokaoScoreSegment nodes, limited to a specified number."""
        query = """
        MATCH (s:GaokaoScoreSegment)
        RETURN s
        LIMIT $limit
        """
        result = self._run_query(query, {"limit": limit})
        return [record["s"] for record in result if "s" in record]

    def get_score_by_rank(
        self,
        rank: int,
        year: int = None,
        province: str = None,
        subject: str = "全体",
    ):
        """
        Get the score corresponding to a specific rank.

        Args:
            rank: The rank to find the score for
            year: Optional filter by year
            province: Optional filter by province
            subject: Filter by subject (default: '全体')

        Returns:
            Dictionary with score information or None if not found
        """
        query = """
        MATCH (s:GaokaoScoreSegment)
        WHERE toInteger(s.rank) >= $rank
        """

        params = {"rank": rank}

        # Add filters
        if year is not None:
            query += " AND s.year = $year"
            params["year"] = year

        if province is not None:
            query += " AND s.province = $province"
            params["province"] = province

        query += " AND s.subject = $subject"
        params["subject"] = subject

        query += " RETURN s ORDER BY toInteger(s.rank) ASC LIMIT 1"

        result = self._run_query(query, params)
        if result and len(result) > 0 and "s" in result[0]:
            return result[0]["s"]
        return None

    def get_rank_by_score(
        self,
        score: int,
        year: int = None,
        province: str = None,
        subject: str = "全体",
    ):
        """
        Get the rank corresponding to a specific score.

        Args:
            score: The score to find the rank for
            year: Optional filter by year
            province: Optional filter by province
            subject: Filter by subject (default: '全体')

        Returns:
            Dictionary with rank information or None if not found
        """
        query = """
        MATCH (s:GaokaoScoreSegment)
        WHERE toInteger(s.score) = $score
        """

        params = {"score": score}

        # Add filters
        if year is not None:
            query += " AND s.year = $year"
            params["year"] = year

        if province is not None:
            query += " AND s.province = $province"
            params["province"] = province

        query += " AND s.subject = $subject"
        params["subject"] = subject

        query += " RETURN s"

        result = self._run_query(query, params)
        if result and len(result) > 0 and "s" in result[0]:
            return result[0]["s"]
        return None

    def export_to_csv(
        self,
        file_path: str,
        year: int = None,
        province: str = None,
        subject: str = None,
    ):
        """
        Export query results to a CSV file.

        Args:
            file_path: Path to save the CSV file
            year: Optional filter by year
            province: Optional filter by province
            subject: Optional filter by subject
        """
        query = "MATCH (s:GaokaoScoreSegment) WHERE 1=1"
        params = {}

        # Add filters
        if year is not None:
            query += " AND s.year = $year"
            params["year"] = year

        if province is not None:
            query += " AND s.province = $province"
            params["province"] = province

        if subject is not None:
            query += " AND s.subject = $subject"
            params["subject"] = subject

        query += (
            " RETURN s.year AS year, s.province AS province, s.subject AS subject, "
            + "s.score AS score, s.tiescore AS tiescore, s.rank AS rank "
            + "ORDER BY s.year DESC, s.province, s.subject, toInteger(s.score) DESC"
        )

        result = self._run_query(query, params)
        if result:
            df = pd.DataFrame(result)
            df.to_csv(file_path, index=False)
            print(f"Exported {len(df)} records to {file_path}")
            return True
        else:
            print("No data found matching the criteria.")
            return False


def main():
    """Main function to demonstrate usage."""
    query = GaokaoScoreQuery()

    try:
        # Display the menu
        while True:
            print("\n===== Gaokao Score Query Tool =====")
            print("1. Show available years")
            print("2. Show available provinces")
            print("3. Show available subjects")
            print("4. Query by score range")
            print("5. Query by rank range")
            print("6. Get score by rank")
            print("7. Get rank by score")
            print("8. Export data to CSV")
            print("9. List all nodes (limited)")
            print("0. Exit")

            choice = input("Enter your choice (0-9): ")

            if choice == "1":
                years = query.get_available_years()
                print("Available years:", years)

            elif choice == "2":
                provinces = query.get_available_provinces()
                print("Available provinces:", provinces)

            elif choice == "3":
                subjects = query.get_available_subjects()
                print("Available subjects:", subjects)

            elif choice == "4":
                min_score = int(input("Enter minimum score: "))
                max_score = int(input("Enter maximum score: "))
                year = input("Enter year (optional, press Enter to skip): ")
                year = int(year) if year else None
                province = input("Enter province (optional, press Enter to skip): ")
                province = province if province else None
                subject = input("Enter subject (optional, press Enter to skip): ")
                subject = subject if subject else None

                results = query.query_by_score_range(
                    min_score, max_score, year, province, subject
                )
                print(f"Found {len(results)} results:")
                for i, res in enumerate(results[:10]):  # Show only first 10
                    print(f"{i + 1}. {json.dumps(res, ensure_ascii=False)}")
                if len(results) > 10:
                    print(f"... and {len(results) - 10} more results.")

            elif choice == "5":
                min_rank = int(input("Enter minimum rank: "))
                max_rank = int(input("Enter maximum rank: "))
                year = input("Enter year (optional, press Enter to skip): ")
                year = int(year) if year else None
                province = input("Enter province (optional, press Enter to skip): ")
                province = province if province else None
                subject = input("Enter subject (optional, press Enter to skip): ")
                subject = subject if subject else None

                results = query.query_by_rank_range(
                    min_rank, max_rank, year, province, subject
                )
                print(f"Found {len(results)} results:")
                for i, res in enumerate(results[:10]):  # Show only first 10
                    print(f"{i + 1}. {json.dumps(res, ensure_ascii=False)}")
                if len(results) > 10:
                    print(f"... and {len(results) - 10} more results.")

            elif choice == "6":
                rank = int(input("Enter rank: "))
                year = input("Enter year (optional, press Enter to skip): ")
                year = int(year) if year else None
                province = input("Enter province (optional, press Enter to skip): ")
                province = province if province else None
                subject = input("Enter subject (default: 全体): ") or "全体"

                result = query.get_score_by_rank(rank, year, province, subject)
                if result:
                    print(f"Result: {json.dumps(result, ensure_ascii=False)}")
                else:
                    print("No matching score found.")

            elif choice == "7":
                score = int(input("Enter score: "))
                year = input("Enter year (optional, press Enter to skip): ")
                year = int(year) if year else None
                province = input("Enter province (optional, press Enter to skip): ")
                province = province if province else None
                subject = input("Enter subject (default: 全体): ") or "全体"

                result = query.get_rank_by_score(score, year, province, subject)
                if result:
                    print(f"Result: {json.dumps(result, ensure_ascii=False)}")
                else:
                    print("No matching rank found.")

            elif choice == "8":
                file_path = input("Enter the output CSV file path: ")
                year = input("Enter year (optional, press Enter to skip): ")
                year = int(year) if year else None
                province = input("Enter province (optional, press Enter to skip): ")
                province = province if province else None
                subject = input("Enter subject (optional, press Enter to skip): ")
                subject = subject if subject else None

                query.export_to_csv(file_path, year, province, subject)

            elif choice == "9":
                limit = int(
                    input("Enter maximum number of nodes to display (default: 10): ")
                    or "10"
                )
                nodes = query.list_all_nodes(limit)
                print(f"Found {len(nodes)} nodes:")
                for i, node in enumerate(nodes):
                    print(f"{i + 1}. {json.dumps(node, ensure_ascii=False)}")

            elif choice == "0":
                print("Exiting...")
                break

            else:
                print("Invalid choice. Please try again.")

    finally:
        query.close()


if __name__ == "__main__":
    main()
