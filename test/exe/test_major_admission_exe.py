import asyncio
import os
import sys
import json
from datetime import datetime
import time
import random

import pytest
from lucas_common_components.logging import setup_logger
from tortoise import Tortoise

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.exe.command.SchoolMajorCutoffCommandExe import SchoolMajorCutoffCommandExe
from src.infrastructure.db.db_connection import DatabaseConnection
from src.config.config_model import PostgresqlConfig

# 配置日志
logger = setup_logger(__name__)
# 配置 pytest 的异步测试
pytestmark = pytest.mark.asyncio(scope="function")


@pytest.fixture(scope="session")
def event_loop():
    """创建每个测试用例的默认事件循环实例"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session", autouse=True)
async def setup_config(event_loop):
    """初始化数据库连接"""
    logger.info("开始初始化测试数据库连接")
    try:
        db_config = PostgresqlConfig(
            host="pgm-2ze006vz39w74h35.pg.rds.aliyuncs.com",
            port=5432,
            user="test_admin",
            password="EaV4u372ax",
            database="yai-test",
            pool_size=50,  # 增加连接池大小
            connection_timeout=60000,  # 增加连接超时时间到60秒
            pool_recycle=3600,  # 连接回收时间1小时
            max_overflow=20,  # 允许的最大溢出连接数
            echo=False,  # 关闭SQL日志
        )
        logger.info(f"获取到数据库配置: {db_config}")

        config = DatabaseConnection.get_tortoise_config(db_config)
        # 修改 Tortoise 配置
        config["connections"]["default"].update(
            {
                "pool_size": 50,
                "max_overflow": 20,
                "timeout": 60,
                "command_timeout": 60,
                "pool_recycle": 3600,
                "echo": False,
                "retry_limit": 3,  # 添加重试限制
                "retry_interval": 1,  # 重试间隔1秒
            }
        )
        logger.info("成功获取 Tortoise 配置")

        # 初始化 Tortoise
        await Tortoise.init(config=config)
        logger.info("Tortoise 初始化成功")

        # 修改数据库表结构
        try:
            conn = Tortoise.get_connection("default")
            await conn.execute_query(
                "ALTER TABLE major_admission_score ALTER COLUMN major_name TYPE varchar(256);"
            )
            logger.info("成功修改 major_name 字段长度为 256")
        except Exception as e:
            logger.error(f"修改数据库表结构失败: {str(e)}")
            # 如果是因为字段已经是 varchar(256) 则忽略错误
            if "already has type" not in str(e):
                raise

        yield

        # 确保所有连接都被正确关闭
        await Tortoise.close_connections()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"初始化测试数据库连接时发生错误: {str(e)}", exc_info=True)
        raise


@pytest.fixture
def service():
    """返回真实的 MajorAdmissionService 实例"""
    logger.info("创建 MajorAdmissionService 实例")
    service = SchoolMajorCutoffCommandExe()
    # 为了测试目的，手动设置 API key 和参数
    api_key = os.getenv("GUGUDATA_APPKEY", "VRJWAYXT9AZCRUTYT6HX3HNU3H4SJU87")
    service.api_key = api_key  # 从环境变量中获取或使用测试值
    logger.info(f"使用 API KEY: {api_key}")
    service.request_interval = 0.5  # 增加请求间隔到0.5秒
    service.retry_times = 3  # 增加重试次数
    service.retry_interval = 2  # 增加重试间隔
    service.batch_size = 50  # 减小批量大小，避免连接超时
    return service


@pytest.mark.asyncio
async def test_collect_data(service):
    """测试数据采集功能，并验证数据是否成功写入数据库"""
    # 确保 Tortoise ORM 已初始化
    if not Tortoise._inited:
        logger.warning("Tortoise ORM 未初始化，尝试进行初始化...")
        db_config = PostgresqlConfig(
            host="pgm-2ze006vz39w74h35.pg.rds.aliyuncs.com",
            port=5432,
            user="test_admin",
            password="EaV4u372ax",
            database="yai-test",
            pool_size=20,
            connection_timeout=3000,
        )
        config = DatabaseConnection.get_tortoise_config(db_config)
        await Tortoise.init(config=config)
        logger.info("Tortoise ORM 初始化完成")

    # 输出当前 Tortoise 数据库连接状态
    logger.info(f"Tortoise 初始化状态: {Tortoise._inited}")

    # 显示所有注册的模型
    logger.debug(f"Tortoise 注册的模型: {Tortoise.apps}")

    # 使用原始SQL查询来检查数据库中的数据
    conn = Tortoise.get_connection("default")
    # 测试前先查询数据库中相关条件的数据数量
    try:
        logger.info("======= 测试前使用SQL查询数据库记录数 =======")
        result = await conn.execute_query(
            "SELECT COUNT(*) FROM major_admission_score WHERE province_name = '北京' AND school_name LIKE '%清华大学%' AND major_name LIKE '%计算机%' AND year = 2023"
        )
        before_count = result[0][0]
        logger.info(f"测试前数据库中符合条件的记录数: {before_count}")

        # 查看现有记录
        if before_count > 0:
            records_result = await conn.execute_query(
                "SELECT id, school_name, major_name, year, lowest_score FROM major_admission_score WHERE province_name = '北京' AND school_name LIKE '%清华大学%' AND major_name LIKE '%计算机%' AND year = 2023 LIMIT 3"
            )
            for record in records_result[0]:
                logger.debug(f"现有记录示例: {record}")
    except Exception as e:
        logger.error(f"查询数据库失败: {str(e)}", exc_info=True)
        before_count = 0

    # 执行数据采集
    logger.info("======= 开始执行数据采集 =======")
    logger.info("API KEY: " + service.api_key)

    collect_result = await service.collect_data(
        enrollprovince="北京",
        schoolname="清华大学",
        majorname="计算机",
        year=2023,
        batch_size=5,
    )

    logger.info(f"采集结果: {json.dumps(collect_result, ensure_ascii=False, indent=2)}")

    # 验证采集结果基本结构
    assert "success" in collect_result
    assert "total_count" in collect_result
    assert "saved_count" in collect_result

    # 验证 API 调用成功
    assert collect_result["success"] is True

    # 只要能获取数据并且没有抛出异常，就认为测试通过
    assert collect_result["total_count"] >= 0

    # 测试后再次使用SQL查询数据库，验证数据是否写入
    try:
        logger.info("======= 测试后使用SQL查询数据库记录数 =======")
        # 多等待一段时间确保数据写入完成
        await asyncio.sleep(2)
        result = await conn.execute_query(
            "SELECT COUNT(*) FROM major_admission_score WHERE province_name = '北京' AND school_name LIKE '%清华大学%' AND major_name LIKE '%计算机%' AND year = 2023"
        )
        after_count = result[0][0]
        logger.info(f"测试后数据库中符合条件的记录数: {after_count}")

        # 检查记录数是否增加
        expected_increase = collect_result["saved_count"]
        actual_increase = after_count - before_count
        logger.info(
            f"期望增加的记录数: {expected_increase}, 实际增加的记录数: {actual_increase}"
        )

        # 检查是否有数据写入
        if actual_increase > 0:
            logger.info("✅ 数据成功写入数据库")

            # 查看新增的记录
            new_records_result = await conn.execute_query(
                "SELECT id, school_name, major_name, year, lowest_score, created_at FROM major_admission_score WHERE province_name = '北京' AND school_name LIKE '%清华大学%' AND major_name LIKE '%计算机%' AND year = 2023 ORDER BY id DESC LIMIT 3"
            )
            for record in new_records_result[0]:
                logger.info(f"新增记录: {record}")
        else:
            logger.warning(
                "❌ 数据未写入数据库，可能是因为：1) 数据已存在; 2) API未返回数据; 3) 写入失败"
            )

            # 查询API返回的详细信息
            if "raw_data" in collect_result:
                logger.debug(
                    f"原始API响应: {json.dumps(collect_result.get('raw_data'), ensure_ascii=False)[:1000]}..."
                )

                # 检查API是否返回了数据
                data_sample = collect_result.get("raw_data", {}).get("data_sample", [])
                if data_sample:
                    logger.info(
                        f"API确实返回了数据，但未能写入。第一条数据示例: {json.dumps(data_sample[0], ensure_ascii=False)}"
                    )

                    # 尝试手动插入一条记录
                    try:
                        logger.info("尝试手动插入一条记录...")
                        sample = collect_result.get("raw_data", {}).get(
                            "data_sample", []
                        )[0]

                        # 手动构建插入SQL
                        insert_sql = """
                        INSERT INTO major_admission_score 
                        (school_uuid, school_name, province_name, major_name, year, lowest_score, batch_name, type_name) 
                        VALUES 
                        (%s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        await conn.execute_query(
                            insert_sql,
                            [
                                sample.get("SchoolUUID"),
                                sample.get("SchoolName"),
                                sample.get("ProvinceName"),
                                sample.get("MajorName"),
                                sample.get("Year"),
                                sample.get("LowestScore"),
                                sample.get("BatchName"),
                                sample.get("TypeName"),
                            ],
                        )
                        logger.info("手动插入记录成功")
                    except Exception as e:
                        logger.error(f"手动插入记录失败: {str(e)}", exc_info=True)
    except Exception as e:
        logger.error(f"测试后查询数据库失败: {str(e)}", exc_info=True)

    return collect_result

    # @pytest.mark.asyncio
    # async def test_save_and_query_specific_data(service):
    """测试保存特定数据并查询验证"""
    # 1. 获取特定的测试数据
    # 使用较为独特的条件，避免与已有数据冲突
    test_params = {
        "enrollprovince": "天津",  # 选择一个数据较少的省份
        "majorname": "软件工程",
        "year": 2022,  # 使用非当前年份
        "pagesize": 5,
    }

    api_result = await service.fetch_data(test_params)

    # 确保 API 返回了数据
    if api_result["DataStatus"]["StatusCode"] == 100 and api_result["Data"]:
        # 2. 保存这些数据
        saved_count = await service.save_data(api_result["Data"])
        logger.info(f"保存了 {saved_count} 条测试数据")

        # 3. 使用相同条件查询
        query_result = await service.query_scores(
            province_name="天津", major_name="软件工程", year=2022, limit=10
        )

        # 4. 验证查询结果
        assert query_result["success"] is True
        assert len(query_result["data"]) > 0

        # 验证数据字段
        for item in query_result["data"]:
            assert item["province_name"] == "天津"
            assert "软件工程" in item["major_name"]
            assert item["year"] == 2022

        # 验证统计信息
        assert "statistics" in query_result
        assert isinstance(query_result["statistics"], dict)


@pytest.mark.asyncio
async def test_collect_all_data(service):
    """测试全国所有大学所有专业的指定年份数据采集功能"""
    logger.info("======= 开始测试collect_all_data方法 =======")

    # 确保 Tortoise ORM 已初始化
    if not Tortoise._inited:
        logger.warning("Tortoise ORM 未初始化，尝试进行初始化...")
        db_config = PostgresqlConfig(
            host="pgm-2ze006vz39w74h35.pg.rds.aliyuncs.com",
            port=5432,
            user="test_admin",
            password="EaV4u372ax",
            database="yai-test",
            pool_size=20,
            connection_timeout=3000,
        )
        config = DatabaseConnection.get_tortoise_config(db_config)
        await Tortoise.init(config=config)
        logger.info("Tortoise ORM 初始化完成")

    # 修改patch service.collect_data方法，增加额外的日志输出
    original_collect_data = service.collect_data

    async def patched_collect_data(*args, **kwargs):
        logger.info(f"调用collect_data: args={args}, kwargs={kwargs}")
        logger.info("开始调用API获取数据...")

        # 显示调用进度的计时器
        start_time = asyncio.get_event_loop().time()

        # 调用原始方法
        result = await original_collect_data(*args, **kwargs)

        # 记录执行时间
        elapsed = asyncio.get_event_loop().time() - start_time
        logger.info(f"API调用完成，耗时: {elapsed:.2f}秒")

        # 打印结果摘要
        logger.info(
            f"API返回: status={result.get('success')}, total={result.get('total_count')}, saved={result.get('saved_count')}"
        )

        # 显示获取的部分数据示例
        if result.get("raw_data") and result.get("raw_data").get("data_sample"):
            samples = result.get("raw_data").get("data_sample")
            logger.info(f"获取到数据样例({len(samples)}条):")
            for idx, sample in enumerate(samples[:2]):  # 只显示前2条
                logger.info(
                    f"  样例{idx + 1}: 学校={sample.get('SchoolName')}, 专业={sample.get('MajorName')}, 分数={sample.get('LowestScore')}"
                )

        return result

    # 替换方法
    service.collect_data = patched_collect_data

    try:
        test_year = 2024

        # 获取测试前数据库中该年份的记录数
        conn = Tortoise.get_connection("default")
        try:
            result = await conn.execute_query(
                f"SELECT COUNT(*) FROM major_admission_score WHERE year = {test_year}"
            )
            before_count = result[0][0]
            logger.info(f"测试前数据库中{test_year}年的记录数: {before_count}")
        except Exception as e:
            logger.error(f"查询数据库失败: {str(e)}", exc_info=True)
            before_count = 0

        # 设置更小的batch_size以加快测试速度
        test_batch_size = 30

        # 显示测试参数
        logger.info(f"测试参数: year={test_year}, batch_size={test_batch_size}")
        logger.info("开始调用collect_all_data方法...")

        # 使用更简化的参数调用，只获取少量数据
        test_params = {"year": test_year, "batch_size": test_batch_size}

        # 记录开始时间
        start_time = asyncio.get_event_loop().time()

        # 调用被测试方法
        result = await service.collect_all_data(**test_params)

        # 记录结束时间和总耗时
        elapsed = asyncio.get_event_loop().time() - start_time
        logger.info(f"collect_all_data完成，总耗时: {elapsed:.2f}秒")

        # 详细打印采集结果
        logger.info("采集结果摘要:")
        logger.info(f"  成功状态: {result.get('success')}")
        logger.info(f"  总数据量: {result.get('total_count')}")
        logger.info(f"  保存数量: {result.get('saved_count')}")
        logger.info(f"  执行时间: {result.get('execution_time_seconds'):.2f}秒")

        # 验证返回结果的基本结构
        assert "success" in result
        assert "total_count" in result
        assert "saved_count" in result
        assert "execution_time_seconds" in result

        # 验证执行是否成功
        assert result["success"] is True

        # 验证总数据量大于0
        assert result["total_count"] >= 0

        # 如果API确实返回了数据，验证执行时间合理
        if result["total_count"] > 0:
            assert result["execution_time_seconds"] > 0

        # 等待数据写入完成
        logger.info("等待数据写入完成...")
        await asyncio.sleep(2)

        # 验证数据是否写入数据库
        try:
            result = await conn.execute_query(
                f"SELECT COUNT(*) FROM major_admission_score WHERE year = {test_year}"
            )
            after_count = result[0][0]
            logger.info(f"测试后数据库中{test_year}年的记录数: {after_count}")

            # 检查记录数是否增加或与API返回的保存数量一致
            # 注意：如果数据库中已存在部分记录，saved_count可能小于API返回的总数据量
            if after_count > before_count:
                logger.info(
                    f"✅ 数据成功写入数据库，增加了 {after_count - before_count} 条记录"
                )

                # 查询最新添加的记录示例
                sample_records = await conn.execute_query(
                    f"SELECT id, school_name, major_name, province_name, lowest_score FROM major_admission_score WHERE year = {test_year} ORDER BY id DESC LIMIT 3"
                )

                if sample_records and sample_records[0]:
                    logger.info("最新添加的记录示例:")
                    for idx, record in enumerate(sample_records[0]):
                        logger.info(f"  记录{idx + 1}: {record}")
            else:
                logger.warning(
                    "❌ 数据未增加，可能是因为：1) 数据已存在; 2) API未返回新数据; 3) 写入失败"
                )
        except Exception as e:
            logger.error(f"测试后查询数据库失败: {str(e)}", exc_info=True)

        logger.info("======= 测试collect_all_data方法完成 =======")
        return result

    finally:
        # 恢复原始方法
        service.collect_data = original_collect_data


@pytest.mark.asyncio
async def test_collect_with_checkpoint(service):
    """测试带断点续传功能的多省份并行采集"""
    logger.info(
        "======= 开始测试collect_all_data_by_province_with_checkpoint方法 ======="
    )

    # 确保 Tortoise ORM 已初始化
    if not Tortoise._inited:
        logger.warning("Tortoise ORM 未初始化，尝试进行初始化...")
        db_config = PostgresqlConfig(
            host="pgm-2ze006vz39w74h35.pg.rds.aliyuncs.com",
            port=5432,
            user="test_admin",
            password="EaV4u372ax",
            database="yai-test",
            pool_size=20,
            connection_timeout=3000,
        )
        config = DatabaseConnection.get_tortoise_config(db_config)
        await Tortoise.init(config=config)
        logger.info("Tortoise ORM 初始化完成")

    # 使用所有省份进行完整测试
    test_year = 2024
    test_batch_size = 100
    # 包含所有省份
    test_provinces = [
        "上海",
        "云南",
        "内蒙古",
        "北京",
        "台湾",
        "吉林",
        "四川",
        "天津",
        "宁夏",
        "安徽",
        "山东",
        "山西",
        "广东",
        "广西",
        "新疆",
        "江苏",
        "江西",
        "河北",
        "河南",
        "浙江",
        "海南",
        "湖北",
        "湖南",
        "甘肃",
        "福建",
        "西藏",
        "贵州",
        "辽宁",
        "重庆",
        "陕西",
        "青海",
        "香港",
        "黑龙江",
    ]
    test_checkpoint_file = f"test_checkpoint_{test_year}.json"
    test_max_workers = 5  # 并行度修改为1，完全串行化处理以避免API限制导致的HTTP 405错误

    # 替换原始方法以使用所有省份
    original_collect_all_data_by_province_with_checkpoint = (
        service.collect_all_data_by_province_with_checkpoint
    )

    async def patched_collect_all_data_by_province_with_checkpoint(
        year: int,
        batch_size: int = 50,
        max_workers: int = 3,
        checkpoint_file: str = None,
    ):
        logger.info(f"测试断点续传功能，使用省份: {test_provinces}")

        # 使用原始实现
        # 但是修改all_provinces为测试省份
        start_time = time.time()
        total_count = 0
        saved_count = 0

        # 设置断点续传文件
        if checkpoint_file is None:
            checkpoint_file = test_checkpoint_file

        # 加载断点信息
        processed_provinces = []
        provinces_results = {}
        if os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, "r", encoding="utf-8") as f:
                    checkpoint_data = json.load(f)
                    processed_provinces = checkpoint_data.get("processed_provinces", [])
                    provinces_results = checkpoint_data.get("provinces_results", {})
                    total_count = checkpoint_data.get("total_count", 0)
                    saved_count = checkpoint_data.get("saved_count", 0)
                    logger.info(
                        f"加载断点续传文件: {checkpoint_file}, 已处理{len(processed_provinces)}个省份"
                    )
            except Exception as e:
                logger.error(f"读取断点续传文件失败: {str(e)}, 将从头开始处理")
                processed_provinces = []
                provinces_results = {}

        # 过滤出未处理的省份
        provinces_to_process = [
            p for p in test_provinces if p not in processed_provinces
        ]

        if not provinces_to_process:
            logger.info(f"所有省份已处理完毕，共{len(processed_provinces)}个省份")
            return {
                "success": True,
                "message": f"所有省份已处理完毕，共{len(processed_provinces)}个省份",
                "total_count": total_count,
                "saved_count": saved_count,
                "provinces_count": len(processed_provinces),
                "provinces_results": provinces_results,
                "execution_time_seconds": 0,
            }

        logger.info(
            f"开始按省份采集{year}年数据，待处理{len(provinces_to_process)}个省份"
        )

        # 定义单个省份处理函数
        async def process_province(province):
            retry_count = 0
            max_retries = 2
            while retry_count <= max_retries:
                try:
                    logger.info(f"开始采集省份: {province}, 重试次数: {retry_count}")

                    # 使用优化版的数据采集方法，一条数据即时保存
                    result = await service.collect_data_optimized(
                        enrollprovince=province, year=year, batch_size=batch_size
                    )

                    if result.get("success", False):
                        logger.info(
                            f"成功采集{province}省份数据，总数据量: {result.get('total_count')}, 保存数量: {result.get('saved_count')}"
                        )
                        return {
                            "province": province,
                            "success": True,
                            "total_count": result.get("total_count", 0),
                            "saved_count": result.get("saved_count", 0),
                            "message": f"成功采集{province}省份数据",
                        }
                    else:
                        error_msg = result.get("message", "未知错误")
                        logger.error(f"采集{province}省份数据失败: {error_msg}")

                        # 特定错误类型进行重试
                        if (
                            "请求频率过高" in error_msg
                            or "服务暂时不可用" in error_msg
                            or "超时" in error_msg
                            or "429" in error_msg
                            or "502" in error_msg
                        ):
                            retry_count += 1
                            # 使用更长延迟，避免频率限制
                            retry_delay = service.retry_interval * (
                                3**retry_count
                            ) + random.uniform(1, 3)
                            logger.info(
                                f"检测到频率限制，将在 {retry_delay:.2f} 秒后重试采集{province}省份数据"
                            )
                            await asyncio.sleep(retry_delay)
                            continue

                        return {
                            "province": province,
                            "success": False,
                            "total_count": 0,
                            "saved_count": 0,
                            "message": error_msg,
                        }
                except Exception as e:
                    logger.exception(f"采集{province}省份数据异常: {str(e)}")
                    retry_count += 1
                    retry_delay = service.retry_interval * (
                        2**retry_count
                    ) + random.uniform(0.5, 1.5)
                    logger.info(
                        f"将在 {retry_delay:.2f} 秒后重试采集{province}省份数据"
                    )
                    await asyncio.sleep(retry_delay)

            # 达到最大重试次数后仍失败
            return {
                "province": province,
                "success": False,
                "total_count": 0,
                "saved_count": 0,
                "message": f"达到最大重试次数({max_retries})后仍然失败",
            }

        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(max_workers)

        async def bounded_process_province(province):
            async with semaphore:
                return await process_province(province)

        # 批量处理省份，使用有限的并发
        tasks = [
            bounded_process_province(province) for province in provinces_to_process
        ]
        results = []

        for task in asyncio.as_completed(tasks):
            result = await task
            province = result.get("province")
            results.append(result)

            # 更新计数
            if result.get("success", False):
                province_total = result.get("total_count", 0)
                province_saved = result.get("saved_count", 0)
                total_count += province_total
                saved_count += province_saved
                processed_provinces.append(province)
                provinces_results[province] = {
                    "success": True,
                    "total_count": province_total,
                    "saved_count": province_saved,
                    "timestamp": datetime.now().isoformat(),
                }
            else:
                provinces_results[province] = {
                    "success": False,
                    "message": result.get("message", "未知错误"),
                    "timestamp": datetime.now().isoformat(),
                }

            # 保存断点续传文件
            checkpoint_data = {
                "processed_provinces": processed_provinces,
                "provinces_results": provinces_results,
                "total_count": total_count,
                "saved_count": saved_count,
                "last_update": datetime.now().isoformat(),
                "completed_provinces_count": len(processed_provinces),
                "total_provinces_count": len(test_provinces),
            }

            try:
                with open(checkpoint_file, "w", encoding="utf-8") as f:
                    json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                    logger.info(
                        f"已更新断点续传文件: {checkpoint_file}, 当前已处理{len(processed_provinces)}/{len(test_provinces)}个省份"
                    )
            except Exception as e:
                logger.error(f"保存断点续传文件失败: {str(e)}")

        end_time = time.time()
        execution_time = end_time - start_time

        # 计算成功和失败的省份
        success_provinces = [
            p for p in provinces_results if provinces_results[p].get("success", False)
        ]
        failed_provinces = [
            p
            for p in provinces_results
            if not provinces_results[p].get("success", False)
        ]

        # 最终结果
        result = {
            "success": len(failed_provinces) == 0,
            "message": f"完成按省份采集{year}年数据，成功{len(success_provinces)}个省份，失败{len(failed_provinces)}个省份",
            "total_count": total_count,
            "saved_count": saved_count,
            "provinces_count": len(test_provinces),
            "processed_provinces_count": len(processed_provinces),
            "success_provinces_count": len(success_provinces),
            "failed_provinces_count": len(failed_provinces),
            "failed_provinces": failed_provinces,
            "provinces_results": provinces_results,
            "execution_time_seconds": execution_time,
            "checkpoint_file": checkpoint_file,
            "completed_time": datetime.now().isoformat(),
        }

        # 记录日志
        logger.info(
            f"采集统计：总计{total_count}条数据，成功保存{saved_count}条，耗时{execution_time:.2f}秒"
        )
        logger.info(
            f"省份统计：总计{len(test_provinces)}个省份，成功{len(success_provinces)}个，失败{len(failed_provinces)}个"
        )
        if failed_provinces:
            logger.warning(f"失败省份列表: {', '.join(failed_provinces)}")

        return result

    # 替换方法
    service.collect_all_data_by_province_with_checkpoint = (
        patched_collect_all_data_by_province_with_checkpoint
    )

    try:
        # 删除可能已存在的断点续传文件，以便从头开始测试
        if os.path.exists(test_checkpoint_file):
            try:
                os.remove(test_checkpoint_file)
                logger.info(f"已删除旧的断点续传文件: {test_checkpoint_file}")
            except Exception as e:
                logger.error(f"删除旧的断点续传文件失败: {str(e)}")

        # 获取测试前数据库中特定年份的记录数
        conn = Tortoise.get_connection("default")
        try:
            # 构建IN查询的省份列表
            provinces_str = "', '".join(test_provinces)
            query = f"SELECT COUNT(*) as count FROM major_admission_score WHERE year = {test_year} AND province_name IN ('{provinces_str}')"
            result = await conn.execute_query(query)
            # 正确处理查询结果
            before_count = (
                result[1][0]["count"] if result and len(result) > 1 and result[1] else 0
            )
            logger.info(f"测试前数据库中{test_year}年指定省份的记录数: {before_count}")
        except Exception as e:
            logger.error(f"查询数据库失败: {str(e)}", exc_info=True)
            before_count = 0

        # 显示测试参数
        logger.info(
            f"测试参数: provinces数量={len(test_provinces)}, year={test_year}, batch_size={test_batch_size}, max_workers={test_max_workers}"
        )

        # 记录开始时间
        start_time = asyncio.get_event_loop().time()

        # 调用被测试方法
        result = await service.collect_all_data_by_province_with_checkpoint(
            year=test_year,
            batch_size=test_batch_size,
            max_workers=test_max_workers,
            checkpoint_file=test_checkpoint_file,
        )

        # 记录总耗时
        elapsed = asyncio.get_event_loop().time() - start_time
        logger.info(
            f"collect_all_data_by_province_with_checkpoint完成，总耗时: {elapsed:.2f}秒"
        )

        # 打印采集结果摘要
        logger.info("采集结果摘要:")
        logger.info(f"  成功状态: {result.get('success')}")
        logger.info(f"  总数据量: {result.get('total_count')}")
        logger.info(f"  保存数量: {result.get('saved_count')}")
        logger.info(f"  省份数量: {result.get('provinces_count')}")
        logger.info(f"  已处理省份数量: {result.get('processed_provinces_count')}")
        logger.info(f"  成功省份数量: {result.get('success_provinces_count')}")
        logger.info(f"  失败省份数量: {result.get('failed_provinces_count')}")
        logger.info(f"  执行时间: {result.get('execution_time_seconds'):.2f}秒")

        # 验证断点续传文件是否创建
        assert os.path.exists(test_checkpoint_file)

        # 读取断点续传文件内容
        try:
            with open(test_checkpoint_file, "r", encoding="utf-8") as f:
                checkpoint_data = json.load(f)
                logger.info(
                    f"断点续传文件内容预览: {json.dumps(checkpoint_data, ensure_ascii=False)[:200]}..."
                )

                # 验证断点续传文件结构
                assert "processed_provinces" in checkpoint_data
                assert "provinces_results" in checkpoint_data
                assert "total_count" in checkpoint_data
                assert "saved_count" in checkpoint_data
                assert "last_update" in checkpoint_data

                # 验证已处理省份数量
                assert len(checkpoint_data["processed_provinces"]) == result.get(
                    "processed_provinces_count"
                )
        except Exception as e:
            logger.error(f"读取或验证断点续传文件失败: {str(e)}")
            assert False, f"断点续传文件验证失败: {str(e)}"

        # 验证返回结果的基本结构
        assert "success" in result
        assert "total_count" in result
        assert "saved_count" in result
        assert "provinces_count" in result
        assert "processed_provinces_count" in result
        assert "success_provinces_count" in result
        assert "failed_provinces_count" in result
        assert "failed_provinces" in result
        assert "provinces_results" in result
        assert "execution_time_seconds" in result
        assert "checkpoint_file" in result

        # 等待数据写入完成
        logger.info("等待数据写入完成...")
        await asyncio.sleep(2)

        # 验证数据是否写入数据库
        try:
            # 构建IN查询的省份列表
            provinces_str = "', '".join(test_provinces)
            query = f"SELECT COUNT(*) as count FROM major_admission_score WHERE year = {test_year} AND province_name IN ('{provinces_str}')"
            result_after = await conn.execute_query(query)
            # 正确处理查询结果
            after_count = (
                result_after[1][0]["count"]
                if result_after and len(result_after) > 1 and result_after[1]
                else 0
            )
            logger.info(f"测试后数据库中{test_year}年指定省份的记录数: {after_count}")

            # 检查记录数是否增加
            if after_count > before_count:
                logger.info(
                    f"✅ 数据成功写入数据库，增加了 {after_count - before_count} 条记录"
                )

                # 查询最新添加的记录示例
                sample_records = await conn.execute_query(
                    f"SELECT id, school_name, major_name, province_name, lowest_score, batch_name FROM major_admission_score WHERE year = {test_year} AND province_name IN ('{provinces_str}') ORDER BY id DESC LIMIT 5"
                )

                if sample_records and sample_records[0]:
                    logger.info("最新添加的记录示例:")
                    for idx, record in enumerate(sample_records[0]):
                        logger.info(f"  记录{idx + 1}: {record}")
            else:
                logger.warning(
                    "❌ 数据未增加，可能是因为：1) 数据已存在; 2) API未返回新数据; 3) 写入失败"
                )
        except Exception as e:
            logger.error(f"测试后查询数据库失败: {str(e)}", exc_info=True)

        # 验证断点续传功能 - 再次运行相同的采集，应该跳过已处理的省份
        logger.info("======= 测试断点续传功能 - 第二次运行 =======")

        # 第二次调用应该跳过已处理的省份
        second_start_time = asyncio.get_event_loop().time()
        second_result = await service.collect_all_data_by_province_with_checkpoint(
            year=test_year,
            batch_size=test_batch_size,
            max_workers=test_max_workers,
            checkpoint_file=test_checkpoint_file,
        )
        second_elapsed = asyncio.get_event_loop().time() - second_start_time

        logger.info(f"第二次运行完成，总耗时: {second_elapsed:.2f}秒")
        logger.info("第二次运行结果摘要:")
        logger.info(f"  成功状态: {second_result.get('success')}")
        logger.info(
            f"  已处理省份数量: {second_result.get('processed_provinces_count')}"
        )
        logger.info(f"  执行时间: {second_result.get('execution_time_seconds'):.2f}秒")

        # 验证第二次运行是否快速完成（所有省份已处理）
        assert (
            second_result.get("execution_time_seconds", 0)
            < result.get("execution_time_seconds", float("inf")) * 0.1
        ), "第二次运行应该几乎不耗时，因为所有省份已处理"
        assert second_result.get("processed_provinces_count") == len(test_provinces), (
            "所有省份应该已标记为已处理"
        )

        # 清理测试文件
        try:
            os.remove(test_checkpoint_file)
            logger.info(f"已删除测试断点续传文件: {test_checkpoint_file}")
        except Exception as e:
            logger.warning(f"清理测试文件失败: {str(e)}")

        logger.info(
            "======= 测试collect_all_data_by_province_with_checkpoint方法完成 ======="
        )
        return result

    finally:
        # 恢复原始方法
        service.collect_all_data_by_province_with_checkpoint = (
            original_collect_all_data_by_province_with_checkpoint
        )

        # 尝试清理测试文件
        if os.path.exists(test_checkpoint_file):
            try:
                os.remove(test_checkpoint_file)
                logger.info(f"已删除测试断点续传文件: {test_checkpoint_file}")
            except Exception as e:
                logger.warning(f"清理测试文件失败: {str(e)}")
