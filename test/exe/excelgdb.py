#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
This script reads data from an Excel file containing Gaokao score segment data
and injects it into a Neo4j graph database.
"""

import pandas as pd
from typing import Dict, List, Any
from neo4j import GraphDatabase
from datetime import datetime

# Neo4j 连接配置
neo4j_uri = "bolt://gds-2ze79n4u801cdnln149870.graphdb.rds.aliyuncs.com:8182"
neo4j_user = "test_admin"
neo4j_password = "EaV4u372ax"

# 高考一分一段表Excel文件路径
DEFAULT_EXCEL_PATH = (
    "/home/<USER>/python/5.8/yai-chat/ref/docs/山东高考一份一段.xlsx"
)
DEFAULT_YEAR = 2024
DEFAULT_PROVINCE = "山东省"


class ExcelToGraphDB:
    def __init__(
        self,
        uri: str = neo4j_uri,
        user: str = neo4j_user,
        password: str = neo4j_password,
    ):
        """Initialize the connection to Neo4j."""
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def close(self):
        """Close the Neo4j driver connection."""
        self.driver.close()

    def _create_gaokao_score_node(self, tx, data: Dict[str, Any]):
        """Create a GaokaoScoreSegment node in Neo4j."""
        # Using MERGE to avoid duplicate nodes
        # Add timestamp to data
        timestamp_data = data.copy()
        timestamp_data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        query = """
        MERGE (s:GaokaoScoreSegment {year: $year, province: $province, score: $score, subject: $subject})
        ON CREATE SET s.tiescore = $tiescore, s.rank = $rank, s.created_at = $timestamp
        ON MATCH SET s.tiescore = $tiescore, s.rank = $rank, s.updated_at = $timestamp
        RETURN s
        """
        result = tx.run(query, **timestamp_data)
        return result.data()[0] if result.data() else None

    def batch_create_gaokao_scores(self, data_list: List[Dict[str, Any]]):
        """Batch create GaokaoScoreSegment nodes from a list of data dictionaries."""
        with self.driver.session() as session:
            for data in data_list:
                session.execute_write(self._create_gaokao_score_node, data)
            print(
                f"Successfully injected {len(data_list)} GaokaoScoreSegment nodes into Neo4j."
            )

    def process_excel_file(
        self,
        excel_path: str = DEFAULT_EXCEL_PATH,
        sheet_name: str = 0,
        year: int = DEFAULT_YEAR,
        province: str = DEFAULT_PROVINCE,
        header_row: int = 2,  # 使用第3行作为列标题 (0-indexed)
        skip_rows: int = 3,  # 跳过前3行 (标题行、主列标题行和子标题行)
    ) -> List[Dict[str, Any]]:
        """
        Process an Excel file containing Gaokao score segment data.

        Args:
            excel_path: Path to the Excel file
            sheet_name: Name or index of the sheet to read
            year: Year of the Gaokao data
            province: Province of the Gaokao data
            header_row: Row index (0-based) where header information is located
            skip_rows: Number of rows to skip at the beginning of the file

        Returns:
            List of dictionaries with node data
        """
        try:
            # Read Excel file
            df = pd.read_excel(
                excel_path,
                sheet_name=sheet_name,
                header=None,  # 不使用文件中的列标题
                skiprows=skip_rows,
            )
            print(f"Successfully read Excel file: {excel_path}, sheet: {sheet_name}")

            # Check if the dataframe is empty
            if df.empty:
                print("Warning: Excel file contains no data!")
                return []

            # 打印数据框的形状和前几行，帮助调试
            print(f"DataFrame shape: {df.shape}")
            print("First few rows:")
            print(df.head())

            # 准备数据
            data_list = []

            # 处理每一行数据
            for idx, row in df.iterrows():
                score = str(row.iloc[0])  # 第一列是分数

                # 处理每个科目
                # 科目列的排列顺序: 全体, 选考物理, 选考化学, 选考生物, 选考思想政治, 选考历史, 选考地理
                subjects = [
                    "全体",
                    "选考物理",
                    "选考化学",
                    "选考生物",
                    "选考思想政治",
                    "选考历史",
                    "选考地理",
                ]

                for i, subject in enumerate(subjects):
                    # 每个科目有两列 (本段人数, 累计人数)
                    # 计算本段人数和累计人数的列索引
                    subject_col_base = 1 + i * 2  # 从B列(索引1)开始

                    # 检查科目列是否存在
                    if subject_col_base < len(row) and subject_col_base + 1 < len(row):
                        tiescore = str(row.iloc[subject_col_base])  # 本段人数
                        rank = str(row.iloc[subject_col_base + 1])  # 累计人数

                        # 跳过空值或NaN
                        if pd.isna(tiescore) or pd.isna(rank):
                            continue

                        # 创建节点数据
                        node_data = {
                            "year": year,
                            "province": province,
                            "score": score,
                            "subject": subject,
                            "tiescore": tiescore,
                            "rank": rank,
                        }
                        data_list.append(node_data)

            print(f"Processed {len(data_list)} data points")
            return data_list

        except Exception as e:
            print(f"Error processing Excel file: {str(e)}")
            import traceback

            traceback.print_exc()  # 打印详细错误栈
            return []

    def process_and_inject_excel_data(
        self,
        excel_path: str = DEFAULT_EXCEL_PATH,
        sheet_name: str = 0,
        year: int = DEFAULT_YEAR,
        province: str = DEFAULT_PROVINCE,
        header_row: int = 2,
        skip_rows: int = 3,
    ):
        """Process Excel file and inject data into Neo4j in one operation."""
        data_list = self.process_excel_file(
            excel_path, sheet_name, year, province, header_row, skip_rows
        )

        if data_list:
            self.batch_create_gaokao_scores(data_list)
            return True
        return False


def main():
    """Main function to demonstrate usage."""
    # 使用硬编码的默认路径，也允许用户自定义
    excel_path = (
        input(f"Enter the path to the Excel file (default: {DEFAULT_EXCEL_PATH}): ")
        or DEFAULT_EXCEL_PATH
    )
    sheet_name = input("Enter the sheet name or index (default: 0): ") or 0
    try:
        sheet_name = int(sheet_name)
    except ValueError:
        pass  # 如果不能转换为整数，保持为字符串

    year = int(
        input(f"Enter the year for the data (default: {DEFAULT_YEAR}): ")
        or str(DEFAULT_YEAR)
    )
    province = (
        input(f"Enter the province (default: {DEFAULT_PROVINCE}): ") or DEFAULT_PROVINCE
    )
    header_row = int(input("Enter the header row index (0-based, default: 2): ") or "2")
    skip_rows = int(input("Enter the number of rows to skip (default: 3): ") or "3")

    print(f"Processing file: {excel_path}")
    print(
        f"Parameters: sheet={sheet_name}, year={year}, province={province}, header_row={header_row}, skip_rows={skip_rows}"
    )

    etg = ExcelToGraphDB()
    try:
        success = etg.process_and_inject_excel_data(
            excel_path=excel_path,
            sheet_name=sheet_name,
            year=year,
            province=province,
            header_row=header_row,
            skip_rows=skip_rows,
        )

        if success:
            print(f"Successfully processed and injected data from {excel_path}")
        else:
            print(f"Failed to process and inject data from {excel_path}")
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        import traceback

        traceback.print_exc()  # 打印详细错误栈
    finally:
        etg.close()


if __name__ == "__main__":
    main()
