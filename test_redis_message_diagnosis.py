#!/usr/bin/env python3
"""
Redis 消息接收诊断脚本

检查 chat_processor 中 Redis 消息接收的各个环节，
帮助定位收不到消息的具体原因。
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_redis_connection():
    """测试 Redis 连接"""
    print("🔍 测试 Redis 连接...")
    
    try:
        from src.infrastructure.session.redis_client import RedisClient
        
        # 获取 Redis 客户端实例
        redis_client = await RedisClient.get_instance()
        
        # 检查初始化状态
        print(f"    Redis 初始化状态: {redis_client._initialized}")
        print(f"    Redis 连接对象: {redis_client._redis}")
        print(f"    PubSub 对象: {redis_client._pubsub}")
        
        # 测试基本连接
        if redis_client._redis:
            try:
                await redis_client._redis.ping()
                print("    ✅ Redis 连接正常")
                return True
            except Exception as e:
                print(f"    ❌ Redis 连接失败: {e}")
                return False
        else:
            print("    ❌ Redis 连接对象为空")
            return False
            
    except Exception as e:
        print(f"❌ Redis 连接测试失败: {e}")
        traceback.print_exc()
        return False


async def test_redis_config():
    """测试 Redis 配置"""
    print("\n🔍 测试 Redis 配置...")
    
    try:
        from src.config.config_model import AIChatAppConfig
        
        config = AIChatAppConfig.get_instance()
        redis_config = config.redis
        
        if redis_config:
            print(f"    Redis Host: {redis_config.host}")
            print(f"    Redis Port: {redis_config.port}")
            print(f"    Redis DB: {redis_config.db}")
            print(f"    Redis Username: {redis_config.username}")
            print(f"    Redis Password: {'***' if redis_config.password else 'None'}")
            print(f"    Max Connections: {redis_config.max_connections}")
            print("    ✅ Redis 配置存在")
            return True
        else:
            print("    ❌ Redis 配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ Redis 配置测试失败: {e}")
        traceback.print_exc()
        return False


async def test_message_broadcaster():
    """测试消息广播器"""
    print("\n🔍 测试消息广播器...")
    
    try:
        from src.infrastructure.session.message_broadcaster import MessageBroadcasterFactory
        
        # 创建广播器
        broadcaster = await MessageBroadcasterFactory.create()
        
        if broadcaster:
            print(f"    广播器类型: {type(broadcaster).__name__}")
            print(f"    广播器初始化状态: {getattr(broadcaster, '_initialized', 'Unknown')}")
            print("    ✅ 消息广播器创建成功")
            
            # 测试订阅
            test_messages = []
            
            async def test_callback(channel: str, message):
                test_messages.append((channel, message))
                print(f"    📨 收到消息: {channel} -> {message}")
            
            # 订阅测试频道
            success = await broadcaster.subscribe("test_channel", test_callback, "test_subscriber")
            if success:
                print("    ✅ 订阅测试频道成功")
                
                # 发布测试消息
                await asyncio.sleep(0.5)  # 等待订阅生效
                publish_success = await broadcaster.publish("test_channel", {"test": "message"})
                
                if publish_success:
                    print("    ✅ 发布测试消息成功")
                    
                    # 等待消息接收
                    await asyncio.sleep(2.0)
                    
                    if test_messages:
                        print(f"    ✅ 收到 {len(test_messages)} 条消息")
                        return True
                    else:
                        print("    ❌ 未收到测试消息")
                        return False
                else:
                    print("    ❌ 发布测试消息失败")
                    return False
            else:
                print("    ❌ 订阅测试频道失败")
                return False
        else:
            print("    ❌ 消息广播器创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 消息广播器测试失败: {e}")
        traceback.print_exc()
        return False


async def test_connection_manager_subscription():
    """测试连接管理器的订阅"""
    print("\n🔍 测试连接管理器的订阅...")
    
    try:
        from src.infrastructure.session import ConnectionManager
        
        # 创建连接管理器
        connection_manager = ConnectionManager()
        
        # 等待广播器初始化
        await asyncio.sleep(2.0)
        
        # 检查广播器状态
        broadcaster_ready = await connection_manager.ensure_broadcaster_ready()
        print(f"    广播器就绪状态: {broadcaster_ready}")
        
        if broadcaster_ready:
            print(f"    广播器对象: {connection_manager._broadcaster}")
            print("    ✅ 连接管理器广播器就绪")
            
            # 检查是否已订阅频道
            if hasattr(connection_manager._broadcaster, '_redis_client'):
                redis_client = connection_manager._broadcaster._redis_client
                if redis_client and hasattr(redis_client, '_subscribers'):
                    subscribers = redis_client._subscribers
                    print(f"    当前订阅频道: {list(subscribers.keys())}")
                    
                    if "conversation_message" in subscribers:
                        print("    ✅ 已订阅 conversation_message 频道")
                        return True
                    else:
                        print("    ❌ 未订阅 conversation_message 频道")
                        return False
            
            return True
        else:
            print("    ❌ 连接管理器广播器未就绪")
            return False
            
    except Exception as e:
        print(f"❌ 连接管理器订阅测试失败: {e}")
        traceback.print_exc()
        return False


async def test_chat_processor_integration():
    """测试 ChatProcessor 集成"""
    print("\n🔍 测试 ChatProcessor 集成...")
    
    try:
        from src.infrastructure.session.chat_processor import ChatProcessor
        
        # 获取 ChatProcessor 实例
        chat_processor = ChatProcessor.get_instance()
        
        print(f"    ChatProcessor 实例: {chat_processor}")
        print(f"    ConnectionManager: {chat_processor.connection_manager}")
        print(f"    SessionManager: {chat_processor.session_manager}")
        
        # 检查连接管理器的广播器
        connection_manager = chat_processor.connection_manager
        await asyncio.sleep(1.0)  # 等待初始化
        
        broadcaster_ready = await connection_manager.ensure_broadcaster_ready()
        print(f"    ChatProcessor 广播器就绪: {broadcaster_ready}")
        
        if broadcaster_ready:
            print("    ✅ ChatProcessor 集成正常")
            return True
        else:
            print("    ❌ ChatProcessor 广播器未就绪")
            return False
            
    except Exception as e:
        print(f"❌ ChatProcessor 集成测试失败: {e}")
        traceback.print_exc()
        return False


async def test_end_to_end_message_flow():
    """测试端到端消息流"""
    print("\n🔍 测试端到端消息流...")
    
    try:
        from src.infrastructure.session.chat_processor import ChatProcessor
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        # 获取 ChatProcessor 实例
        chat_processor = ChatProcessor.get_instance()
        connection_manager = chat_processor.connection_manager
        
        # 等待初始化完成
        await asyncio.sleep(2.0)
        
        # 创建测试连接
        test_conversation_id = "test_conversation_123"
        connection = await connection_manager.add_connection(test_conversation_id, "test_user")
        
        print(f"    创建测试连接: {connection.connection_id}")
        
        # 收集接收到的消息
        received_messages = []
        
        async def collect_messages():
            try:
                while connection.status.value == "active":
                    try:
                        message = await asyncio.wait_for(
                            connection.response_queue.get(), 
                            timeout=5.0
                        )
                        received_messages.append(message)
                        print(f"    📨 连接收到消息: {message}")
                        
                        if message is None:  # 结束信号
                            break
                    except asyncio.TimeoutError:
                        break
            except Exception as e:
                print(f"    消息收集异常: {e}")
        
        # 启动消息收集任务
        collect_task = asyncio.create_task(collect_messages())
        
        # 等待一下
        await asyncio.sleep(0.5)
        
        # 通过广播发送测试消息
        test_message = {"test": "broadcast_message", "timestamp": time.time()}
        await connection_manager.broadcast_to_conversation(test_conversation_id, test_message)
        
        print(f"    发送广播消息: {test_message}")
        
        # 等待消息接收
        await asyncio.sleep(3.0)
        
        # 发送结束信号
        await connection_manager.broadcast_to_conversation(test_conversation_id, None)
        
        # 等待收集任务完成
        try:
            await asyncio.wait_for(collect_task, timeout=2.0)
        except asyncio.TimeoutError:
            collect_task.cancel()
        
        # 检查结果
        if received_messages:
            print(f"    ✅ 收到 {len(received_messages)} 条消息")
            for i, msg in enumerate(received_messages):
                print(f"      消息 {i+1}: {msg}")
            return True
        else:
            print("    ❌ 未收到任何消息")
            return False
            
    except Exception as e:
        print(f"❌ 端到端消息流测试失败: {e}")
        traceback.print_exc()
        return False


async def test_redis_listener_thread():
    """测试 Redis 监听线程"""
    print("\n🔍 测试 Redis 监听线程...")
    
    try:
        from src.infrastructure.session.redis_client import RedisClient
        
        redis_client = await RedisClient.get_instance()
        
        print(f"    监听器启动状态: {redis_client._listener_started}")
        print(f"    监听线程: {redis_client._listener_thread}")
        print(f"    停止标志: {redis_client._stop_listener}")
        
        if redis_client._listener_thread:
            print(f"    线程存活状态: {redis_client._listener_thread.is_alive()}")
            print(f"    线程守护状态: {redis_client._listener_thread.daemon}")
            
            if redis_client._listener_thread.is_alive():
                print("    ✅ Redis 监听线程正在运行")
                return True
            else:
                print("    ❌ Redis 监听线程未运行")
                return False
        else:
            print("    ❌ Redis 监听线程未创建")
            return False
            
    except Exception as e:
        print(f"❌ Redis 监听线程测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 Redis 消息接收诊断")
    print("=" * 60)
    
    async def run_diagnosis():
        results = {}
        
        # 运行诊断测试
        results["redis_config"] = await test_redis_config()
        results["redis_connection"] = await test_redis_connection()
        results["message_broadcaster"] = await test_message_broadcaster()
        results["connection_manager"] = await test_connection_manager_subscription()
        results["chat_processor"] = await test_chat_processor_integration()
        results["listener_thread"] = await test_redis_listener_thread()
        results["end_to_end"] = await test_end_to_end_message_flow()
        
        return results
    
    try:
        results = asyncio.run(run_diagnosis())
        
        # 总结诊断结果
        print("\n" + "=" * 60)
        print("📊 诊断结果总结:")
        
        all_passed = True
        for test_name, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"  {test_name}: {status}")
            if not passed:
                all_passed = False
        
        print("\n🎯 问题分析:")
        if not results.get("redis_config"):
            print("  ❌ Redis 配置问题 - 检查配置文件中的 Redis 设置")
        
        if not results.get("redis_connection"):
            print("  ❌ Redis 连接问题 - 检查 Redis 服务是否运行，网络是否可达")
        
        if not results.get("message_broadcaster"):
            print("  ❌ 消息广播器问题 - 检查 Redis pub/sub 功能")
        
        if not results.get("connection_manager"):
            print("  ❌ 连接管理器订阅问题 - 检查频道订阅逻辑")
        
        if not results.get("listener_thread"):
            print("  ❌ 监听线程问题 - 检查线程启动和运行状态")
        
        if not results.get("end_to_end"):
            print("  ❌ 端到端消息流问题 - 检查完整的消息传递链路")
        
        if all_passed:
            print("  🎉 所有测试都通过了！Redis 消息接收功能正常")
        else:
            print("  🔧 需要修复上述问题才能正常接收 Redis 消息")
        
        return all_passed
        
    except Exception as e:
        print(f"\n💥 诊断过程中出现异常: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 诊断被用户中断")
        sys.exit(1)
