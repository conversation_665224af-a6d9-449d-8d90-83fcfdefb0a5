# OpenSearch 模块重构计划 (plan.md)

## 1. 现状与问题分析

当前 `src/infrastructure/opensearch` 模块主要包含两个文件：`BaseRequest.py` 和 `OpenSearchLLMClient.py`。它们共同构成了与阿里云 OpenSearch LLM 版的交互层，但存在以下几个核心问题：

1.  **代码严重重复**: `BaseRequest.py` 中的 `_request` (同步) 和 `async_request` (异步) 方法有超过80%的逻辑是完全相同的，包括运行时配置、重试逻辑、请求头构建、认证签名等。这违反了 DRY (Don't Repeat Yourself) 原则。

2.  **职责边界模糊**:
    *   `BaseRequest.py` 应该只负责底层协议和认证，但包含了过多的实现细节。
    *   `OpenSearchLLMClient.py` 本应是高层业务客户端，但其 `stream_multi_search` 方法却绕过了 `BaseRequest.py`，**重新实现了一遍完整的签名和请求逻辑**，并引入了 `aiohttp`，与 `BaseRequest.py` 使用的 `Tea.core` 技术栈不一致。这是最严重的架构问题。

3.  **单一方法过长**: `BaseRequest.py` 中的两个核心方法都超过了100行，将请求构建、重试、签名、响应解析等多个职责耦合在一起，难以阅读和维护。

4.  **可测试性差**: 由于职责耦合，很难对签名逻辑、重试逻辑或请求构建逻辑进行独立的单元测试。

## 2. 重构目标

本次重构旨在解决上述问题，达成以下目标：

1.  **单一职责原则 (SRP)**:
    *   建立一个纯粹的、底层的 `AliyunOpenSearchClient`，只负责处理与阿里云 OpenSearch 服务端通信的协议细节（如签名、重试、流式处理）。
    *   让 `OpenSearchLLMClient` 聚焦于高层业务逻辑，如构建特定API的请求体、解析业务相关的响应数据。

2.  **代码复用 (DRY)**: 消除所有重复的请求构建和签名逻辑。统一使用一套底层客户端。

3.  **技术栈统一**: 移除 `aiohttp` 的直接使用，统一通过封装好的底层客户端处理HTTP请求，无论是常规请求还是流式请求。

4.  **提升可测试性**: 能够独立测试底层客户端的签名和连接能力，以及高层客户端的业务逻辑。

5.  **提升可维护性**: 代码结构更清晰，新功能（如调用另一个OpenSearch API）的开发将变得更容易。

## 3. 核心设计方案

我们将引入清晰的两层结构：

-   **底层: `aliyun_opensearch_basic_client.py`** (替代 `BaseRequest.py`)
-   **高层: `aliyun_opensearch_llm_client.py`** (重构当前的 `OpenSearchLLMClient.py`)

---

### 3.1. 底层: `AliyunOpenSearchBasicClient`

这个类将是与 OpenSearch API 进行底层通信的唯一入口。

**职责**:
-   管理 AK/SK 等认证凭证。
-   封装 HTTP 请求的构建、签名和发送。
-   统一处理常规请求和流式 (Streaming) 请求。
-   内置重试和退避策略。

**核心方法签名 (示例)**:
```python
class AliyunOpenSearchBasicClient:
    def __init__(self, config: Config):
        # ... 初始化凭证和 endpoint

    async def execute_request(
        self,
        method: str,
        pathname: str,
        query: Dict,
        body: Dict,
        stream: bool = False
    ) -> AsyncGenerator[Dict, None]:
        # 1. 构建 TeaRequest (私有方法 _build_request)
        # 2. 签名 (私有方法 _sign_request)
        # 3. 使用 TeaCore.async_do_action 执行
        # 4. 如果 stream=True，则处理流式响应
        # 5. 如果 stream=False，则一次性返回结果
        # 6. 包含完整的重试逻辑
```

### 3.2. 高层: `OpenSearchLLMClient`

这个类将作为面向业务（`Exe` 层）的客户端，它只关心业务逻辑，不关心通信细节。

**职责**:
-   依赖注入 `AliyunOpenSearchBasicClient`。
-   提供具体的业务方法，如 `search_major_info`、`stream_incremental_search` 等。
-   构建符合 OpenSearch LLM 版业务API规范的 `body`。
-   调用底层 `AliyunOpenSearchBasicClient` 的 `execute_request` 方法。
-   解析和处理业务返回的数据，例如从流式数据中提取增量内容。

**重构后的 `stream_incremental_search` 示例**:
```python
class AliyunOpenSearchLLMClient:
    def __init__(self, config: Config):
        self._client = AliyunOpenSearchBasicClient(config)

    async def stream_incremental_search(self, app_name: str, body: Dict) -> AsyncGenerator[str, None]:
        # 1. 准备 pathname 和 query
        pathname = f"/v3/openapi/apps/{app_name}/actions/multi-search"
        
        # 2. 调用底层客户端，获取原始流式响应
        async for raw_chunk in self._client.execute_request("POST", pathname, query={}, body=body, stream=True):
            # 3. 在这里实现解析增量内容的业务逻辑
            #    (这部分现有逻辑可以保留)
            if raw_chunk ...:
                yield incremental_content
```
**关键变化**: `aiohttp` 相关代码被完全移除，签名和请求发送都委托给了底层客户端。

## 4. 实施步骤

为保证平滑过渡和风险可控，建议遵循以下步骤：

1.  **创建 `plan.md`**: ✅ **已完成** - 明确重构目标和方案，并进行评审。

2.  **创建新文件 `aliyun_opensearch_basic_client.py`**: ✅ **已完成**
    *   ✅ 在 `src/infrastructure/opensearch/` 目录下创建新文件。
    *   ✅ 实现新的 `AliyunOpenSearchBasicClient` 和 `Config` 类。
    *   ✅ 将 `BaseRequest.py` 中的逻辑（去重后）迁移至此，并进行优化，统一 `_request` 和 `async_request`。
    *   ⏳ 为新实现的客户端编写单元测试，重点测试签名和请求构建逻辑。

3.  **创建新文件 `aliyun_opensearch_llm_client.py`**: ✅ **已完成**
    *   ✅ 创建新的高层业务客户端，使用依赖注入模式。
    *   ✅ 重写 `searchDoc`, `multi_search`, `stream_multi_search` 等方法，将请求发送逻辑委托给 `AliyunOpenSearchBasicClient`。
    *   ✅ **移除所有 `aiohttp` 和手动构造签名的代码**。
    *   ✅ 提供向后兼容的别名 `OpenSearchLLMClient`。

4.  **更新调用方 (`Exe` 层)**: ✅ **已完成**
    *   ✅ 修改所有 `Exe` 层文件的导入语句，切换到新的客户端。
    *   ✅ 更新 `Config` 类的导入路径。
    *   ✅ 保持原有的使用方式不变，确保向后兼容。

5.  **集成测试**: 运行所有依赖 OpenSearch 的业务流程（如专业查询、志愿填报等），确保功能与重构前完全一致。

6.  **代码清理**:
    *   在所有测试通过后，安全删除 `BaseRequest.py` 文件。
    *   进行最终的代码审查。

7.  **合并**: 将重构后的代码合并到主开发分支。

## 5. 文件结构变化

**重构前**:
```
src/infrastructure/opensearch/
├── __init__.py
├── BaseRequest.py          # -> 将被删除
└── OpenSearchLLMClient.py    # -> 将被重构
```

**重构后**:
```
src/infrastructure/opensearch/
├── __init__.py
├── aliyun_opensearch_basic_client.py # (新) 底层协议客户端
└── aliyun_opensearch_llm_client.py   # (新) 高层业务客户端
```

## 6. 风险评估

-   **风险等级**: **中等**。该模块是核心查询功能的底层依赖，影响广泛。
-   **风险缓解措施**:
    1.  **并行开发**: 在不删除旧代码的情况下开发新模块。
    2.  **充分测试**: 必须有单元测试和覆盖所有业务场景的集成测试。
    3.  **分步上线**: 如果条件允许，可以考虑使用特性开关进行灰度发布。 