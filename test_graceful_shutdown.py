#!/usr/bin/env python3
"""
优雅下线功能测试脚本

验证 ConnectionManager 和 SessionManager 的优雅下线功能。
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_connection_manager_graceful_shutdown():
    """测试连接管理器优雅下线"""
    print("🔍 测试连接管理器优雅下线...")
    
    try:
        from src.infrastructure.session import ConnectionManager
        
        async def test_shutdown():
            manager = ConnectionManager()
            
            # 创建一些连接
            connections = []
            for i in range(3):
                conn = await manager.add_connection(f"conv_{i}", f"user_{i}")
                connections.append(conn)
            
            # 验证连接已创建
            stats = await manager.get_connection_stats()
            assert stats["total_connections"] == 3, "应该有3个连接"
            
            # 执行优雅下线
            await manager.close_all_connections()
            
            # 验证所有连接已关闭
            final_stats = await manager.get_connection_stats()
            assert final_stats["total_connections"] == 0, "所有连接应该被关闭"
            assert final_stats["total_conversations"] == 0, "所有会话映射应该被清理"
            
            print("    ✅ 连接管理器优雅下线验证通过")
        
        asyncio.run(test_shutdown())
        return True
        
    except Exception as e:
        print(f"❌ 连接管理器优雅下线测试失败: {e}")
        traceback.print_exc()
        return False


def test_session_manager_graceful_shutdown():
    """测试会话管理器优雅下线"""
    print("\n🔍 测试会话管理器优雅下线...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_shutdown():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建模拟处理器
            async def mock_processor(request, user_id, conversation_id):
                for i in range(5):
                    await asyncio.sleep(0.1)
                    yield f"Step {i+1}"
                yield "Complete"
            
            # 创建连接和任务
            connection = await connection_manager.add_connection("test_conv", "test_user")
            request = ChatInputVO(
                conversation_id="test_conv",
                messages=[MessageVO(text="test", role="user")]
            )
            
            # 提交任务
            await session_manager.submit_session_task(
                request, "test_user", connection.connection_id, mock_processor
            )
            
            # 等待任务开始
            await asyncio.sleep(0.1)
            
            # 验证任务正在运行
            stats = await session_manager.get_manager_stats()
            assert stats["active_conversations"] > 0, "应该有活跃会话"
            
            # 执行优雅下线
            await session_manager.shutdown()
            
            # 验证所有任务已清理
            final_stats = await session_manager.get_manager_stats()
            assert final_stats["active_conversations"] == 0, "所有活跃会话应该被清理"
            assert final_stats["total_queues"] == 0, "所有队列应该被清理"
            
            # 清理连接管理器
            await connection_manager.close_all_connections()
            
            print("    ✅ 会话管理器优雅下线验证通过")
        
        asyncio.run(test_shutdown())
        return True
        
    except Exception as e:
        print(f"❌ 会话管理器优雅下线测试失败: {e}")
        traceback.print_exc()
        return False


def test_task_cancellation():
    """测试任务取消功能"""
    print("\n🔍 测试任务取消功能...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_cancellation():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建长时间运行的处理器
            async def long_processor(request, user_id, conversation_id):
                try:
                    for i in range(100):  # 很长的任务
                        await asyncio.sleep(0.1)
                        yield f"Long step {i+1}"
                    yield "Long complete"
                except asyncio.CancelledError:
                    yield "Task cancelled"
                    raise
            
            # 创建连接和任务
            connection = await connection_manager.add_connection("long_conv", "test_user")
            request = ChatInputVO(
                conversation_id="long_conv",
                messages=[MessageVO(text="long task", role="user")]
            )
            
            # 提交长任务
            await session_manager.submit_session_task(
                request, "test_user", connection.connection_id, long_processor
            )
            
            # 等待任务开始
            await asyncio.sleep(0.2)
            
            # 验证任务正在运行
            stats = await session_manager.get_manager_stats()
            assert stats["active_conversations"] > 0, "应该有活跃会话"
            
            # 取消特定会话的任务
            await session_manager.cancel_conversation_tasks("long_conv")
            
            # 验证任务已取消
            await asyncio.sleep(0.1)
            final_stats = await session_manager.get_manager_stats()
            assert final_stats["active_conversations"] == 0, "会话应该被取消"
            
            # 清理
            await connection_manager.close_all_connections()
            await session_manager.shutdown()
            
            print("    ✅ 任务取消功能验证通过")
        
        asyncio.run(test_cancellation())
        return True
        
    except Exception as e:
        print(f"❌ 任务取消功能测试失败: {e}")
        traceback.print_exc()
        return False


def test_ai_chat_service_graceful_shutdown():
    """测试 ai_chat_service 优雅下线"""
    print("\n🔍 测试 ai_chat_service 优雅下线...")
    
    try:
        from src.app.ai_chat_service import graceful_shutdown, get_service_health
        
        async def test_service_shutdown():
            # 获取初始健康状态
            health = await get_service_health()
            assert health["status"] == "healthy", "服务应该是健康的"
            
            print(f"    初始健康状态: {health}")
            
            # 执行优雅下线
            await graceful_shutdown()
            
            # 验证下线后的状态
            final_health = await get_service_health()
            print(f"    下线后状态: {final_health}")
            
            print("    ✅ ai_chat_service 优雅下线验证通过")
        
        asyncio.run(test_service_shutdown())
        return True
        
    except Exception as e:
        print(f"❌ ai_chat_service 优雅下线测试失败: {e}")
        traceback.print_exc()
        return False


def test_timeout_handling():
    """测试超时处理"""
    print("\n🔍 测试超时处理...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_timeout():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建永不结束的处理器
            async def infinite_processor(request, user_id, conversation_id):
                try:
                    i = 0
                    while True:
                        await asyncio.sleep(0.1)
                        yield f"Infinite step {i}"
                        i += 1
                except asyncio.CancelledError:
                    yield "Infinite task cancelled"
                    raise
            
            # 创建连接和任务
            connection = await connection_manager.add_connection("infinite_conv", "test_user")
            request = ChatInputVO(
                conversation_id="infinite_conv",
                messages=[MessageVO(text="infinite task", role="user")]
            )
            
            # 提交无限任务
            await session_manager.submit_session_task(
                request, "test_user", connection.connection_id, infinite_processor
            )
            
            # 等待任务开始
            await asyncio.sleep(0.2)
            
            # 模拟带超时的等待
            from src.app.ai_chat_service import _wait_for_active_tasks
            
            start_time = time.time()
            await _wait_for_active_tasks(timeout=1.0)  # 1秒超时
            elapsed = time.time() - start_time
            
            # 验证超时处理
            assert elapsed >= 1.0, "应该等待至少1秒"
            assert elapsed < 2.0, "不应该等待超过2秒"
            
            # 清理
            await session_manager.shutdown()
            await connection_manager.close_all_connections()
            
            print("    ✅ 超时处理验证通过")
        
        asyncio.run(test_timeout())
        return True
        
    except Exception as e:
        print(f"❌ 超时处理测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 优雅下线功能测试")
    print("=" * 60)
    
    success = True
    
    # 运行测试
    success &= test_connection_manager_graceful_shutdown()
    success &= test_session_manager_graceful_shutdown()
    success &= test_task_cancellation()
    success &= test_ai_chat_service_graceful_shutdown()
    success &= test_timeout_handling()
    
    # 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有优雅下线功能测试都通过了！")
        print("\n📊 优雅下线特性:")
        print("  ✅ ConnectionManager 优雅关闭所有连接")
        print("  ✅ SessionManager 优雅关闭所有会话")
        print("  ✅ 任务取消机制")
        print("  ✅ 超时处理")
        print("  ✅ 资源清理")
        print("  ✅ 健康状态监控")
        print("\n🎯 优雅下线流程:")
        print("  1. 停止接受新连接/任务")
        print("  2. 等待当前任务完成（带超时）")
        print("  3. 取消剩余任务")
        print("  4. 清理所有资源")
        print("  5. 关闭管理器")
        print("\n💡 实际应用:")
        print("  - 服务重启时保证数据完整性")
        print("  - 避免任务中断导致的数据丢失")
        print("  - 优雅处理系统关闭信号")
        print("  - 支持零停机部署")
    else:
        print("❌ 优雅下线功能测试失败，需要进一步修复")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        traceback.print_exc()
        sys.exit(1)
