# AI智能志愿填报 - 专业推荐方案

## 产品背景与定义

智能志愿填报系统旨在通过AI技术，帮助高考学生根据个人情况和职业发展方向，科学合理地进行志愿填报决策。本方案主要聚焦于系统中的**专业推荐**核心功能模块。

## 产品目标

1. 降低学生志愿填报决策难度，提供个性化专业推荐
2. 结合用户分数、兴趣特点和就业愿景，提供科学的专业匹配
3. 提供清晰的专业信息，帮助用户了解各专业特点及就业前景
4. 根据历史录取数据，提高志愿填报的成功率

## 用户画像构建

### 数据收集维度

| 维度 | 收集方式 | 数据用途 |
|------|---------|--------|
| 基础分数信息 | 表单填写 | 筛选可报考专业范围 |
| 兴趣特点 | 兴趣测评问卷 | 匹配适合的专业方向 |
| 能力倾向 | 能力测评问卷 | 评估专业学习适配度 |
| 就业愿景 | 结构化对话收集 | 匹配就业前景契合度 |
| 填报策略偏好 | OpenSearch智能问答 | 确定推荐策略权重 |

## 技术架构

### 数据源

1. **用户输入数据**
   - 分数信息
   - 兴趣测评结果
   - 对话中收集的就业愿景

2. **系统数据**
   - PG数据库: major_admission_score（高考专业录取分数线表）
   - PG数据库: major_info（专业信息表）
   - OpenSearch: 填报策略知识库

3. **大模型**
   - 负责对话交互和专业推荐决策

### 系统流程

```
用户基础信息 --> 就业愿景收集 --> 填报策略确认 --> 数据整合处理 --> 大模型分析 --> 专业推荐输出
```

## 功能设计

### 1. 用户信息收集模块

#### 1.1 基础信息收集
- 高考分数
- 考试科目组合
- 生源地区
- 专业倾向范围

#### 1.2 就业愿景对话收集
采用结构化对话方式，引导用户表达：
- 期望的工作环境（室内/户外/混合）
- 偏好的工作性质（研究型/技术型/管理型/创意型）
- 期望薪资范围
- 工作地点偏好
- 行业偏好

示例对话引导：
```
系统: 您期望未来从事什么类型的工作环境？室内工作、户外工作，还是两者兼有？
用户: 我比较喜欢室内工作环境。
系统: 理解了。在室内工作环境中，您更偏好研究型、技术型、管理型还是创意型的工作内容？
用户: 我对技术型和创意型都比较感兴趣。
系统: 您考虑过未来希望在哪些城市或地区工作吗？
...
```

#### 1.3 填报策略智能问答
通过OpenSearch实现的LLM问答系统，帮助用户确定填报策略的倾向：
- 冲稳保策略比例
- 专业优先vs学校优先
- 就业导向vs兴趣导向
- 地域限制条件

### 2. 专业匹配与推荐引擎

#### 2.1 多维度匹配算法
综合考虑以下因素进行专业推荐：
- 分数适配度：用户分数与专业历史分数线的匹配程度
- 兴趣适配度：用户兴趣特点与专业特性的契合度
- 能力适配度：用户能力测评结果与专业能力要求的匹配度
- 就业契合度：专业就业方向与用户就业愿景的一致性

#### 2.2 推荐策略权重调整
根据用户填报策略偏好，动态调整各维度的权重：
- 就业导向：提高就业契合度权重
- 兴趣导向：提高兴趣适配度权重
- 稳妥策略：提高分数适配度权重

### 3. 专业信息展示模块

为每个推荐专业提供以下信息：
- 专业基本介绍
- 主要课程内容
- 就业方向及前景
- 相关院校推荐
- 历年录取分数线趋势
- 专业录取难度评估

## 数据流设计

### 输入数据结构

```json
{
  "user_info": {
    "score": 650,
    "subjects": ["物理", "化学", "生物"],
    "province": "北京"
  },
  "career_vision": {
    "work_environment": "室内",
    "work_type": ["技术型", "创意型"],
    "salary_expectation": "10k-15k",
    "location_preference": ["北京", "上海", "杭州"],
    "industry_preference": ["IT", "设计", "金融"]
  },
  "strategy": {
    "priority": "专业优先",
    "orientation": "就业导向",
    "risk_preference": "稳妥"
  }
}
```

### 输出数据结构

```json
{
  "recommended_majors": [
    {
      "name": "计算机科学与技术",
      "match_score": 92,
      "match_reasons": ["分数适合", "兴趣匹配", "就业前景好"],
      "admission_scores": {
        "min": 630,
        "max": 680,
        "avg": 645
      },
      "career_prospects": ["软件工程师", "算法工程师", "系统架构师"],
      "recommended_schools": [
        {"name": "北京大学", "admission_probability": "中等"},
        {"name": "上海交通大学", "admission_probability": "较高"}
      ]
    },
    // 更多专业推荐...
  ],
  "recommendation_explanation": "根据您的兴趣偏好和就业愿景，系统推荐了偏向技术创新类的专业，且所选专业在您的目标城市有较好的就业市场..."
}
```

## 用户界面设计建议

### 1. 专业推荐列表页

- 以卡片形式展示推荐专业
- 每个卡片包含专业名称、匹配度、推荐理由摘要
- 支持按匹配度、录取难度、就业前景等维度排序
- 提供标签筛选功能（如按学科门类、就业方向筛选）

### 2. 专业详情页

- 专业介绍与课程内容
- 能力要求与学习难度评估
- 就业方向与薪资水平统计
- 推荐院校列表（按录取可能性排序）
- 历年录取分数线走势图
- 相关专业推荐

### 3. 专业比较功能

- 支持多个专业并排比较
- 多维度数据可视化对比
- 提供决策建议

## 实施路径

### 第一阶段：基础功能实现
- 完成用户信息收集表单
- 实现基于分数的专业筛选
- 接入专业数据库

### 第二阶段：智能推荐增强
- 实现就业愿景对话收集
- 开发多维度匹配算法
- 接入OpenSearch填报策略问答

### 第三阶段：体验优化
- 完善专业信息展示
- 优化推荐算法
- 添加专业比较功能
- A/B测试和用户反馈收集

## 评估指标

- 用户满意度：推荐专业的用户接受率
- 推荐质量：用户最终选择在推荐列表中的比例
- 交互效率：完成专业推荐流程的平均时间
- 转化率：试用到付费的转化比例

## 风险与应对

| 风险 | 应对措施 |
|-----|--------|
| 专业数据不完整 | 建立专业数据完善机制，定期更新 |
| 用户信息不充分 | 优化对话设计，提高信息收集效率 |
| 推荐结果不准确 | 建立反馈机制，持续优化推荐算法 |
| 用户期望过高 | 明确产品定位，管理用户预期 |

## 结论

专业推荐是AI志愿填报产品的核心功能，通过结合用户基础信息、就业愿景和填报策略，利用大模型分析能力，可以为用户提供个性化、科学的专业推荐，帮助用户做出更明智的志愿填报决策。
