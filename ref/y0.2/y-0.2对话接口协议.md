# Y-0.2 对话接口协议

基于引导式对话——就业导向的交互样式和 DeepChat 前端组件，设计的前后端 API 交互协议。

## 1. 对话初始化

*   **目的**: 前端加载页面时调用，获取初始对话内容和可选项。
*   **请求**:
    *   `URL`: `/api/chat/init`
    *   `方法`: `GET`
    *   `请求体`: 无
*   **响应**:
    *   `状态码`: `200 OK`
    *   `响应体 (JSON)`:
        ```json
        {
          "messages": [
            {
              "role": "ai", // 消息角色，可以是 ai 或 user
              "text": "您好！我们可以一起探索您的职业发展方向。请选择您感兴趣的方面：", // 消息文本
              "options": [ // 引导用户选择的选项列表
                {"type": "category_select", "value": "work_environment", "label": "工作环境氛围"},
                {"type": "category_select", "value": "achievement_feedback", "label": "成就反馈机制"},
                {"type": "category_select", "value": "team_role", "label": "团队角色定位"},
                {"type": "value_emphasis", "value": "value_emphasis", "label": "价值实现侧重"}
              ]
            }
          ],
          "currentState": { // 当前对话状态，用于前端更新UI，例如图片中的圆点状态
             "step": 1,
             "work_environment": "inactive", // 状态可以是 inactive, active, completed, selected 等
             "achievement_feedback": "inactive",
             "team_role": "inactive",
             "value_emphasis": "inactive"
          }
        }
        ```
    *   **说明**: `messages` 字段包含 DeepChat 需要渲染的初始消息。`options` 是自定义字段，用于表示后端提供的、用户可以选择的选项。前端需要解析 `options` 并将其渲染为交互元素（例如按钮或链接）。`currentState` 字段包含整个对话的状态信息，前端可以根据这个信息更新除了对话消息之外的 UI 部分，比如图片中的圆点。

## 2. 用户发送消息/选择选项

*   **目的**: 用户输入文本或选择一个选项后，前端将用户的行为发送给后端。
*   **请求**:
    *   `URL`: `/api/chat/send` (或者可以根据是否选择选项设计不同的URL)
    *   `方法`: `POST`
    *   `请求体 (JSON)`:
        ```json
        {
          "type": "text", // 用户输入的类型，可以是 text 或 selection
          "content": "用户输入的文本内容", // 或用户选择的选项的 value，例如 "work_environment"
          "currentContext": { // 包含当前的对话ID、之前的对话轮次或其他必要上下文
            "dialogId": "...",
            "step": 1
          }
        }
        ```
*   **响应**:
    *   `状态码`: `200 OK`
    *   `响应体 (JSON)`:
        ```json
        {
          "messages": [
            {
              "role": "user",
              "text": "我选择了工作环境氛围"
            },
            {
              "role": "ai",
              "text": "好的，关于工作环境氛围，您更倾向于哪种？例如：规范稳定清晰，还是灵活创新协作？",
              "options": [
                 {"type": "environment_select", "value": "stable_clear", "label": "规范稳定清晰"},
                 {"type": "environment_select", "value": "flexible_innovative", "label": "灵活创新协作"}
              ]
            }
          ],
          "currentState": { // 更新后的对话状态
             "step": 2,
             "work_environment": "selected",
             "achievement_feedback": "inactive",
             "team_role": "inactive",
             "value_emphasis": "inactive"
             // 可能包含更细致的状态，比如在工作环境氛围下的具体选项状态
          },
          "dialogId": "..." // 如果是首次请求，后端返回对话ID
        }
        ```
    *   **说明**: 响应包含后端处理后的新消息列表以及更新后的对话状态。前端将新消息添加到 DeepChat 中，并根据 `currentState` 更新 UI。`dialogId` 用于后续同一轮对话的请求。

## 3. 推荐专业选择与确认

*   **目的**: 后端根据用户之前的选择推荐专业列表，前端展示并允许用户确认或修改选择，然后将最终选择发送回后端。

*   **当后端推荐专业列表时（作为对用户前一步操作的响应）**:
    *   这会是 `/api/chat/send` 接口响应的一部分。
    *   `响应体 (JSON)` 将在 `messages` 字段中包含一个特定类型的消息，该消息包含推荐的专业列表和它们的状态。
        ```json
        {
          "messages": [
            // ... 可能包含用户前一步的消息 ...
            {
              "role": "ai",
              "text": "根据我们刚才对您职业偏好和各项维度的深入分析，您非常适合那些需要长期投入、深耕专业、追求深度成就的领域。结合您注重确定性、深耕专业、偏好长期积累与核心主导角色的特质，我为您推荐以下几个可能非常契合的志愿填报专业，供您参考，您可以点击按钮进行选择：",
              "type": "profession_recommendation", // 新增消息类型标识
              "selectableItems": [ // 推荐的专业列表，可多选
                {"value": "physics", "label": "1. 物理学", "status": "selected"}, // status 可以是 selected, unselected, rejected
                {"value": "cs_ai", "label": "2. 计算机科学与技术（人工智能/算法方向）", "status": "selected"},
                {"value": "biomedical_engineering", "label": "3. 生物医学工程", "status": "selected"},
                {"value": "pharmacy", "label": "4. 药学", "status": "rejected"}, // 示例：表示不推荐或用户已排除
                {"value": "materials_science", "label": "5. 材料科学与工程", "status": "selected"},
                {"value": "energy_power_engineering", "label": "6. 能源与动力工程", "status": "selected"}
              ],
              "confirmAction": { // 确认按钮的信息
                "type": "confirm_selection",
                "label": "确认"
              }
            }
          ],
          "currentState": { // 更新后的对话状态，反映当前所处的步骤
             "step": 3,
             // ... 其他状态 ...
          }
        }
        ```
    *   **说明**: `type: "profession_recommendation"` 标识这是一个专业推荐消息。`selectableItems` 数组包含每个推荐专业的详细信息，包括 `value`（用于后端识别）、`label`（前端显示文本）和 `status`（初始选中状态）。`confirmAction` 字段指示前端渲染一个确认按钮，并在用户点击时触发相应的发送请求。

*   **用户确认专业选择时**:
    *   这会是向 `/api/chat/send` 接口发送的请求。
    *   `请求体 (JSON)`:
        ```json
        {
          "type": "confirm_selection", // 用户操作类型，与 confirmAction.type 对应
          "content": ["physics", "cs_ai", "biomedical_engineering", "materials_science", "energy_power_engineering"], // 用户最终选择的专业 value 列表
          "currentContext": { // 包含当前的对话ID和其他必要上下文
            "dialogId": "...",
            "step": 3 // 当前所处的步骤
          }
        }
        ```
    *   **响应**: 后端处理用户选择后，返回包含下一轮对话内容和状态的响应，格式与步骤 2 的响应类似。

## API 协议设计考虑事项:

1.  **状态管理**: 对话的状态（用户走到哪一步，选择了什么）由后端管理，前端通过 `currentState` 字段获取状态信息并更新 UI。
2.  **选项表示**: 使用 `options` 字段传递用户可选择的选项，前端根据 `type` 和 `label` 渲染不同的交互元素。用户选择后，将 `value` 发送给后端。
3.  **DeepChat 集成**: 响应中的 `messages` 字段直接符合 DeepChat 的消息格式（包含 `role` 和 `text`）。前端需要监听用户输入事件，并将用户输入或选项选择封装成请求发送给后端，然后将后端返回的 `messages` 添加到 DeepChat 实例中。
4.  **错误处理**: 增加错误响应格式，例如 `{"code": 500, "message": "服务器内部错误"}`，并在前端进行相应的提示。
5.  **安全性**: 对于实际应用，需要考虑用户认证、请求加密等安全措施。

这份协议提供了一个基础框架，可以根据实际业务需求进一步细化 `currentState` 的结构、`options` 的类型和内容，以及增加更多 API 端点（例如用于获取历史对话记录等）。

## API 协议设计考虑事项 (补充):

*   **消息类型 (`type`)**: 在 `messages` 字段中引入 `type` 字段，用于区分不同展示和交互形式的消息（如纯文本、带选项、带专业列表等），便于前端进行差异化渲染。
*   **用户操作类型 (`type`)**: 在用户发送的请求体中，`type` 字段明确标识用户执行了哪种操作（如发送文本、选择类别、确认专业等）。

这份补充完善了对话流程中关于专业推荐环节的接口协议设计。 