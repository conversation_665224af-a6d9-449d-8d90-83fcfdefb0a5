app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 循环测试-实验1
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/anthropic:0.0.13@3c4d4e14652baa9ce1f10e934da9995b3cde0f2ec94ee6638b73fecf1ed53288
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.1.5@012c9e0467a11910db974e0436348e93a376fdc96381946a3db2c56708377381
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables:
  - description: ''
    id: c6d8f868-3378-4791-83ee-181c4c574f48
    name: answer
    selector:
    - env
    - answer
    value: ' '
    value_type: string
  - description: ''
    id: e2277ef8-7769-4c30-b5e8-80ba320891d6
    name: fmpkey
    selector:
    - env
    - fmpkey
    value: oSBkR9vIQ7Au2O7gFjfxIfg8VQ3vrcyR
    value_type: string
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: loop-start
        targetType: llm
      id: 1745736592974start-source-1745736635063-target
      selected: false
      source: 1745736592974start
      sourceHandle: source
      target: '1745736635063'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: llm
        targetType: tool
      id: 1745736635063-source-1745736689919-target
      selected: false
      source: '1745736635063'
      sourceHandle: source
      target: '1745736689919'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: template-transform
        targetType: llm
      id: 1745736788778-source-1745736973218-target
      selected: false
      source: '1745736788778'
      sourceHandle: source
      target: '1745736973218'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: llm
        targetType: assigner
      id: 1745736973218-source-1745737138928-target
      selected: false
      source: '1745736973218'
      sourceHandle: source
      target: '1745737138928'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: tool
        targetType: template-transform
      id: 1745736689919-source-1745736788778-target
      selected: false
      source: '1745736689919'
      sourceHandle: source
      target: '1745736788778'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17457340486300-source-1745736592974-target
      source: '17457340486300'
      sourceHandle: source
      target: '1745736592974'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 17457665814220-source-1745766730888-target
      source: '17457665814220'
      sourceHandle: source
      target: '1745766730888'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17457341467330-source-17458542648830-target
      source: '17457341467330'
      sourceHandle: source
      target: '17458542648830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: llm
        targetType: tool
      id: 1745854264883017458542648830-source-1745854264883017458542648841-target
      source: '1745854264883017458542648830'
      sourceHandle: source
      target: '1745854264883017458542648841'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: tool
        targetType: template-transform
      id: 1745854264883017458542648841-source-1745854264883017458542648842-target
      source: '1745854264883017458542648841'
      sourceHandle: source
      target: '1745854264883017458542648842'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: template-transform
        targetType: llm
      id: 1745854264883017458542648842-source-1745854264883017458542648843-target
      source: '1745854264883017458542648842'
      sourceHandle: source
      target: '1745854264883017458542648843'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: llm
        targetType: assigner
      id: 1745854264883017458542648843-source-1745854264883017458542648844-target
      source: '1745854264883017458542648843'
      sourceHandle: source
      target: '1745854264883017458542648844'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: loop-start
        targetType: llm
      id: 1745854264883start-source-1745854264883017458542648830-target
      source: 1745854264883start
      sourceHandle: source
      target: '1745854264883017458542648830'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17457361886690-source-17458554196650-target
      source: '17457361886690'
      sourceHandle: source
      target: '17458554196650'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: loop-start
        targetType: llm
      id: 1745855419665start-source-1745855419665017458554196650-target
      source: 1745855419665start
      sourceHandle: source
      target: '1745855419665017458554196650'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: llm
        targetType: tool
      id: 1745855419665017458554196650-source-1745855419665017458554196651-target
      source: '1745855419665017458554196650'
      sourceHandle: source
      target: '1745855419665017458554196651'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: tool
        targetType: template-transform
      id: 1745855419665017458554196651-source-1745855419665017458554196652-target
      source: '1745855419665017458554196651'
      sourceHandle: source
      target: '1745855419665017458554196652'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: template-transform
        targetType: llm
      id: 1745855419665017458554196652-source-1745855419665017458554196653-target
      source: '1745855419665017458554196652'
      sourceHandle: source
      target: '1745855419665017458554196653'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: llm
        targetType: assigner
      id: 1745855419665017458554196653-source-1745855419665017458554196654-target
      source: '1745855419665017458554196653'
      sourceHandle: source
      target: '1745855419665017458554196654'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17457361931830-source-17458560149760-target
      source: '17457361931830'
      sourceHandle: source
      target: '17458560149760'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: loop-start
        targetType: llm
      id: 1745856014976start-source-1745856014976017458560149760-target
      source: 1745856014976start
      sourceHandle: source
      target: '1745856014976017458560149760'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: llm
        targetType: tool
      id: 1745856014976017458560149760-source-1745856014976017458560149761-target
      source: '1745856014976017458560149760'
      sourceHandle: source
      target: '1745856014976017458560149761'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: tool
        targetType: template-transform
      id: 1745856014976017458560149761-source-1745856014976017458560149762-target
      source: '1745856014976017458560149761'
      sourceHandle: source
      target: '1745856014976017458560149762'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: template-transform
        targetType: llm
      id: 1745856014976017458560149762-source-1745856014976017458560149763-target
      source: '1745856014976017458560149762'
      sourceHandle: source
      target: '1745856014976017458560149763'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: llm
        targetType: assigner
      id: 1745856014976017458560149763-source-1745856014976017458560149764-target
      source: '1745856014976017458560149763'
      sourceHandle: source
      target: '1745856014976017458560149764'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 17458542648830-source-17457361931830-target
      source: '17458542648830'
      sourceHandle: source
      target: '17457361931830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 17458554196650-source-17457361931830-target
      source: '17458554196650'
      sourceHandle: source
      target: '17457361931830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 1745736592974-source-17457665814220-target
      source: '1745736592974'
      sourceHandle: source
      target: '17457665814220'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 17458560149760-source-17457340486300-target
      source: '17458560149760'
      sourceHandle: source
      target: '17457340486300'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1738727589378-source-17464144818210-target
      source: '1738727589378'
      sourceHandle: source
      target: '17464144818210'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 17464144818210-source-1746414618239-target
      source: '17464144818210'
      sourceHandle: source
      target: '1746414618239'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types:
          - image
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: Profile
          max_length: 200000
          options: []
          required: false
          type: paragraph
          variable: Profile
      height: 90
      id: '1738727589378'
      position:
        x: -1353.8222152060666
        y: 1086.253817017675
      positionAbsolute:
        x: -1353.8222152060666
        y: 1086.253817017675
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在请进行第四部分的分析，主要关注内容如下：\n\
            \n### 4. 后续发展规划（短期 & 中长期）\n\n1. **大学阶段规划**  \n   - 指导学生如何利用大学资源（课程、社团、科研项目、产学研合作、实习等），尤其是已经选定的专业与院校。\
            \  \n   - 提醒家长关注和支持学生在校期间的心态和学费等财务规划。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **短期（3~5\
            \ 年）职业发展**  \n   - 对毕业后的主流去向（企业就业、考研、创业、留学等）进行评估；  \n   - 根据学生和家长关注点，给出相关资料、经验或成功案例。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **中长期（5~10 年）前景**  \n   - 由未来学家、经济学家、产业顾问讨论行业变革、城市升级、国际竞争态势等对专业的持续影响。\
            \  \n   - 提醒学生和家长保持学习和调整心态，以应对可能的政策或行业变化。\n   - 高性价比与“隐藏选择”价值的信息\n\n##\
            \ 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n- 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。\
            \  \n\n# 【分析方式与步骤】\n请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：  \n1. **组建评审会**\
            \  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n1. 高考政策与招录规则研究员（细分到各省）\
            \  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家  \n4. 宏观经济学家 / 政策分析师\
            \  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）  \n7. 数据科学家 / 大数据分析师\
            \  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10. 科技趋势专家 / 未来学家  \n11.\
            \ 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n13. 企业家 / 创新创业导师  \n\
            14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\n2. **明确评审会角色**  \n\
               - 每位专家将从其专业视角对研究框架提出质询或建议，补充关键数据点与深层问题。\n\n3. **多轮讨论与迭代**  \n   - 首先由主要分析者给出初步梳理（关键数据、宏观背景、主要争议点）。\
            \  \n   - 各专家依次评议并提出修订，进行反复优化，直到评审会一致同意，认为研究方法与信息收集完整无缺陷。\n\n4. **最终报告输出**\
            \  \n   - 以结构化形式总结，以简洁明了的结论为主，为考生提供务实、可操作的建议\n   - 强调对考生来说“最关键的信息”  \n\
               - 数据来源需说明（如行业白皮书、教育部统计、权威媒体报道等），无需展示全部推理环节。  \n\n请立即按照以上指令展开研究流程\n\n\
            学生情况：\n{{#1738727589378.Profile#}}\n\n前面供参考的报告：\n{{#17458560149760.Third#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 制定挖掘计划 44
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457340486300'
      position:
        x: 8.236486156338344
        y: 955.5751638348602
      positionAbsolute:
        x: 8.236486156338344
        y: 955.5751638348602
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在请进行第一部分的分析，主要关注内容如下：\n\
            \n### 1. 超预期潜力发掘\n\n1. **潜在高价值方向**    \n   - 让他们各自提出对学生“潜在高价值方向”的看法，并预测\
            \ 3～10 年后各领域发展的可能性。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **挖掘超出预期的职业前景**  \n \
            \  - 不止传统“985/211 + 热门专业”，也要关注城市产业升级、新设专业、校企合作等能带来“意外惊喜”的选项。  \n   - 引入最新区域政策、城市人才引进优惠、国际交换项目、双学位/本硕连读等多种路径。\
            \  \n   - 高性价比与“隐藏选择”价值的信息\n\n## 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。\
            \  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\
            - 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。  \n\n# 【分析方式与步骤】\n\
            请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：  \n1. **组建评审会**  \n一个由超过 10\
            \ 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n1. 高考政策与招录规则研究员（细分到各省）\
            \  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家  \n4. 宏观经济学家 / 政策分析师\
            \  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）  \n7. 数据科学家 / 大数据分析师\
            \  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10. 科技趋势专家 / 未来学家  \n11.\
            \ 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n13. 企业家 / 创新创业导师  \n\
            14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\n2. **明确评审会角色**  \n\
               - 每位专家将从其专业视角对研究框架提出质询或建议，补充关键数据点与深层问题。\n\n3. **多轮讨论与迭代**  \n   - 首先由主要分析者给出初步梳理（关键数据、宏观背景、主要争议点）。\
            \  \n   - 各专家依次评议并提出修订，进行反复优化，直到评审会一致同意，认为研究方法与信息收集完整无缺陷。\n\n4. **最终报告输出**\
            \  \n   - 以结构化形式总结，以简洁明了的结论为主，为考生提供务实、可操作的建议\n   - 强调对考生来说“最关键的信息”  \n\
               - 数据来源需说明（如行业白皮书、教育部统计、权威媒体报道等），无需展示全部推理环节。  \n \n请立即按照以上指令展开研究流程\n\
            \n学生情况：\n{{#1738727589378.Profile#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 制定挖掘计划 11
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457341467330'
      position:
        x: -2.9849316837442643
        y: 41.85316766067004
      positionAbsolute:
        x: -2.9849316837442643
        y: 41.85316766067004
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在请进行第二部分的分析，主要关注内容如下：\n\
            \n### 2. 专业与院校推荐（分视角）\n\n1. **从专业到职业**  \n   - 列出若干与学生兴趣、学科特长吻合的专业，并对其职业发展进行分层梳理。\
            \  \n   - 对家长关注的就业、薪资、社会认同度做专门说明。  \n   - 特别指出“新兴专业”“跨学科专业”或“学校特色专业”等可能被忽视但前景亮眼的选项。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **从职业到专业**  \n   - 若学生有较明确职业想法，则先锁定行业方向，再逆推合适专业。\
            \  \n   - 对于家长较看重的“稳定+高薪”职业，如金融、医学、公务员相关专业等，也要给出客观分析。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n3. **关注学生所在省份高考政策**  \n   - 要综合考虑该省份的志愿填报批次、专业组或院校组投档方式，有无“专业优先”或“院校优先”的规则差异。\
            \  \n   - 若有平行志愿，需注意冲稳保的逻辑；若是梯度志愿，则须考量第一志愿命中率等因素。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n## 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n- 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。\
            \  \n\n# 【分析方式与步骤】\n请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：  \n1. **组建评审会**\
            \  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n1. 高考政策与招录规则研究员（细分到各省）\
            \  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家  \n4. 宏观经济学家 / 政策分析师\
            \  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）  \n7. 数据科学家 / 大数据分析师\
            \  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10. 科技趋势专家 / 未来学家  \n11.\
            \ 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n13. 企业家 / 创新创业导师  \n\
            14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\n2. **明确评审会角色**  \n\
               - 每位专家将从其专业视角对研究框架提出质询或建议，补充关键数据点与深层问题。\n\n3. **多轮讨论与迭代**  \n   - 首先由主要分析者给出初步梳理（关键数据、宏观背景、主要争议点）。\
            \  \n   - 各专家依次评议并提出修订，进行反复优化，直到评审会一致同意，认为研究方法与信息收集完整无缺陷。\n\n4. **最终报告输出**\
            \  \n   - 以结构化形式总结，以简洁明了的结论为主，为考生提供务实、可操作的建议\n   - 强调对考生来说“最关键的信息”  \n\
               - 数据来源需说明（如行业白皮书、教育部统计、权威媒体报道等），无需展示全部推理环节。  \n\n请立即按照以上指令展开研究流程\n\n\
            学生情况：\n{{#1738727589378.Profile#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 制定挖掘计划 22
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457361886690'
      position:
        x: 8.236486156338344
        y: 339.6680616066446
      positionAbsolute:
        x: 8.236486156338344
        y: 339.6680616066446
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在请进行第三部分的分析，主要关注内容如下：\n\
            \n### 3. 志愿填报具体方案（省份政策合规）\n\n1. **传统分数优先方案**  \n   - 基于学生预估分数或历年录取位次，按所在省份的投档批次、平行志愿/顺序志愿规则，给出冲刺-稳妥-保底院校的排列。\
            \  \n   - 明确每个填报顺位对应学校（或专业组）的往年录取数据。  \n   - 保证方案严格符合当地高考政策的细节（如本科批次合并、专业+学校组志愿形式、强基计划单独招生等）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **AI 潜力最大化方案**  \n   - 在政策允许范围内，结合学生兴趣与超预期潜力方向给出另一种排序。\
            \  \n   - 可能与分数优先策略不一致，但能带来更高未来回报或更匹配的专业发展空间。  \n   - 显示该方案的可能收益（如更有机会进入某新兴领域或在某城市得到产业扶持）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **学生和家长各自的定制思考**  \n   - 针对学生  \n   - 针对家长\n\
            \   - 若二者发生冲突，提供可选路径或建议进行深度沟通的提示。\n   - 高性价比与“隐藏选择”价值的信息\n\n4. **评审会多轮把关**\
            \  \n   - 审核所有志愿顺序的合理性、合规性、可行性；  \n   - 确认无误后，方可对外发布完整的志愿填报方案。\n\n## 【需要深度数据支撑】\n\
            - 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\
            - 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。  \n\n# 【分析方式与步骤】\n\
            请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：  \n1. **组建评审会**  \n一个由超过 10\
            \ 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n1. 高考政策与招录规则研究员（细分到各省）\
            \  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家  \n4. 宏观经济学家 / 政策分析师\
            \  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）  \n7. 数据科学家 / 大数据分析师\
            \  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10. 科技趋势专家 / 未来学家  \n11.\
            \ 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n13. 企业家 / 创新创业导师  \n\
            14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\n2. **明确评审会角色**  \n\
               - 每位专家将从其专业视角对研究框架提出质询或建议，补充关键数据点与深层问题。\n\n3. **多轮讨论与迭代**  \n   - 首先由主要分析者给出初步梳理（关键数据、宏观背景、主要争议点）。\
            \  \n   - 各专家依次评议并提出修订，进行反复优化，直到评审会一致同意，认为研究方法与信息收集完整无缺陷。\n\n4. **最终报告输出**\
            \  \n   - 以结构化形式总结，以简洁明了的结论为主，为考生提供务实、可操作的建议\n   - 强调对考生来说“最关键的信息”  \n\
               - 数据来源需说明（如行业白皮书、教育部统计、权威媒体报道等），无需展示全部推理环节。  \n\n请立即按照以上指令展开研究流程\n\n\
            学生情况：\n{{#1738727589378.Profile#}}\n\n前面供参考的报告：\n{{#17458542648830.First#}}\n\
            {{#17458554196650.Second#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 制定挖掘计划 33
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457361931830'
      position:
        x: 13.934503870117993
        y: 658.7246187063728
      positionAbsolute:
        x: 13.934503870117993
        y: 658.7246187063728
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 1
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: Forth
          value:
          - '17457340486300'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745736592974start
        title: 循环 4
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '1745736592974'
      position:
        x: 305.233333920688
        y: 955.5751638348602
      positionAbsolute:
        x: 305.233333920688
        y: 955.5751638348602
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745736592974start
      parentId: '1745736592974'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 329.233333920688
        y: 1023.5751638348602
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# 【背景与目标】\n现在有一份《高考报志愿及职业规划建议报告》。我想从高考报志愿学生的视角，评估这份报告。力求识别并挖掘对学生“性价比”特别高但容易被忽视的隐藏选择。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节，最后请输出问题清单。主要关注内容如下：\n\
            \n### 4. 后续发展规划（短期 & 中长期）\n\n1. **大学阶段规划**  \n   - 指导学生如何利用大学资源（课程、社团、科研项目、产学研合作、实习等），尤其是已经选定的专业与院校。\
            \  \n   - 提醒家长关注和支持学生在校期间的心态和学费等财务规划。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **短期（3~5\
            \ 年）职业发展**  \n   - 对毕业后的主流去向（企业就业、考研、创业、留学等）进行评估；  \n   - 根据学生和家长关注点，给出相关资料、经验或成功案例。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **中长期（5~10 年）前景**  \n   - 由未来学家、经济学家、产业顾问讨论行业变革、城市升级、国际竞争态势等对专业的持续影响。\
            \  \n   - 提醒学生和家长保持学习和调整心态，以应对可能的政策或行业变化。\n   - 高性价比与“隐藏选择”价值的信息\n\n##\
            \ 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\n# 【分析方式与步骤】\n请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：\
            \  \n1. **组建评审会**  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n\
            1. 高考政策与招录规则研究员（细分到各省）  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家\
            \  \n4. 宏观经济学家 / 政策分析师  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）\
            \  \n7. 数据科学家 / 大数据分析师  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10.\
            \ 科技趋势专家 / 未来学家  \n11. 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n\
            13. 企业家 / 创新创业导师  \n14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\
            \n2. **明确评审会角色**：  \n   - 每位专家基于其专业背景，对研究方法与要点进行质询、补充或纠偏。  \n\n3. **开展多轮讨论与迭代**：  \n\
               - 由主要分析者先提出初步框架、数据来源思路。  \n   - 各位专家依次提出改进意见，进行多轮优化。  \n   - 直至评审会整体认可分析方法符合最高标准，不再有遗漏或明显缺陷。  \n\
            \n4. **最终输出要求**： \n- 请输出最终版的问题清单\n\n请立即根据以上指令展开研究流程\n\n学生情况：\n{{#1738727589378.Profile#}}\n\
            \n之前的分析报告：\n{{#17458560149760.Third#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 4
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745736635063'
      parentId: '1745736592974'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 398.649557260288
        y: 1020.5751638348602
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '1745736592974'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745736635063.text#}}'
        type: tool
      height: 54
      id: '1745736689919'
      parentId: '1745736592974'
      position:
        x: 368.55595460468464
        y: 70.01917717128963
      positionAbsolute:
        x: 673.7892885253726
        y: 1025.5943410061498
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '1745736592974'
          - HQA
          variable: HQA
        - value_selector:
          - '1745736635063'
          - text
          variable: NQA
      height: 54
      id: '1745736788778'
      parentId: '1745736592974'
      position:
        x: 692.5908050098149
        y: 71.7444612918858
      positionAbsolute:
        x: 997.8241389305028
        y: 1027.319625126746
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在已经有一份分析报告，和收集的新的信息，请更新分析报告的第四部分。主要关注内容如下：\n\
            \n### 4. 后续发展规划（短期 & 中长期）\n\n1. **大学阶段规划**  \n   - 指导学生如何利用大学资源（课程、社团、科研项目、产学研合作、实习等），尤其是已经选定的专业与院校。\
            \  \n   - 提醒家长关注和支持学生在校期间的心态和学费等财务规划。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **短期（3~5\
            \ 年）职业发展**  \n   - 对毕业后的主流去向（企业就业、考研、创业、留学等）进行评估；  \n   - 根据学生和家长关注点，给出相关资料、经验或成功案例。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **中长期（5~10 年）前景**  \n   - 由未来学家、经济学家、产业顾问讨论行业变革、城市升级、国际竞争态势等对专业的持续影响。\
            \  \n   - 提醒学生和家长保持学习和调整心态，以应对可能的政策或行业变化。\n   - 高性价比与“隐藏选择”价值的信息\n\n##\
            \ 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n- 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。\
            \  \n\n# 【分析方式与步骤】\n为确保分析专业且多角度，请分步骤进行，并包含充分的思考与内部讨论环节，但在最终回答时仅呈现精炼的结论或概括，避免在外显回答中泄露冗长推理过程。  \n\
            1. **组建评审会**  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n\
            1. 高考政策与招录规则研究员（细分到各省）  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家\
            \  \n4. 宏观经济学家 / 政策分析师  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）\
            \  \n7. 数据科学家 / 大数据分析师  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10.\
            \ 科技趋势专家 / 未来学家  \n11. 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n\
            13. 企业家 / 创新创业导师  \n14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\
            \n2. **明确评审会角色**  \n   - 每位专家针对研究框架和收集数据的思路提出质疑和改进意见；  \n   - 确保能够深度覆盖各种角度。\n\
            \n3. **多轮讨论与迭代**  \n   - 由主要分析者先提出初步方案与数据来源；  \n   - 各专家依次给出改进建议；  \n\
            \   - 反复优化直到评审会一致同意分析方法全面、严谨、数据可靠。\n\n4. **最终报告输出**  \n   - 只需给出结构化且聚焦的结论\n\
            \   - 以及对学生“性价比”与隐藏机遇的综合分析。  \n   - 若引用具体数字或排名，可简要标明数据出处；不必在回答中展示所有中间推理。\
            \  \n\n# 【结论】\n- 在输出最终结论前，必须完成多轮专家评审与改进，确保没有明显疏漏或数据缺陷。  \n- 最终呈现时，以条理清晰且易于理解的形式\n\
            \n学生情况：\n{{#1738727589378.Profile#}}\n\n分析报告：\n{{#17458560149760.Third#}}\n\
            \n问题清单：\n{{#1745736788778.output#}}\n\n问题回答：\n{{#1745736689919.text#}}\n\
            \n请立即依照以上指令执行研究流程，并最终呈现完整分析结论。\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 41
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745736973218'
      parentId: '1745736592974'
      position:
        x: 998.3862351915275
        y: 73.53989147359859
      positionAbsolute:
        x: 1303.6195691122155
        y: 1029.1150553084588
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745736973218'
          - text
          variable_selector:
          - '1745736592974'
          - Forth
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745736788778'
          - output
          variable_selector:
          - '1745736592974'
          - HQA
          write_mode: over-write
        loop_id: '1745736592974'
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 116
      id: '1745737138928'
      parentId: '1745736592974'
      position:
        x: 1304.1816653732403
        y: 73.53989147359857
      positionAbsolute:
        x: 1609.4149992939283
        y: 1029.1150553084588
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-pro-preview-03-25
          provider: langgenius/gemini/google
        prompt_template:
        - id: f73ee2e2-3d13-476c-926e-50eb0caecbb0
          role: system
          text: ''
        - id: c63135f3-9034-437d-ac99-5a15a712c182
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。此前你已经有了第一版本的报告，请你对上一个版本的报告进行优化整理，尽量用朴实的普通人可以听懂的语言，尤其是对专有名词进行解释，要突出有深度的思考、结论，要用丰富的细节证据支撑这些结论，要注意用娓娓道来的语言让读者吸收理解，要注明引用源\n\
            \n请用如下的框架进行内容输出：\n\n### 0. 学生情况综述\n1. 学生情况简单介绍\n\n### 1. 超预期潜力发掘\n\n1.\
            \ **潜在高价值方向**    \n   - 让他们各自提出对学生“潜在高价值方向”的看法，并预测 3～10 年后各领域发展的可能性。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **挖掘超出预期的职业前景**  \n   - 不止传统“985/211 + 热门专业”，也要关注城市产业升级、新设专业、校企合作等能带来“意外惊喜”的选项。\
            \  \n   - 引入最新区域政策、城市人才引进优惠、国际交换项目、双学位/本硕连读等多种路径。  \n   - 高性价比与“隐藏选择”价值的信息\n\
            \n### 2. 专业与院校推荐（分视角）\n\n1. **从专业到职业**  \n   - 列出若干与学生兴趣、学科特长吻合的专业，并对其职业发展进行分层梳理。\
            \  \n   - 对家长关注的就业、薪资、社会认同度做专门说明。  \n   - 特别指出“新兴专业”“跨学科专业”或“学校特色专业”等可能被忽视但前景亮眼的选项。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **从职业到专业**  \n   - 若学生有较明确职业想法，则先锁定行业方向，再逆推合适专业。\
            \  \n   - 对于家长较看重的“稳定+高薪”职业，如金融、医学、公务员相关专业等，也要给出客观分析。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n3. **关注学生所在省份高考政策**  \n   - 要综合考虑该省份的志愿填报批次、专业组或院校组投档方式，有无“专业优先”或“院校优先”的规则差异。\
            \  \n   - 若有平行志愿，需注意冲稳保的逻辑；若是梯度志愿，则须考量第一志愿命中率等因素。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n### 3. 志愿填报具体方案（省份政策合规）\n\n1. **传统分数优先方案**  \n   - 基于学生预估分数或历年录取位次，按所在省份的投档批次、平行志愿/顺序志愿规则，给出冲刺-稳妥-保底院校的排列。\
            \  \n   - 明确每个填报顺位对应学校（或专业组）的往年录取数据。  \n   - 保证方案严格符合当地高考政策的细节（如本科批次合并、专业+学校组志愿形式、强基计划单独招生等）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **AI 潜力最大化方案**  \n   - 在政策允许范围内，结合学生兴趣与超预期潜力方向给出另一种排序。\
            \  \n   - 可能与分数优先策略不一致，但能带来更高未来回报或更匹配的专业发展空间。  \n   - 显示该方案的可能收益（如更有机会进入某新兴领域或在某城市得到产业扶持）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **学生和家长各自的定制思考**  \n   - 针对学生  \n   - 针对家长\n\
            \   - 若二者发生冲突，提供可选路径或建议进行深度沟通的提示。\n   - 高性价比与“隐藏选择”价值的信息\n\n### 4. 后续发展规划（短期\
            \ & 中长期）\n\n1. **大学阶段规划**  \n   - 指导学生如何利用大学资源（课程、社团、科研项目、产学研合作、实习等），尤其是已经选定的专业与院校。\
            \  \n   - 提醒家长关注和支持学生在校期间的心态和学费等财务规划。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **短期（3~5\
            \ 年）职业发展**  \n   - 对毕业后的主流去向（企业就业、考研、创业、留学等）进行评估；  \n   - 根据学生和家长关注点，给出相关资料、经验或成功案例。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **中长期（5~10 年）前景**  \n   - 由未来学家、经济学家、产业顾问讨论行业变革、城市升级、国际竞争态势等对专业的持续影响。\
            \  \n   - 提醒学生和家长保持学习和调整心态，以应对可能的政策或行业变化。\n   - 高性价比与“隐藏选择”价值的信息\n\n##\
            \ 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n- 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。\
            \  \n\n**最终报告输出**  \n   - 以结构化形式总结，以简洁明了的结论为主，为考生提供务实、可操作的建议\n   - 强调对考生来说“最关键的信息”\
            \  \n   - 数据来源只需简要说明（如行业白皮书、教育部统计、权威媒体报道等），无需展示推理环节  \n   - 要突出有深度的思考、结论，要用丰富的细节证据支撑这些结论，要注意用娓娓道来的语言让读者吸收理解\n\
            \   - 不要用json格式输出，用文本形式输出报告\n\n待压缩的报告：\n{{#17458542648830.First#}}\n{{#17458554196650.Second#}}\n\
            {{#17458560149760.Third#}}\n{{#1745736592974.Forth#}}\n\n学生情况：\n{{#1738727589378.Profile#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 7
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457665814220'
      position:
        x: 2267.8681788375407
        y: 401.6450568402179
      positionAbsolute:
        x: 2267.8681788375407
        y: 401.6450568402179
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17457665814220'
          - text
          variable: text
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1745766730888'
      position:
        x: 2267.8681788375407
        y: 574.302285883295
      positionAbsolute:
        x: 2267.8681788375407
        y: 574.302285883295
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 3
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: First
          value:
          - '17457341467330'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745854264883start
        title: 循环 1
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '17458542648830'
      position:
        x: 574.8730147412621
        y: -132.1880296064411
      positionAbsolute:
        x: 574.8730147412621
        y: -132.1880296064411
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# 【背景与目标】\n现在有一份《高考报志愿及职业规划建议报告》。我想从高考报志愿学生的视角，评估这份报告。力求识别并挖掘对学生“性价比”特别高但容易被忽视的隐藏选择。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节，最后请输出问题清单。主要关注内容如下：\n\
            \n### 1. 超预期潜力发掘\n\n1. **潜在高价值方向**    \n   - 让他们各自提出对学生“潜在高价值方向”的看法，并预测\
            \ 3～10 年后各领域发展的可能性。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **挖掘超出预期的职业前景**  \n \
            \  - 不止传统“985/211 + 热门专业”，也要关注城市产业升级、新设专业、校企合作等能带来“意外惊喜”的选项。  \n   - 引入最新区域政策、城市人才引进优惠、国际交换项目、双学位/本硕连读等多种路径。\
            \  \n   - 高性价比与“隐藏选择”价值的信息\n\n## 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。\
            \  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\
            \n# 【分析方式与步骤】\n请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：  \n1. **组建评审会**\
            \  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n1. 高考政策与招录规则研究员（细分到各省）\
            \  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家  \n4. 宏观经济学家 / 政策分析师\
            \  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）  \n7. 数据科学家 / 大数据分析师\
            \  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10. 科技趋势专家 / 未来学家  \n11.\
            \ 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n13. 企业家 / 创新创业导师  \n\
            14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\n2. **明确评审会角色**：  \n\
               - 每位专家基于其专业背景，对研究方法与要点进行质询、补充或纠偏。  \n\n3. **开展多轮讨论与迭代**：  \n   - 由主要分析者先提出初步框架、数据来源思路。  \n\
               - 各位专家依次提出改进意见，进行多轮优化。  \n   - 直至评审会整体认可分析方法符合最高标准，不再有遗漏或明显缺陷。  \n\n\
            4. **最终输出要求**： \n- 请输出最终版的问题清单\n\n请立即根据以上指令展开研究流程\n\n学生情况：\n{{#1738727589378.Profile#}}\n\
            \n之前的分析报告：\n{{#17458542648830.First#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745854264883017458542648830'
      parentId: '17458542648830'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 668.2892380808621
        y: -67.18802960644109
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '17458542648830'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745854264883017458542648830.text#}}'
        type: tool
      height: 54
      id: '1745854264883017458542648841'
      parentId: '17458542648830'
      position:
        x: 368.55595460468464
        y: 65
      positionAbsolute:
        x: 943.4289693459467
        y: -67.18802960644109
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '17458542648830'
          - HQA
          variable: HQA
        - value_selector:
          - '1745854264883017458542648830'
          - text
          variable: NQA
      height: 54
      id: '1745854264883017458542648842'
      parentId: '17458542648830'
      position:
        x: 696.1501419524639
        y: 71.74446129188578
      positionAbsolute:
        x: 1271.023156693726
        y: -60.44356831455531
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在已经有一份分析报告，和收集的新的信息，请更新分析报告的第一部分。主要关注内容如下：\n\
            \n### 1. 超预期潜力发掘\n\n1. **潜在高价值方向**    \n   - 让他们各自提出对学生“潜在高价值方向”的看法，并预测\
            \ 3～10 年后各领域发展的可能性。\n   - 高性价比与“隐藏选择”价值的信息\n\n2. **挖掘超出预期的职业前景**  \n \
            \  - 不止传统“985/211 + 热门专业”，也要关注城市产业升级、新设专业、校企合作等能带来“意外惊喜”的选项。  \n   - 引入最新区域政策、城市人才引进优惠、国际交换项目、双学位/本硕连读等多种路径。\
            \  \n   - 高性价比与“隐藏选择”价值的信息\n\n## 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。\
            \  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\
            - 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。  \n\n# 【分析方式与步骤】\n\
            为确保分析专业且多角度，请分步骤进行，并包含充分的思考与内部讨论环节，但在最终回答时仅呈现精炼的结论或概括，避免在外显回答中泄露冗长推理过程。  \n\
            1. **组建评审会**  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n\
            1. 高考政策与招录规则研究员（细分到各省）  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家\
            \  \n4. 宏观经济学家 / 政策分析师  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）\
            \  \n7. 数据科学家 / 大数据分析师  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10.\
            \ 科技趋势专家 / 未来学家  \n11. 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n\
            13. 企业家 / 创新创业导师  \n14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\
            \n2. **明确评审会角色**  \n   - 每位专家针对研究框架和收集数据的思路提出质疑和改进意见；  \n   - 确保能够深度覆盖各种角度。\n\
            \n3. **多轮讨论与迭代**  \n   - 由主要分析者先提出初步方案与数据来源；  \n   - 各专家依次给出改进建议；  \n\
            \   - 反复优化直到评审会一致同意分析方法全面、严谨、数据可靠。\n\n4. **最终报告输出**  \n   - 只需给出结构化且聚焦的结论\n\
            \   - 以及对学生“性价比”与隐藏机遇的综合分析。  \n   - 若引用具体数字或排名，可简要标明数据出处；不必在回答中展示所有中间推理。\
            \  \n\n# 【结论】\n- 在输出最终结论前，必须完成多轮专家评审与改进，确保没有明显疏漏或数据缺陷。  \n- 最终呈现时，以条理清晰且易于理解的形式\n\
            \n学生情况：\n{{#1738727589378.Profile#}}\n\n分析报告：\n{{#17458542648830.First#}}\n\
            \n问题清单：\n{{#1745854264883017458542648842.output#}}\n\n问题回答：\n{{#1745854264883017458542648841.text#}}\n\
            \n请立即依照以上指令执行研究流程，并最终呈现完整分析结论。\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 11
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745854264883017458542648843'
      parentId: '17458542648830'
      position:
        x: 998.3862351915275
        y: 73.53989147359859
      positionAbsolute:
        x: 1573.2592499327895
        y: -58.648138132842504
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745854264883017458542648843'
          - text
          variable_selector:
          - '17458542648830'
          - First
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745854264883017458542648842'
          - output
          variable_selector:
          - '17458542648830'
          - HQA
          write_mode: over-write
        loop_id: '17458542648830'
        selected: false
        title: 变量赋值 1
        type: assigner
        version: '2'
      height: 116
      id: '1745854264883017458542648844'
      parentId: '17458542648830'
      position:
        x: 1383.9539133661567
        y: 93.4900885688987
      positionAbsolute:
        x: 1958.8269281074188
        y: -38.69794103754239
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745854264883start
      parentId: '17458542648830'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 598.8730147412621
        y: -64.18802960644109
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 3
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: Second
          value:
          - '17457361886690'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745855419665start
        title: 循环 2
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '17458554196650'
      position:
        x: 305.233333920688
        y: 329.0202520988962
      positionAbsolute:
        x: 305.233333920688
        y: 329.0202520988962
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# 【背景与目标】\n现在有一份《高考报志愿及职业规划建议报告》。我想从高考报志愿学生的视角，评估这份报告。力求识别并挖掘对学生“性价比”特别高但容易被忽视的隐藏选择。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节，最后请输出问题清单。主要关注内容如下：\n\
            \n### 2. 专业与院校推荐（分视角）\n\n1. **从专业到职业**  \n   - 列出若干与学生兴趣、学科特长吻合的专业，并对其职业发展进行分层梳理。\
            \  \n   - 对家长关注的就业、薪资、社会认同度做专门说明。  \n   - 特别指出“新兴专业”“跨学科专业”或“学校特色专业”等可能被忽视但前景亮眼的选项。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **从职业到专业**  \n   - 若学生有较明确职业想法，则先锁定行业方向，再逆推合适专业。\
            \  \n   - 对于家长较看重的“稳定+高薪”职业，如金融、医学、公务员相关专业等，也要给出客观分析。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n3. **关注学生所在省份高考政策**  \n   - 要综合考虑该省份的志愿填报批次、专业组或院校组投档方式，有无“专业优先”或“院校优先”的规则差异。\
            \  \n   - 若有平行志愿，需注意冲稳保的逻辑；若是梯度志愿，则须考量第一志愿命中率等因素。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n## 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\n# 【分析方式与步骤】\n请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：\
            \  \n1. **组建评审会**  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n\
            1. 高考政策与招录规则研究员（细分到各省）  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家\
            \  \n4. 宏观经济学家 / 政策分析师  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）\
            \  \n7. 数据科学家 / 大数据分析师  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10.\
            \ 科技趋势专家 / 未来学家  \n11. 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n\
            13. 企业家 / 创新创业导师  \n14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\
            \n2. **明确评审会角色**：  \n   - 每位专家基于其专业背景，对研究方法与要点进行质询、补充或纠偏。  \n\n3. **开展多轮讨论与迭代**：  \n\
               - 由主要分析者先提出初步框架、数据来源思路。  \n   - 各位专家依次提出改进意见，进行多轮优化。  \n   - 直至评审会整体认可分析方法符合最高标准，不再有遗漏或明显缺陷。  \n\
            \n4. **最终输出要求**： \n- 请输出最终版的问题清单\n\n请立即根据以上指令展开研究流程\n\n学生情况：\n{{#1738727589378.Profile#}}\n\
            \n之前的分析报告：\n{{#17458554196650.Second#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745855419665017458554196650'
      parentId: '17458554196650'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 398.649557260288
        y: 394.0202520988962
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '17458554196650'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745855419665017458554196650.text#}}'
        type: tool
      height: 54
      id: '1745855419665017458554196651'
      parentId: '17458554196650'
      position:
        x: 368.55595460468464
        y: 65
      positionAbsolute:
        x: 673.7892885253726
        y: 394.0202520988962
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '17458554196650'
          - HQA
          variable: HQA
        - value_selector:
          - '1745855419665017458554196650'
          - text
          variable: NQA
      height: 54
      id: '1745855419665017458554196652'
      parentId: '17458554196650'
      position:
        x: 692.5908050098149
        y: 71.7444612918858
      positionAbsolute:
        x: 997.8241389305028
        y: 400.764713390782
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在已经有一份分析报告，和收集的新的信息，请更新分析报告的第二部分。主要关注内容如下：\n\
            \n### 2. 专业与院校推荐（分视角）\n\n1. **从专业到职业**  \n   - 列出若干与学生兴趣、学科特长吻合的专业，并对其职业发展进行分层梳理。\
            \  \n   - 对家长关注的就业、薪资、社会认同度做专门说明。  \n   - 特别指出“新兴专业”“跨学科专业”或“学校特色专业”等可能被忽视但前景亮眼的选项。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **从职业到专业**  \n   - 若学生有较明确职业想法，则先锁定行业方向，再逆推合适专业。\
            \  \n   - 对于家长较看重的“稳定+高薪”职业，如金融、医学、公务员相关专业等，也要给出客观分析。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n3. **关注学生所在省份高考政策**  \n   - 要综合考虑该省份的志愿填报批次、专业组或院校组投档方式，有无“专业优先”或“院校优先”的规则差异。\
            \  \n   - 若有平行志愿，需注意冲稳保的逻辑；若是梯度志愿，则须考量第一志愿命中率等因素。\n   - 高性价比与“隐藏选择”价值的信息\n\
            \n## 【需要深度数据支撑】\n- 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。\
            \  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n- 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。\
            \  \n\n# 【分析方式与步骤】\n为确保分析专业且多角度，请分步骤进行，并包含充分的思考与内部讨论环节，但在最终回答时仅呈现精炼的结论或概括，避免在外显回答中泄露冗长推理过程。  \n\
            1. **组建评审会**  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n\
            1. 高考政策与招录规则研究员（细分到各省）  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家\
            \  \n4. 宏观经济学家 / 政策分析师  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）\
            \  \n7. 数据科学家 / 大数据分析师  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10.\
            \ 科技趋势专家 / 未来学家  \n11. 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n\
            13. 企业家 / 创新创业导师  \n14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\
            \n2. **明确评审会角色**  \n   - 每位专家针对研究框架和收集数据的思路提出质疑和改进意见；  \n   - 确保能够深度覆盖各种角度。\n\
            \n3. **多轮讨论与迭代**  \n   - 由主要分析者先提出初步方案与数据来源；  \n   - 各专家依次给出改进建议；  \n\
            \   - 反复优化直到评审会一致同意分析方法全面、严谨、数据可靠。\n\n4. **最终报告输出**  \n   - 只需给出结构化且聚焦的结论\n\
            \   - 以及对学生“性价比”与隐藏机遇的综合分析。  \n   - 若引用具体数字或排名，可简要标明数据出处；不必在回答中展示所有中间推理。\
            \  \n\n# 【结论】\n- 在输出最终结论前，必须完成多轮专家评审与改进，确保没有明显疏漏或数据缺陷。  \n- 最终呈现时，以条理清晰且易于理解的形式\n\
            \n学生情况：\n{{#1738727589378.Profile#}}\n\n分析报告：\n{{#17458554196650.Second#}}\n\
            \n问题清单：\n{{#1745855419665017458554196652.output#}}\n\n问题回答：\n{{#1745855419665017458554196651.text#}}\n\
            \n请立即依照以上指令执行研究流程，并最终呈现完整分析结论。\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 21
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745855419665017458554196653'
      parentId: '17458554196650'
      position:
        x: 1007.0079533298938
        y: 65
      positionAbsolute:
        x: 1312.2412872505818
        y: 394.0202520988962
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745855419665017458554196653'
          - text
          variable_selector:
          - '17458554196650'
          - Second
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745855419665017458554196652'
          - output
          variable_selector:
          - '17458554196650'
          - HQA
          write_mode: over-write
        loop_id: '17458554196650'
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 116
      id: '1745855419665017458554196654'
      parentId: '17458554196650'
      position:
        x: 1304.1816653732403
        y: 73.53989147359857
      positionAbsolute:
        x: 1609.4149992939283
        y: 402.5601435724948
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745855419665start
      parentId: '17458554196650'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 329.233333920688
        y: 397.0202520988962
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 3
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: Third
          value:
          - '17457361931830'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745856014976start
        title: 循环 3
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '17458560149760'
      position:
        x: 305.233333920688
        y: 658.7246187063728
      positionAbsolute:
        x: 305.233333920688
        y: 658.7246187063728
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# 【背景与目标】\n现在有一份《高考报志愿及职业规划建议报告》。我想从高考报志愿学生的视角，评估这份报告。力求识别并挖掘对学生“性价比”特别高但容易被忽视的隐藏选择。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节，最后请输出问题清单。主要关注内容如下：\n\
            \n### 3. 志愿填报具体方案（省份政策合规）\n\n1. **传统分数优先方案**  \n   - 基于学生预估分数或历年录取位次，按所在省份的投档批次、平行志愿/顺序志愿规则，给出冲刺-稳妥-保底院校的排列。\
            \  \n   - 明确每个填报顺位对应学校（或专业组）的往年录取数据。  \n   - 保证方案严格符合当地高考政策的细节（如本科批次合并、专业+学校组志愿形式、强基计划单独招生等）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **AI 潜力最大化方案**  \n   - 在政策允许范围内，结合学生兴趣与超预期潜力方向给出另一种排序。\
            \  \n   - 可能与分数优先策略不一致，但能带来更高未来回报或更匹配的专业发展空间。  \n   - 显示该方案的可能收益（如更有机会进入某新兴领域或在某城市得到产业扶持）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **学生和家长各自的定制思考**  \n   - 针对学生  \n   - 针对家长\n\
            \   - 若二者发生冲突，提供可选路径或建议进行深度沟通的提示。\n   - 高性价比与“隐藏选择”价值的信息\n\n4. **评审会多轮把关**\
            \  \n   - 审核所有志愿顺序的合理性、合规性、可行性；  \n   - 确认无误后，方可对外发布完整的志愿填报方案。\n\n## 【需要深度数据支撑】\n\
            - 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\
            \n# 【分析方式与步骤】\n请严格按照以下多轮程序进行分析，不在最终回答中公开冗长思考过程，仅呈现凝练后的结论：  \n1. **组建评审会**\
            \  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n1. 高考政策与招录规则研究员（细分到各省）\
            \  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家  \n4. 宏观经济学家 / 政策分析师\
            \  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）  \n7. 数据科学家 / 大数据分析师\
            \  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10. 科技趋势专家 / 未来学家  \n11.\
            \ 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n13. 企业家 / 创新创业导师  \n\
            14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\n2. **明确评审会角色**：  \n\
               - 每位专家基于其专业背景，对研究方法与要点进行质询、补充或纠偏。  \n\n3. **开展多轮讨论与迭代**：  \n   - 由主要分析者先提出初步框架、数据来源思路。  \n\
               - 各位专家依次提出改进意见，进行多轮优化。  \n   - 直至评审会整体认可分析方法符合最高标准，不再有遗漏或明显缺陷。  \n\n\
            4. **最终输出要求**： \n- 请输出最终版的问题清单\n\n请立即根据以上指令展开研究流程\n\n学生情况：\n{{#1738727589378.Profile#}}\n\
            \n之前的分析报告：\n{{#17458542648830.First#}}\n{{#17458554196650.Second#}}\n\
            {{#17458560149760.Third#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745856014976017458560149760'
      parentId: '17458560149760'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 398.649557260288
        y: 723.7246187063728
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '17458560149760'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745856014976017458560149760.text#}}'
        type: tool
      height: 54
      id: '1745856014976017458560149761'
      parentId: '17458560149760'
      position:
        x: 368.55595460468464
        y: 65
      positionAbsolute:
        x: 673.7892885253726
        y: 723.7246187063728
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '17458560149760'
          - HQA
          variable: HQA
        - value_selector:
          - '1745856014976017458560149760'
          - text
          variable: NQA
      height: 54
      id: '1745856014976017458560149762'
      parentId: '17458560149760'
      position:
        x: 692.5908050098149
        y: 71.7444612918858
      positionAbsolute:
        x: 997.8241389305028
        y: 730.4690799982586
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: "# 【背景与目标】\n你的目标是为一位高考学生生成一份系统且有深度的《高考报志愿及职业规划建议报告》。需要对以下方面进行深度研究，并尤其关注“性价比高的隐藏机会”。请提出更有深度的问题，引导搜集更高质量的数据，挖掘对学生最具价值的细节。现在已经有一份分析报告，和收集的新的信息，请更新分析报告的第三部分。主要关注内容如下：\n\
            \n### 3. 志愿填报具体方案（省份政策合规）\n\n1. **传统分数优先方案**  \n   - 基于学生预估分数或历年录取位次，按所在省份的投档批次、平行志愿/顺序志愿规则，给出冲刺-稳妥-保底院校的排列。\
            \  \n   - 明确每个填报顺位对应学校（或专业组）的往年录取数据。  \n   - 保证方案严格符合当地高考政策的细节（如本科批次合并、专业+学校组志愿形式、强基计划单独招生等）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n2. **AI 潜力最大化方案**  \n   - 在政策允许范围内，结合学生兴趣与超预期潜力方向给出另一种排序。\
            \  \n   - 可能与分数优先策略不一致，但能带来更高未来回报或更匹配的专业发展空间。  \n   - 显示该方案的可能收益（如更有机会进入某新兴领域或在某城市得到产业扶持）。\n\
            \   - 高性价比与“隐藏选择”价值的信息\n\n3. **学生和家长各自的定制思考**  \n   - 针对学生  \n   - 针对家长\n\
            \   - 若二者发生冲突，提供可选路径或建议进行深度沟通的提示。\n   - 高性价比与“隐藏选择”价值的信息\n\n4. **评审会多轮把关**\
            \  \n   - 审核所有志愿顺序的合理性、合规性、可行性；  \n   - 确认无误后，方可对外发布完整的志愿填报方案。\n\n## 【需要深度数据支撑】\n\
            - 尽量使用权威统计局或政府机构最新发布的数据。  \n- 如果有薪酬、房价等敏感数据，尽可能核对多个来源或给出区间值。  \n- 对地方政策或项目，提供官方链接或公告来源（如市政府官网、教育局官网）。\n\
            - 志愿填报具体方案必须严格符合学生所在省份的高考政策细则，如批次设置、平行志愿/顺序志愿、录取原则、特殊类型招生政策等。  \n\n# 【分析方式与步骤】\n\
            为确保分析专业且多角度，请分步骤进行，并包含充分的思考与内部讨论环节，但在最终回答时仅呈现精炼的结论或概括，避免在外显回答中泄露冗长推理过程。  \n\
            1. **组建评审会**  \n一个由超过 10 位的顶级专家组成的“评审会”（建议 12~15 位或更多），其成员来自以下或更多领域：\n\
            1. 高考政策与招录规则研究员（细分到各省）  \n2. 教育学家 / 教育部高校设置与专业增设研究专家  \n3. 职业规划师 / 人力资源专家\
            \  \n4. 宏观经济学家 / 政策分析师  \n5. 城市规划与区域发展顾问  \n6. 大学教授（涵盖理工科、文科、医药、艺术、经管等多专业）\
            \  \n7. 数据科学家 / 大数据分析师  \n8. 心理学家 / 家庭教育指导专家  \n9. 社会学家 / 公共管理学者  \n10.\
            \ 科技趋势专家 / 未来学家  \n11. 产业顾问（熟悉校企合作、行业需求）  \n12. 国际教育与留学顾问（如需国际化视角）  \n\
            13. 企业家 / 创新创业导师  \n14. 学科竞赛或特殊类型招生专家（如强基、综合评价等）  \n15. …（可继续扩充更多领域）\n\
            \n2. **明确评审会角色**  \n   - 每位专家针对研究框架和收集数据的思路提出质疑和改进意见；  \n   - 确保能够深度覆盖各种角度。\n\
            \n3. **多轮讨论与迭代**  \n   - 由主要分析者先提出初步方案与数据来源；  \n   - 各专家依次给出改进建议；  \n\
            \   - 反复优化直到评审会一致同意分析方法全面、严谨、数据可靠。\n\n4. **最终报告输出**  \n   - 只需给出结构化且聚焦的结论\n\
            \   - 以及对学生“性价比”与隐藏机遇的综合分析。  \n   - 若引用具体数字或排名，可简要标明数据出处；不必在回答中展示所有中间推理。\
            \  \n\n# 【结论】\n- 在输出最终结论前，必须完成多轮专家评审与改进，确保没有明显疏漏或数据缺陷。  \n- 最终呈现时，以条理清晰且易于理解的形式\n\
            \n学生情况：\n{{#1738727589378.Profile#}}\n\n分析报告：\n{{#17458542648830.First#}}\n\
            {{#17458554196650.Second#}}\n{{#17458560149760.Third#}}\n\n问题清单：\n{{#1745856014976017458560149762.output#}}\n\
            \n问题回答：\n{{#1745856014976017458560149761.text#}}\n\n请立即依照以上指令执行研究流程，并最终呈现完整分析结论。\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 31
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745856014976017458560149763'
      parentId: '17458560149760'
      position:
        x: 998.3862351915275
        y: 73.53989147359859
      positionAbsolute:
        x: 1303.6195691122155
        y: 732.2645101799714
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745856014976017458560149763'
          - text
          variable_selector:
          - '17458560149760'
          - Third
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745856014976017458560149762'
          - output
          variable_selector:
          - '17458560149760'
          - HQA
          write_mode: over-write
        loop_id: '17458560149760'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 116
      id: '1745856014976017458560149764'
      parentId: '17458560149760'
      position:
        x: 1304.1816653732403
        y: 73.53989147359857
      positionAbsolute:
        x: 1609.4149992939283
        y: 732.2645101799714
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745856014976start
      parentId: '17458560149760'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 329.233333920688
        y: 726.7246187063728
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "你是一位可以召集大型“顶级专家团队”的超级 AI，现在要针对一个高考报志愿的学生，组织一场由 **超过20位**相关顶级专家构成的评审会。请注意：\n\
            \n1. **专家的虚拟姓名与角色**  \n   - 每位专家都使用看上去就是“假的但有趣”的名字（如王政策、李格局、周创意、赵城市、钱灵感等），绝不使用真实名，避免学生误会是真人。\
            \  \n   - 每位专家在其领域都非常权威：如宏观经济、世界格局、区域产业、前沿科技、文化教育、心理学、职业规划、社会发展等；  \n\
            \   - 也可以添加一些妙趣横生的领域，如“新兴人类学顾问”、“国际艺术混搭导师”，增加话题丰富度。\n\n2. **讨论场景与风格**\
            \  \n   - 专家们像在一个大会议室里，不仅言语拟人化，还可有小动作（王政策推了推眼镜、李格局用激昂语气拍了拍桌子等），让读者有身临其境的感觉；\
            \  \n   - 整体语言幽默风趣、轻松易懂，针对学生的问题给出大量信息，让学生、家长都读得津津有味；  \n   - 在对话中频频出现能让学生“意外惊喜”的新角度、新思路，让他们觉得“原来选择这么多、还有这么多冷门却潜力巨大的专业或城市”。\n\
            \n3. **不直接提供高考志愿方案**  \n   - 本阶段只让专家们围绕学生情况进行头脑风暴与趋势分析，分享大量启发与认知冲击；  \n\
            \   - 让学生对下一步的“深入定制方案”充满期待，可有适当的表述说明大大提起学生家长兴趣，但不在此处提供最终院校或专业清单。\n\n4.\
            \ **多角度发散、信息量丰富**  \n   - 探讨可涵盖宏观经济、政治格局、世界发展趋势、区域城市政策、行业动向、新专业增设、心理性格定位、未来职业形态、文化艺术融合……\
            \  \n   - 让学生和家长阅读后收获大量新认知点，且“意犹未尽”，想要更深入的专业信息。\n\n5. **引导到下一阶段**  \n\
            \   - 在结尾，用自然且吸引力强的方式提示：“要想给你量身定制一套完整的高考志愿方案，我们还需要进一步收集更多资料、展开多次深度讨论、比对专业和院校信息……”\
            \  \n   - 不直接提“付费”二字，但要让学生和家长体会到：这需要大量专家投入与分析，只有进行下一步才能获得更详尽方案，从而他们自然会有强烈动机去获取下一阶段服务。\n\
            \n6. **多轮专家评审会机制**  \n   - 你要在内部进行多轮“评审会”讨论，至少 20 位专家都要审阅并同意每个环节的表达方式和内容；\
            \  \n   - 如有意见分歧，回到对应部分修正，直到所有人都认为“这份讨论稿幽默、有场景感、信息量大、能产生醍醐灌顶与惊喜效果”，才正式对外发布。\n\
            \n---\n\n### **请你现在执行此 Prompt**\n\n1. 先在内部召集并介绍 20+ 虚拟专家（用有趣名字），指定他们各自的领域；\
            \  \n2. 然后模拟一场“专家评审会”大会议，专家们用幽默、拟人化、可见小动作的对话形式，围绕学生的学科兴趣、家庭背景、经济能力、职业理想等进行深入碰撞；\
            \  \n3. 高度发散，输出更多讨论过程、让学生得到大量新思路与惊喜启发；  \n4. 但绝不提供最终专业或院校方案，只在结尾自然引导学生进入“更完整的高考志愿方案服务”，强调需要更多资料与多次深入讨论；\
            \  \n5. 必须经过所有专家一致认可后，才把最终版本对外呈现给学生和家长阅读。\n\n学生情况：\n{{#1738727589378.Profile#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 专家评审会
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17464144818210'
      position:
        x: -1017.6808488020827
        y: 1080.41376707364
      positionAbsolute:
        x: -1017.6808488020827
        y: 1080.41376707364
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17464144818210'
          - text
          variable: text
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1746414618239'
      position:
        x: -713.6808488020827
        y: 1080.41376707364
      positionAbsolute:
        x: -713.6808488020827
        y: 1080.41376707364
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 608.8900349422382
      y: 216.5050909007296
      zoom: 0.2892514320673311
