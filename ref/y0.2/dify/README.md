# 高考志愿填报工作流

本项目使用 LangGraph 实现了基于 Dify 工作流配置的高考志愿填报智能顾问系统。该系统通过五个连续节点处理学生信息，提供从个人特质分析到大学及中长期发展规划的完整指导。

## 系统架构

工作流基于 LangGraph 状态机实现，包含以下核心组件：

1. **工作流状态管理**：使用 `WorkflowState` 类型字典管理整个工作流的状态
2. **节点间信息传递**：每个节点的输出作为下一节点的输入
3. **循环查询机制**：使用 `loop_query.py` 模块实现问题拆分与深度搜索
4. **错误处理与日志**：完整的错误处理和日志机制确保工作流稳定运行

## 工作流节点

系统包含五个顺序执行的节点：

### 节点0：个人特质解读

- **输入**：学生原始档案信息
- **处理**：分析学生的性格、潜能、兴趣和成长诉求
- **输出**：生成结构化的"学生画像"报告

### 节点1：超预期潜力发掘与未来趋势洞察

- **输入**：学生画像
- **处理**：深度评估学生潜力方向，生成问题清单，通过搜索API获取信息
- **输出**：关于学生未来发展方向的深度洞察

### 节点2：个性化专业与院校匹配

- **输入**：前两个节点的输出
- **处理**：生成专业院校匹配相关问题，通过搜索API获取信息
- **输出**：匹配学生特质的专业和院校建议

### 节点3：精准志愿填报方案构建

- **输入**：前三个节点的输出
- **处理**：生成志愿填报方案相关问题，通过搜索API获取信息
- **输出**：具体的高考志愿填报方案

### 节点4：大学及中长期发展规划蓝图

- **输入**：前四个节点的输出
- **处理**：生成长期发展规划相关问题，通过搜索API获取信息
- **输出**：学生大学及未来职业发展的长期规划建议

## 循环查询机制

每个节点（除节点0外）的处理流程如下：

1. 使用 Meta-Prompt 生成一系列高质量问题
2. 解析问题清单为问题列表
3. 对每个问题调用搜索API（Perplexity/BoCha）获取答案
4. 整合答案，用于生成本节点输出

## 优化改进

相比原始实现，本版本工作流有以下优化：

1. **错误处理**：增强的异常处理和恢复机制
2. **搜索提供商切换**：支持多种搜索API（Perplexity/BoCha）
3. **问题提取算法**：改进的问题解析逻辑，更准确地提取问题
4. **并行处理**：支持并行查询多个问题以提高效率
5. **可配置性**：通过环境变量配置模型、搜索提供商等
6. **日志记录**：完整的日志机制便于调试和监控

## 使用方法

### 环境准备

1. 安装依赖：
   ```
   pip install langgraph langchain-openai dotenv requests google-generativeai
   ```

2. 配置环境变量：
   ```
   OPENAI_API_KEY=你的OpenAI密钥
   GOOGLE_API_KEY=你的Google密钥
   PERPLEXITY_API_KEY=你的Perplexity密钥
   BOCHA_API_KEY=你的BoCha密钥
   ```

### 运行测试

```
python test_workflow.py --mode full  # 测试完整工作流
python test_workflow.py --mode node0  # 仅测试节点0
```

## 系统集成

该工作流可以集成到 FastAPI 或其他 Web 框架中，作为志愿填报顾问服务的后端。

## 参考资料

- [LangGraph 文档](https://langchain-ai.github.io/langgraph/)
- Dify 工作流配置：`学生高考报志愿.yml` 和 `循环查询.yml` 