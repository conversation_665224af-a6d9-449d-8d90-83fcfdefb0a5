## 场景
通过ai对话，收集学生画像，生成高考志愿填报建议报告，
## 前置条件
1、每个会话会生成一个报告建议
2、报告建议分为五个章节，每个章节独立生成小报告

## 技术实现
1、报告生成是一个长耗时操作，每个章节创建一个任务
2、前端通过轮询查询每个报告章节生成的进度和结果，用于在页面渲染
3、当这个章节结果展示在页面，用户手动确认后去生成下一个章节
4、任务实现通过阿里云的sae job，去处理任务

---

# 技术方案完善

## 一、业务场景与目标
通过AI对话收集学生画像，自动生成高考志愿填报建议报告。每个报告分为五个独立章节，支持分步生成与用户逐步确认，提升交互体验和系统稳定性。

## 二、系统架构设计

### 1. 总体架构
- **前端**：负责用户交互、进度展示、章节确认与触发下一步生成。
- **后端（API服务）**：负责任务编排、进度查询、结果聚合、异常处理。
- **任务执行（Job/Worker）**：负责实际的AI生成与数据处理，异步执行，解耦主业务流。
- **存储**：用于持久化报告、章节内容、进度状态、用户操作日志等。
- **消息/任务队列**：用于任务分发与状态通知（如RabbitMQ、Redis、阿里云SAE Job等）。

### 2. 关键技术选型
- **AI生成**：调用大模型API（如阿里云、OpenAI等）。
- **任务调度**：阿里云SAE Job，支持弹性扩缩容和失败重试。
- **数据库**：MySQL/PostgreSQL（结构化数据），OSS/对象存储（大文本/附件）。
- **缓存**：Redis（加速进度查询、任务状态同步）。
- **API框架**：FastAPI（高性能、异步、OpenAPI文档友好）。
- **前端**：React/Vue + 轮询/长轮询/WS（进度刷新）。

## 三、详细流程设计

### 1. 报告生成流程
1. 用户发起会话，系统创建报告主记录（report）。
2. 前端展示章节列表，用户逐步确认生成每一章节。
3. 用户点击"生成章节"，API创建章节任务（chapter_task），推送到任务队列。
4. Job/Worker异步拉取任务，调用AI生成章节内容，写入数据库/对象存储，更新任务状态。
5. 前端定时轮询API，查询章节生成进度与结果，渲染页面。
6. 用户确认章节内容后，方可触发下一个章节生成。
7. 所有章节生成并确认后，报告可导出/下载/分享。

### 2. 任务与状态管理
- 每个章节为独立任务，状态包括：待生成、生成中、生成成功、生成失败、已确认。
- 任务失败支持重试，失败原因需记录。
- 用户可查看历史报告与章节内容。

### 3. 进度与异常处理
- 任务状态通过API实时查询，前端友好提示。
- 支持任务超时、失败重试、人工干预。
- 生成失败时，前端可展示错误信息并允许用户重试。

## 四、数据结构设计

### 1. 报告章节任务表（报告章节任务表（report_section_task））
| 字段         | 类型      | 说明                   |
| ------------ | --------- | ---------------------- |
| id           | bigint    | 主键                   |
| report_id    | varchar   | 报告ID（会话ID）       |
| chapter_no   | int       | 章节编号               |
| status       | tinyint   | 状态                   |
| progress     | float     | 进度百分比（0-100）    |
| result       | text      | 章节内容               |
| error_msg    | varchar   | 错误信息               |
| confirmed    | bool      | 用户已确认             |
| created_at   | datetime  | 创建时间               |
| updated_at   | datetime  | 更新时间               |

## 五、接口设计（RESTful）

### 1. 创建报告
- `POST /api/report`
- 返回：report_id

### 2. 查询报告及章节进度
- `GET /api/report/{report_id}`
- 返回：报告基本信息、各章节状态、内容摘要

### 3. 触发章节生成
- `POST /api/report/{report_id}/chapter/{chapter_no}/generate`
- 返回：任务ID、初始状态

### 4. 查询章节进度与内容
- `GET /api/report/{report_id}/chapter/{chapter_no}`
- 返回：章节状态、内容、错误信息

### 5. 用户确认章节
- `POST /api/report/{report_id}/chapter/{chapter_no}/confirm`
- 返回：确认结果

### 6. 导出/下载报告
- `GET /api/report/{report_id}/export`
- 返回：PDF/Word/HTML等

## 六、任务执行与弹性伸缩
- 任务通过阿里云SAE Job异步执行，支持高并发、弹性扩缩容。
- Job需实现幂等性，防止重复执行。
- 支持任务失败自动重试，失败超过阈值后告警。

## 七、安全与合规
- 用户鉴权与权限校验（如OAuth2/JWT）。
- 敏感数据加密存储。
- 日志审计与操作追踪。
- 合规性（如数据留存、导出合规等）。

## 八、监控与运维
- 任务执行监控（成功率、耗时、失败原因）。
- 业务指标监控（报告生成量、用户活跃度等）。
- 告警与自动恢复机制。

## 九、可扩展性与未来规划
- 支持更多AI模型接入。
- 支持多种报告模板与自定义章节。
- 支持多语言、多终端访问。
- 支持批量报告生成与导出。

## 十、技术难点与风险
- AI生成内容的准确性与可控性。
- 长耗时任务的稳定性与幂等性。
- 用户体验（进度反馈、错误提示、交互流畅性）。
- 异常场景（如AI接口超时、任务丢失、数据一致性）。

---

如需详细的类图、接口文档、数据库DDL、异常处理方案等，可进一步细化。如需落地代码结构建议，也可补充说明。

> **数据库说明：**
> 本方案数据库采用 PostgreSQL（PG）。建议：
> - 主键 id 建议使用 bigserial 或 uuid。
> - report_id 可用 varchar 或 uuid，便于与会话ID对接。
> - 建议为 report_id、chapter_no 建联合唯一索引，提升查询效率。
> - 字段类型建议：text 用于大文本，varchar 需指定长度。
> - 时间字段建议用 timestamptz（带时区）。
> - 支持事务，建议所有写操作包裹在事务中。
> - 可利用 PG 的 JSONB 字段扩展章节内容结构。
> - 生产环境建议开启归档、定期备份。

#### PostgreSQL DDL 示例
```sql
CREATE TABLE report_section_task (
    id           bigserial PRIMARY KEY,
    report_id    varchar(64) NOT NULL,
    chapter_no   int NOT NULL,
    status       smallint NOT NULL DEFAULT 0,
    progress     real NOT NULL DEFAULT 0,
    result       text,
    error_msg    varchar(512),
    confirmed    boolean NOT NULL DEFAULT false,
    created_at   timestamptz NOT NULL DEFAULT now(),
    updated_at   timestamptz NOT NULL DEFAULT now(),
    UNIQUE(report_id, chapter_no)
);
CREATE INDEX idx_report_section_task_report_id ON report_section_task(report_id);
```
