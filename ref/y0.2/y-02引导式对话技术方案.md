# 引导式对话技术方案

## 1. 方案概述

### 1.1 背景介绍

引导式对话是一种通过预设路径引导用户完成特定任务的交互模式。相比开放式对话，引导式对话能够更好地控制对话流程，确保用户按照预期路径完成任务，提高交互效率和成功率。

### 1.2 技术栈选择

本方案采用以下技术栈：
- 前端：DeepChat 开源组件
- 后端：LangGraph 工作流引擎
- 通信协议：FastAPI流式响应 (Server-Sent Events)

### 1.3 系统架构

```mermaid
graph TD
    %% 定义各个组件
    User["用户"]
    Browser["用户浏览器"]
    
    %% 前端展示层
    subgraph FrontendLayer["前端展示层"]
        DeepChat["DeepChat前端组件"]
        GuidedButtons["引导式按钮模块"]
        StreamHandler["流式响应处理模块"]
    end
    
    %% API网关层
    subgraph GatewayLayer["API网关层"]
        APIGateway["API网关"]
        Authentication["认证授权"]
        LoadBalancer["负载均衡器"]
    end
    
    %% 业务逻辑层
    subgraph BusinessLayer["业务逻辑层"]
        ChatController["对话控制器"]
        SessionService["会话管理服务"]
        StreamProcessor["FastAPI流式响应处理器"]
        InterruptHandler["中断处理服务"]
    end
    
    %% 工作流引擎层
    subgraph WorkflowLayer["工作流引擎层"]
        LangGraph["LangGraph工作流引擎"]
        DialogControlNodes["对话控制节点"]
        BusinessNodes["业务逻辑节点"]
        InfoNodes["信息补充节点"]
    end
    
    %% 存储层
    subgraph StorageLayer["存储层"]
        SessionDB[("会话状态存储")]
        HistoryDB[("对话历史存储")]
        WorkflowRepo[("工作流定义仓库")]
    end
    
    %% 定义交互关系
    User --> Browser
    Browser --> DeepChat
    
    DeepChat --> GuidedButtons
    DeepChat --> StreamHandler
    
    DeepChat --> APIGateway
    APIGateway --> Authentication
    APIGateway --> LoadBalancer
    
    LoadBalancer --> ChatController
    ChatController --> SessionService
    ChatController --> StreamProcessor
    StreamProcessor --> InterruptHandler
    
    SessionService --> LangGraph
    InterruptHandler --> LangGraph
    
    LangGraph --> DialogControlNodes
    LangGraph --> BusinessNodes
    LangGraph --> InfoNodes
    
    SessionService --> SessionDB
    InterruptHandler --> SessionDB
    ChatController --> HistoryDB
    LangGraph --> WorkflowRepo
    
    %% 反向数据流
    StreamProcessor -.-> DeepChat
    InterruptHandler -.-> DeepChat
    StreamHandler -.-> APIGateway
    GuidedButtons -.-> APIGateway
    
    %% 流程说明
    StreamProcessor --> StreamProcessorNote["流式响应流程: <br/>1. 接收用户请求 <br/>2. 通过SSE建立流式连接 <br/>3. 逐块返回处理结果 <br/>4. 检测中断情况并通知客户端"]
    InterruptHandler --> InterruptNote["中断处理流程: <br/>1. 检测缺失信息 <br/>2. 生成中断请求 <br/>3. 保存当前状态 <br/>4. 通知前端请求补充信息 <br/>5. 接收补充信息后继续流程"]
    LangGraph --> LangGraphNote["LangGraph工作流: <br/>1. 定义状态和节点 <br/>2. 设置条件分支 <br/>3. 支持中断与恢复 <br/>4. 状态共享与传递"]

    %% 样式定义
    classDef frontendStyle fill:#4285f4,color:white
    classDef gatewayStyle fill:#1a73e8,color:white
    classDef businessStyle fill:#0f9d58,color:white
    classDef workflowStyle fill:#34a853,color:white
    classDef dbStyle fill:#5f6368,color:white
    classDef clientStyle fill:#fbbc04,color:black
    classDef noteStyle fill:#f9f9f9,color:black,stroke:#999999

    %% 应用样式
    class DeepChat,GuidedButtons,StreamHandler frontendStyle
    class APIGateway,Authentication,LoadBalancer gatewayStyle
    class ChatController,SessionService,StreamProcessor,InterruptHandler businessStyle
    class LangGraph,DialogControlNodes,BusinessNodes,InfoNodes workflowStyle
    class SessionDB,HistoryDB,WorkflowRepo dbStyle
    class Browser clientStyle
    class StreamProcessorNote,InterruptNote,LangGraphNote noteStyle
```

系统由以下几个主要部分组成：
- 前端展示层：负责用户界面渲染和交互
- API网关层：处理请求路由和认证
- 业务逻辑层：实现引导式对话的核心逻辑
- 工作流引擎层：基于LangGraph的对话流程编排
- 存储层：保存对话历史和用户数据

## 2. 前端设计 (DeepChat)

### 2.1 组件选型

选择DeepChat作为前端组件的原因：
- 开源可定制
- 丰富的消息类型支持
- 良好的样式自定义能力
- 支持Markdown和HTML渲染
- 完善的文件处理功能

### 2.2 界面设计

引导式对话界面包含以下元素：
- 对话历史区域
- 引导式选项按钮区
- 输入框区域（可配置是否允许自由输入）
- 辅助功能区（文件上传、清空对话等）

### 2.3 交互设计

引导式对话的典型交互流程：
1. 系统发送欢迎消息，并提供初始选项
2. 用户选择其中一个选项
3. 系统根据用户选择，继续提供下一步选项或返回结果
4. 重复步骤2-3，直到完成整个对话流程

### 2.4 DeepChat配置示例

```javascript
const chatConfig = {
  // 基本配置
  introMessage: {
    text: "欢迎使用引导式对话系统，请选择以下选项：",
    role: "ai"
  },
  
  // 消息样式配置
  messageStyles: {
    default: {
      shared: { bubble: { borderRadius: "8px" } },
      ai: { bubble: { backgroundColor: "#f0f7ff", color: "#000000" } },
      user: { bubble: { backgroundColor: "#0084ff", color: "#ffffff" } }
    },
    html: {
      shared: { bubble: { padding: "10px" } }
    }
  },
  
  // Markdown渲染配置
  remarkable: {
    html: true,
    breaks: true,
    linkTarget: "_blank"
  },
  
  // 流式响应连接配置
  connect: {
    stream: {
      url: "https://api.example.com/chat/stream",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      }
    }
  }
};
```

### 2.5 引导式按钮实现

利用DeepChat的HTML消息支持，实现引导式按钮：

```javascript
// 示例：发送带有引导按钮的消息
function sendGuidedOptions(options) {
  const buttonsHtml = options.map(opt => 
    `<button class="guided-btn" data-value="${opt.value}">${opt.text}</button>`
  ).join('');
  
  chat.addMessage({
    html: `<div class="guided-options">${buttonsHtml}</div>`,
    role: "ai"
  });
  
  // 绑定按钮点击事件
  document.querySelectorAll('.guided-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const value = btn.getAttribute('data-value');
      handleUserSelection(value);
    });
  });
}
```

### 2.6 流式响应处理

```javascript
// 初始化DeepChat组件时的流式响应处理
function initStreamHandler() {
  const chat = document.getElementById("deep-chat");
  
  // 处理流式响应
  let currentMessage = "";
  
  chat.addEventListener("streamResponse", (event) => {
    const data = event.detail;
    
    // 处理中断情况
    if (data.interrupt) {
      // 显示信息补充请求
      showInfoRequestForm(data.interrupt.required_fields);
      return;
    }
    
    // 处理常规消息片段
    if (data.type === "text") {
      currentMessage += data.content;
      // 更新UI显示部分文本
      updateMessageDisplay(currentMessage);
    } else if (data.type === "options") {
      // 显示选项按钮
      showOptions(data.options);
    } else if (data.type === "end") {
      // 流结束，完成消息显示
      finalizeMessage();
      currentMessage = "";
    }
  });
  
  // 处理信息补充提交
  function submitSupplementaryInfo(info) {
    // 发送补充信息到服务器
    fetch(`https://api.example.com/chat/${sessionId}/continue`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        supplementary_info: info
      })
    });
    // 处理响应...
  }
}
```

## 3. 后端设计 (LangGraph)

### 3.1 LangGraph工作流引擎

LangGraph是一个用于构建和编排LLM应用工作流的框架，具有以下特点：
- 基于有向图的工作流定义
- 节点间的状态共享和传递
- 条件分支和循环支持
- 异步执行能力

### 3.2 工作流节点设计

引导式对话工作流包含以下核心节点类型：

#### 3.2.1 对话控制节点
- `StartNode`: 对话初始化节点
- `RouterNode`: 对话路由决策节点
- `EndNode`: 对话结束处理节点

#### 3.2.2 业务逻辑节点
- `QueryNode`: 数据查询节点
- `ValidationNode`: 输入验证节点
- `TransformNode`: 数据转换节点
- `APICallNode`: 外部服务调用节点

#### 3.2.3 对话生成节点
- `OptionsGeneratorNode`: 生成引导选项
- `ResponseGeneratorNode`: 生成对话响应
- `ErrorHandlerNode`: 处理异常情况

### 3.3 工作流编排示例

```python
from langgraph.graph import StateGraph
from typing import Dict, List, Any

# 定义状态结构
class ChatState:
    messages: List[Dict]
    context: Dict[str, Any]
    current_step: str
    next_options: List[Dict]
    error: str = None

# 创建状态图
workflow = StateGraph(ChatState)

# 添加节点
workflow.add_node("start", start_node)
workflow.add_node("router", router_node)
workflow.add_node("query_data", query_data_node)
workflow.add_node("generate_options", generate_options_node)
workflow.add_node("generate_response", generate_response_node)
workflow.add_node("end", end_node)

# 定义边和条件
workflow.add_edge("start", "router")
workflow.add_conditional_edges(
    "router",
    lambda state: state.current_step,
    {
        "query": "query_data",
        "options": "generate_options",
        "response": "generate_response",
        "end": "end"
    }
)
workflow.add_edge("query_data", "generate_options")
workflow.add_edge("generate_options", "end")
workflow.add_edge("generate_response", "router")

# 编译工作流
compiled_workflow = workflow.compile()
```

### 3.4 状态管理

工作流状态包含以下核心信息：
- 对话历史
- 当前上下文
- 当前步骤
- 可用选项
- 错误信息

状态在节点间传递，确保对话连贯性和上下文一致性。

## 4. API接口设计

### 4.1 WebSocket接口

主要用于实时对话交互，支持以下消息类型：
- `connect`: 建立连接并初始化会话
- `message`: 用户发送消息
- `selection`: 用户选择选项
- `typing`: 输入状态指示
- `response`: 服务端响应
- `options`: 服务端发送选项
- `error`: 错误信息

示例：
```json
// 客户端发送选择消息
{
  "type": "selection",
  "sessionId": "user-session-123",
  "data": {
    "optionId": "opt-1",
    "value": "查询账单"
  }
}

// 服务端响应选项
{
  "type": "options",
  "data": {
    "message": "请选择查询的账单类型",
    "options": [
      {"id": "bill-1", "text": "本月账单", "value": "current"},
      {"id": "bill-2", "text": "上月账单", "value": "last"},
      {"id": "bill-3", "text": "自定义区间", "value": "custom"}
    ]
  }
}
```

### 4.2 RESTful API

用于非实时交互和工作流管理：

#### 4.2.1 会话管理
- `POST /api/sessions`: 创建新会话
- `GET /api/sessions/{sessionId}`: 获取会话信息
- `DELETE /api/sessions/{sessionId}`: 结束会话

#### 4.2.2 对话历史
- `GET /api/sessions/{sessionId}/messages`: 获取对话历史
- `POST /api/sessions/{sessionId}/messages`: 添加消息

#### 4.2.3 工作流管理
- `GET /api/workflows`: 获取可用工作流列表
- `GET /api/workflows/{workflowId}`: 获取工作流定义
- `POST /api/sessions/{sessionId}/workflow`: 设置会话使用的工作流

## 5. 引导式对话流程设计

### 5.1 流程定义格式

引导式对话流程使用JSON格式定义：

```json
{
  "id": "customer_service",
  "name": "客户服务流程",
  "startNode": "welcome",
  "nodes": {
    "welcome": {
      "type": "message",
      "content": "欢迎使用客户服务，请问有什么可以帮您？",
      "options": [
        {"id": "billing", "text": "账单查询", "next": "billing_query"},
        {"id": "complaint", "text": "投诉建议", "next": "complaint_form"},
        {"id": "other", "text": "其他问题", "next": "free_chat"}
      ]
    },
    "billing_query": {
      "type": "form",
      "title": "账单查询",
      "fields": [
        {"id": "month", "label": "查询月份", "type": "select", "options": ["本月", "上月", "更早"]}
      ],
      "next": "show_billing_result"
    },
    // ...更多节点定义
  }
}
```

### 5.2 关键流程示例

#### 5.2.1 欢迎引导流程
1. 系统展示欢迎语和主要功能选项
2. 用户选择所需功能
3. 系统根据选择进入相应子流程

#### 5.2.2 表单收集流程
1. 系统展示表单字段和说明
2. 用户填写必要信息
3. 系统验证输入并提供反馈
4. 完成后进入结果展示或下一步

#### 5.2.3 条件分支流程
1. 系统基于用户输入或上下文进行判断
2. 根据判断结果选择不同的后续流程
3. 为用户提供适合当前情况的选项

#### 5.2.4 混合输入流程
1. 系统提供预设选项的同时允许自由输入
2. 对自由输入进行意图识别和实体提取
3. 根据识别结果决定是否切换到特定流程

## 6. 部署方案

### 6.1 系统架构部署

采用微服务架构，包含以下服务：
- 前端服务：部署DeepChat前端应用
- API网关：处理请求路由和负载均衡
- 对话服务：处理核心对话逻辑
- 工作流引擎：运行LangGraph工作流
- 数据存储服务：存储对话历史和用户数据

### 6.2 扩展性考虑

- 水平扩展：通过增加服务实例应对高并发
- 垂直扩展：为计算密集型节点分配更多资源
- 异步处理：长时间运行的任务使用消息队列
- 缓存机制：常用数据和流程定义进行缓存

### 6.3 监控与运维

- 日志收集：集中收集各服务日志
- 性能监控：监控关键指标如响应时间、吞吐量
- 错误追踪：记录和分析异常情况
- 用户反馈：收集用户使用体验反馈

## 7. 安全性考虑

### 7.1 数据安全
- 传输加密：使用TLS/SSL加密通信
- 存储加密：敏感数据加密存储
- 访问控制：基于角色的权限控制

### 7.2 防攻击措施
- 输入验证：防止注入攻击
- 速率限制：防止DDoS攻击
- 会话保护：防止会话劫持

## 8. 总结与展望

### 8.1 方案优势
- 前端使用成熟的DeepChat组件，降低开发成本
- 后端采用LangGraph灵活编排工作流，便于迭代和扩展
- 引导式对话提高用户体验和任务完成率
- 微服务架构确保系统可扩展性和可维护性

### 8.2 未来规划
- 引入更多AI能力，提升自然语言理解
- 开发更多行业专用对话流程模板
- 提供可视化工作流编辑工具
- 支持多模态交互（语音、图像）

## 9. 附录

### 9.1 DeepChat接口参考
DeepChat提供丰富的配置选项和API，详细文档请参考：
- 消息样式配置：[DeepChat Messages Styles](https://deepchat.dev/docs/messages/styles)
- API接口文档：[DeepChat API](https://deepchat.dev/docs)

### 9.2 LangGraph参考
LangGraph是一个强大的工作流编排工具，具体使用方法请参考官方文档。

# FastAPI流式响应接口

from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
import asyncio
import json

app = FastAPI()

@app.post("/chat/stream")
async def chat_stream(request: Request) -> StreamingResponse:
    # 解析请求数据
    data = await request.json()
    message = data.get("message", "")
    session_id = data.get("sessionId", "")
    
    # 获取工作流处理器
    processor = get_workflow_processor(session_id)
    
    async def generate():
        try:
            # 执行工作流并生成流式响应
            async for chunk in processor.process_stream(message):
                # 发送消息块
                yield f"data: {json.dumps(chunk)}\n\n"
                
                # 检查是否需要中断（信息补充）
                if 'interrupt' in chunk:
                    break
            
            # 响应结束
            yield f"data: {json.dumps({'type': 'end'})}\n\n"
            
        except Exception as e:
            # 错误处理
            error_data = {"type": "error", "message": str(e)}
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream"
    )

# 中断后继续处理接口
@app.post("/chat/{session_id}/continue")
async def continue_chat(session_id: str, request: Request) -> StreamingResponse:
    # 解析用户补充的信息
    data = await request.json()
    supplementary_info = data.get("supplementary_info", {})
    
    # 获取处理器
    processor = get_workflow_processor(session_id)
    
    async def generate():
        try:
            # 继续处理流程
            async for chunk in processor.continue_stream(supplementary_info):
                yield f"data: {json.dumps(chunk)}\n\n"
            
            # 响应结束
            yield f"data: {json.dumps({'type': 'end'})}\n\n"
            
        except Exception as e:
            error_data = {"type": "error", "message": str(e)}
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream"
    ) 