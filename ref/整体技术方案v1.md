# YAI Chat 技术架构文档

## 1. 项目概述

YAI Chat 是一个基于人工智能的聊天服务系统，采用现代化的微服务架构设计，支持异步处理和实时流式响应。系统集成了多种AI模型和工具，提供智能对话服务。

## 2. 技术栈

### 2.1 核心框架
- Python 3.x
- FastAPI/Starlette (异步Web框架)
- LangChain (AI应用框架)
- SSE (Server-Sent Events，用于流式响应)

### 2.2 数据存储
- Redis (缓存和会话管理)
- GDB (图数据库)
- opensearch (搜索引擎)
- PostgreSQL (关系型数据库)

### 2.3 配置管理
- Nacos (配置中心)
- Pydantic (数据验证)

### 2.4 工具和组件
- Snowflake (分布式ID生成)
- Lucas Common Components (通用组件库)

## 3. 系统架构

### 3.1 整体架构
系统采用清晰的分层架构设计，遵循领域驱动设计(DDD)原则，主要包含以下层次：

#### 3.1.1 适配器层 (src/adapter)
- 负责外部接口适配和转换
- 包含控制器(Controller)、视图对象(VO)和转换器(VOConverter)
- 遵循REST API设计规范
- 使用标准响应格式 `GatewayResponse[T]`
- 处理请求验证和参数校验

#### 3.1.2 应用层 (src/app)
- 实现核心业务用例
- 协调领域对象和基础设施
- 处理事务边界
- 实现业务流程编排
- 管理AI代理协作

#### 3.1.3 领域层 (src/domain)
- 定义核心业务模型和规则
- 实现领域逻辑
- 包含领域服务
- 定义领域事件
- 维护业务规则的一致性

#### 3.1.4 基础设施层 (src/infrastructure)
- 提供技术实现细节
- 实现数据持久化
- 处理外部服务集成
- 提供技术能力支持
- 实现跨层功能

### 3.2 核心模块

#### 3.2.1 适配器层 (src/adapter)
- 控制器：处理HTTP请求和响应
- VO对象：定义数据传输结构
- 转换器：实现VO与领域对象的转换
- 异常处理：统一的异常处理机制

#### 3.2.2 应用层 (src/app)
- 服务实现：核心业务逻辑
- 用例编排：业务流程协调
- 事务管理：确保数据一致性
- 事件处理：处理领域事件

#### 3.2.3 领域层 (src/domain)
- 实体：核心业务对象
- 值对象：不可变业务概念
- 领域服务：复杂业务逻辑
- 仓储接口：数据访问抽象

#### 3.2.4 基础设施层 (src/infrastructure)
- 数据访问：实现仓储接口
- 外部服务：集成第三方服务
- 消息队列：异步通信
- 缓存实现：性能优化

#### 3.2.5 配置管理 (src/config)
- 系统配置：应用配置管理
- 环境配置：多环境支持
- 外部服务配置：第三方服务配置

#### 3.2.6 工具类 (src/utils)
- 通用工具：辅助功能实现
- 工具类：可复用功能
- 常量定义：系统常量

## 4. 关键特性

### 4.1 异步处理
- 使用Python的async/await实现异步操作
- 支持并发请求处理
- 流式响应支持

### 4.2 会话管理
- 基于Redis的会话状态管理
- 支持多轮对话
- 会话持久化

### 4.3 智能代理
- 基于LangChain的智能代理系统
- 支持多代理协作
- 可扩展的代理架构

### 4.4 配置管理
- 基于Nacos的配置中心
- 支持动态配置更新
- 多环境配置支持

## 5. 部署架构

### 5.1 容器化
- 使用Docker进行容器化部署
- 支持容器编排
- 环境一致性保证

### 5.2 服务依赖
- 数据库服务
- 缓存服务
- 搜索引擎
- 配置中心

## 6. 安全特性

- 配置信息加密
- 会话安全
- 访问控制
- 数据验证

## 7. 监控和日志

- 集成日志系统
- 性能监控
- 错误追踪
- 运行状态监控

## 8. 扩展性

- 模块化设计
- 插件化架构
- 可扩展的AI模型集成
- 灵活的配置管理

## 9. LangGraph流程编排

### 9.1 整体架构
系统使用LangGraph实现了一个基于状态机的智能代理协作系统，采用分层设计原则：

```mermaid
graph TD
    A[用户输入] --> B[zhiyuanCoordinator]
    B --> C{路由决策}
    C -->|招生政策| D[AdmissionPolicySubAgent]
    C -->|学校信息| E[SchoolSubAgent]
    C -->|专业信息| F[MajorSubAgent]
    D --> B
    E --> B
    F --> B
    B -->|完成| G[结束]
```

### 9.2 核心组件

#### 9.2.1 协调器 (zhiyuanCoordinator)
- 位于应用层，负责业务流程编排
- 实现代理之间的状态转换和协调
- 管理会话状态和上下文
- 基于Redis实现状态持久化
- 处理异常和错误恢复

#### 9.2.2 专业代理
1. **招生政策代理 (AdmissionPolicySubAgent)**
   - 领域层实现，处理招生政策相关业务逻辑
   - 基于政策文档进行智能回答
   - 支持流式响应
   - 维护政策知识库

2. **学校代理 (SchoolSubAgent)**
   - 领域层实现，处理学校信息相关业务逻辑
   - 提供学校详情和建议
   - 维护学校知识库
   - 支持多维度学校分析

3. **专业代理 (MajorSubAgent)**
   - 领域层实现，处理专业信息相关业务逻辑
   - 提供专业分析和建议
   - 维护专业知识库
   - 支持专业匹配度分析

### 9.3 状态管理

#### 9.3.1 状态定义
```python
class zhiyuanState(MessagesState):
    conversationId: str  # 会话标识
    message_id: str      # 消息标识
    isInit: bool        # 初始化标志
    nextAgents: list[str]  # 下一个执行的代理列表
    messages: list[dict]   # 消息历史
    bizRuntimeBO: BizRuntimeBO  # 业务运行时对象
```

#### 9.3.2 状态流转
1. **初始化状态**
   - 创建新的会话ID
   - 初始化状态对象
   - 设置初始代理
   - 建立会话上下文

2. **状态转换**
   - 基于当前状态决定下一个代理
   - 维护代理执行顺序
   - 处理状态更新
   - 实现状态验证

3. **状态持久化**
   - 使用Redis存储会话状态
   - 支持状态恢复和继续
   - 实现会话上下文管理
   - 处理状态过期和清理

### 9.4 流程控制

#### 9.4.1 代理路由
- 基于用户输入类型选择代理
- 支持多代理协作
- 实现动态代理切换
- 处理代理优先级

#### 9.4.2 流式处理
- 支持实时响应
- 实现渐进式输出
- 优化用户体验
- 处理流式异常

#### 9.4.3 错误处理
- 异常捕获和恢复
- 超时处理
- 状态回滚
- 错误日志记录

### 9.5 扩展机制

#### 9.5.1 代理扩展
- 支持添加新的专业代理
- 实现代理注册机制
- 保持系统可扩展性
- 提供代理生命周期管理

#### 9.5.2 状态扩展
- 支持自定义状态属性
- 实现状态验证
- 提供状态转换钩子
- 支持状态迁移策略 