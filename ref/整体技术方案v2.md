# 高考志愿填报顾问系统技术方案

## 1. 系统架构

### 1.1 技术栈选择

- **核心框架**：LangGraph
- **LLM模型**：
  - Claude 3 Sonnet (主要处理各部分内容生成)
  - Gemini Pro (处理最终专家评审会总结)
- **开发语言**：Python 3.9+
- **依赖管理**：pip + requirements.txt

### 1.2 系统流程图

#### 1.2.1 主流程

```mermaid
graph TD
    A[开始] --> B[初始化状态]
    B --> C[第一阶段: 超预期潜力发掘]
    C --> D[第二阶段: 专业与院校推荐]
    D --> E[第三阶段: 志愿填报方案]
    E --> F[第四阶段: 发展规划]
    F --> G[最终阶段: 专家评审会总结]
    G --> H[结束]

    subgraph 状态管理
        I[GraphState] --> J[消息列表]
        I --> K[当前步骤]
        I --> L[各部分内容]
    end

    subgraph 每阶段处理流程
        M[创建提示模板] --> N[调用Claude模型]
        N --> O[更新状态]
        O --> P[确定下一步]
    end
```

#### 1.2.2 状态转换流程

```mermaid
stateDiagram-v2
    [*] --> first
    first --> second: 完成第一部分
    second --> third: 完成第二部分
    third --> fourth: 完成第三部分
    fourth --> final: 完成第四部分
    final --> [*]: 生成最终报告

    state first {
        [*] --> 分析潜力
        分析潜力 --> 挖掘机会
        挖掘机会 --> [*]
    }

    state second {
        [*] --> 专业分析
        专业分析 --> 职业规划
        职业规划 --> 政策考虑
        政策考虑 --> [*]
    }

    state third {
        [*] --> 传统方案
        传统方案 --> AI方案
        AI方案 --> 平衡建议
        平衡建议 --> [*]
    }

    state fourth {
        [*] --> 大学规划
        大学规划 --> 短期发展
        短期发展 --> 长期规划
        长期规划 --> [*]
    }

    state final {
        [*] --> 专家评审
        专家评审 --> 综合建议
        综合建议 --> [*]
    }
```

#### 1.2.3 数据流向图

```mermaid
flowchart TD
    subgraph Input
        A[学生基本信息] --> B[GraphState初始化]
    end

    subgraph Processing
        B --> C{第一阶段}
        C -->|更新状态| D{第二阶段}
        D -->|更新状态| E{第三阶段}
        E -->|更新状态| F{第四阶段}
        F -->|更新状态| G{最终阶段}
    end

    subgraph Models
        C -.->|调用| H[Claude]
        D -.->|调用| H
        E -.->|调用| H
        F -.->|调用| H
        G -.->|调用| I[Gemini]
    end

    subgraph State
        J[消息历史]
        K[当前步骤]
        L[各部分内容]
        
        C -->|写入| L
        D -->|写入| L
        E -->|写入| L
        F -->|写入| L
        G -->|写入| L
        
        H -->|响应| J
        I -->|响应| J
    end

    subgraph Output
        L --> M[最终报告]
    end
```

#### 1.2.4 组件交互图

```mermaid
sequenceDiagram
    participant Client
    participant Workflow
    participant State
    participant Claude
    participant Gemini
    
    Client->>Workflow: 提供学生信息
    Workflow->>State: 初始化状态
    
    loop 四个主要阶段
        Workflow->>State: 获取当前状态
        Workflow->>Claude: 发送提示和上下文
        Claude-->>Workflow: 返回生成内容
        Workflow->>State: 更新状态
    end
    
    Workflow->>Gemini: 发送所有内容生成总结
    Gemini-->>Workflow: 返回专家评审报告
    Workflow->>State: 更新最终报告
    Workflow-->>Client: 返回完整报告
```

## 2. 核心组件设计

### 2.1 状态管理 (GraphState)

```python
class GraphState(TypedDict):
    messages: List[BaseMessage]      # 消息历史
    next_step: str                   # 下一步骤
    current_section: str             # 当前部分
    profile: str                     # 学生信息
    first_section: str               # 第一部分内容
    second_section: str              # 第二部分内容
    third_section: str               # 第三部分内容
    fourth_section: str              # 第四部分内容
    final_report: str                # 最终报告
```

### 2.2 工作流节点

1. **first**: 超预期潜力发掘
   - 分析学生潜在高价值方向
   - 挖掘超出预期的职业前景

2. **second**: 专业与院校推荐
   - 从专业到职业分析
   - 从职业到专业逆向推导
   - 考虑高考政策因素

3. **third**: 志愿填报方案
   - 传统分数优先方案
   - AI潜力最大化方案
   - 平衡学生和家长视角

4. **fourth**: 发展规划
   - 大学阶段规划
   - 短期职业发展规划
   - 中长期前景分析

5. **final**: 专家评审会总结
   - 由虚拟专家团队提供多角度建议
   - 综合评估和建议

## 3. 关键实现细节

### 3.1 工作流控制

```python
def should_continue(state: GraphState) -> str:
    """确定下一步骤"""
    return state["next_step"]
```

### 3.2 节点处理

```python
def process_section(state: GraphState, section_name: str) -> GraphState:
    """处理每个部分的内容生成"""
    chain = create_section_chain(section_name)
    result = chain.invoke(variables)
    state[f"{section_name}_section"] = result.content
    # 更新下一步
    return state
```

### 3.3 提示模板管理

- 使用 `ChatPromptTemplate` 管理提示模板
- 为每个部分定义专门的提示模板
- 在提示中包含上下文信息

## 4. 扩展性设计

### 4.1 可扩展点

1. **模型替换**
   - 可以轻松替换为其他LLM模型
   - 支持不同的模型组合

2. **节点扩展**
   - 可以添加新的处理节点
   - 支持自定义节点逻辑

3. **提示模板定制**
   - 可以根据需求修改提示模板
   - 支持多语言模板

### 4.2 错误处理

1. **模型调用错误**
   - 重试机制
   - 错误日志记录

2. **状态管理错误**
   - 状态验证
   - 异常状态处理

## 5. 部署要求

### 5.1 环境要求

- Python 3.9+
- 足够的内存资源（建议4GB以上）
- 稳定的网络连接

### 5.2 API密钥配置

- Anthropic API密钥
- Google API密钥

### 5.3 依赖安装

```bash
pip install -r requirements.txt
```

## 6. 性能考虑

### 6.1 响应时间

- 每个部分的生成时间：约15-30秒
- 总流程完成时间：约2-3分钟

### 6.2 并发处理

- 支持多用户并发请求
- 每个请求独立的状态管理

## 7. 安全性考虑

### 7.1 API密钥保护

- 使用环境变量管理API密钥
- 避免密钥硬编码

### 7.2 数据安全

- 不保存敏感信息
- 处理完成后清理状态

## 8. 监控和日志

### 8.1 日志记录

- 记录每个步骤的执行状态
- 记录错误和异常情况

### 8.2 性能监控

- 监控API调用延迟
- 监控资源使用情况

## 9. 后续优化方向

1. 添加缓存机制
2. 优化提示模板
3. 增加更多自定义选项
4. 添加批量处理功能
5. 实现结果持久化存储 