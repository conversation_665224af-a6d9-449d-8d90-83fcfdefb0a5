#!/usr/bin/env python3
"""
测试 Redis 事件循环修复

验证新的 Redis 客户端是否解决了事件循环问题。
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_redis_v2_basic():
    """测试 Redis V2 基础功能"""
    print("🔍 测试 Redis V2 基础功能...")
    
    try:
        from src.infrastructure.session.redis_client_v2 import RedisClientV2
        
        # 获取客户端实例
        redis_client = await RedisClientV2.get_instance()
        
        print(f"    Redis V2 初始化状态: {redis_client._initialized}")
        print(f"    Redis V2 连接对象: {redis_client._redis}")
        print(f"    PubSub 对象: {redis_client._pubsub}")
        
        # 测试连接
        if redis_client._redis:
            try:
                await redis_client._redis.ping()
                print("    ✅ Redis V2 连接正常")
                return True
            except Exception as e:
                print(f"    ❌ Redis V2 连接失败: {e}")
                return False
        else:
            print("    ❌ Redis V2 连接对象为空")
            return False
            
    except Exception as e:
        print(f"❌ Redis V2 基础功能测试失败: {e}")
        traceback.print_exc()
        return False


async def test_redis_v2_pubsub():
    """测试 Redis V2 发布订阅功能"""
    print("\n🔍 测试 Redis V2 发布订阅功能...")
    
    try:
        from src.infrastructure.session.redis_client_v2 import RedisClientV2
        
        redis_client = await RedisClientV2.get_instance()
        
        # 收集接收到的消息
        received_messages = []
        
        async def test_callback(channel: str, message):
            received_messages.append((channel, message))
            print(f"    📨 收到消息: {channel} -> {message}")
        
        # 订阅测试频道
        test_channel = "test_channel_v2"
        await redis_client.subscribe(test_channel, test_callback, "test_subscriber")
        print(f"    ✅ 已订阅频道: {test_channel}")
        
        # 等待订阅生效
        await asyncio.sleep(1.0)
        
        # 发布测试消息
        test_messages = [
            {"test": "message_1", "timestamp": time.time()},
            {"test": "message_2", "timestamp": time.time()},
            "simple_string_message"
        ]
        
        for i, msg in enumerate(test_messages):
            await redis_client.publish(test_channel, msg)
            print(f"    📤 发送消息 {i+1}: {msg}")
            await asyncio.sleep(0.5)
        
        # 等待消息接收
        await asyncio.sleep(2.0)
        
        # 检查结果
        if len(received_messages) >= len(test_messages):
            print(f"    ✅ 成功接收 {len(received_messages)} 条消息")
            return True
        else:
            print(f"    ❌ 消息丢失: 发送 {len(test_messages)}, 接收 {len(received_messages)}")
            return False
            
    except Exception as e:
        print(f"❌ Redis V2 发布订阅测试失败: {e}")
        traceback.print_exc()
        return False


async def test_message_broadcaster_v2():
    """测试使用 V2 的消息广播器"""
    print("\n🔍 测试消息广播器 V2...")
    
    try:
        from src.infrastructure.session.message_broadcaster import MessageBroadcasterFactory
        
        # 创建广播器
        broadcaster = await MessageBroadcasterFactory.create()
        
        print(f"    广播器类型: {type(broadcaster).__name__}")
        
        # 收集消息
        received_messages = []
        
        async def broadcast_callback(channel: str, message):
            received_messages.append((channel, message))
            print(f"    📨 广播器收到消息: {channel} -> {message}")
        
        # 订阅频道
        test_channel = "broadcast_test_v2"
        success = await broadcaster.subscribe(test_channel, broadcast_callback, "broadcast_subscriber")
        
        if success:
            print(f"    ✅ 广播器订阅成功: {test_channel}")
            
            # 等待订阅生效
            await asyncio.sleep(1.0)
            
            # 发布消息
            test_message = {"broadcast": "test", "version": "v2", "timestamp": time.time()}
            publish_success = await broadcaster.publish(test_channel, test_message)
            
            if publish_success:
                print(f"    ✅ 广播器发布成功: {test_message}")
                
                # 等待接收
                await asyncio.sleep(2.0)
                
                if received_messages:
                    print(f"    ✅ 广播器测试成功，收到 {len(received_messages)} 条消息")
                    return True
                else:
                    print("    ❌ 广播器未收到消息")
                    return False
            else:
                print("    ❌ 广播器发布失败")
                return False
        else:
            print("    ❌ 广播器订阅失败")
            return False
            
    except Exception as e:
        print(f"❌ 消息广播器 V2 测试失败: {e}")
        traceback.print_exc()
        return False


async def test_chat_processor_with_v2():
    """测试 ChatProcessor 使用 V2"""
    print("\n🔍 测试 ChatProcessor 使用 V2...")
    
    try:
        from src.infrastructure.session.chat_processor import ChatProcessor
        
        # 获取 ChatProcessor 实例
        chat_processor = ChatProcessor.get_instance()
        
        # 等待初始化
        await asyncio.sleep(3.0)
        
        # 运行诊断
        diagnosis = await chat_processor.diagnose_redis_messaging()
        
        print("    📊 ChatProcessor 诊断结果:")
        for key, value in diagnosis.items():
            status = "✅" if value else "❌"
            if isinstance(value, list):
                status = "✅" if value else "❌"
                print(f"      {key}: {status} {value}")
            elif isinstance(value, bool):
                print(f"      {key}: {status}")
            else:
                print(f"      {key}: {value}")
        
        # 检查关键指标
        success = (
            diagnosis.get("broadcaster_ready", False) and
            diagnosis.get("redis_connection", False) and
            diagnosis.get("subscribed_channels", [])
        )
        
        if success:
            print("    ✅ ChatProcessor V2 集成成功")
            return True
        else:
            print("    ❌ ChatProcessor V2 集成失败")
            return False
            
    except Exception as e:
        print(f"❌ ChatProcessor V2 测试失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🚀 Redis 事件循环修复验证")
    print("=" * 50)
    
    results = {}
    
    # 运行测试
    results["redis_v2_basic"] = await test_redis_v2_basic()
    results["redis_v2_pubsub"] = await test_redis_v2_pubsub()
    results["broadcaster_v2"] = await test_message_broadcaster_v2()
    results["chat_processor_v2"] = await test_chat_processor_with_v2()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n🎯 修复状态:")
    if all_passed:
        print("  🎉 Redis 事件循环问题已修复！")
        print("  ✅ 所有测试都通过了")
        print("  📝 建议:")
        print("    1. 部署新版本的 Redis 客户端")
        print("    2. 监控生产环境中的错误日志")
        print("    3. 如有问题可回滚到原版本")
    else:
        print("  ❌ 仍有问题需要解决")
        print("  🔧 建议:")
        print("    1. 检查失败的测试项")
        print("    2. 查看详细错误日志")
        print("    3. 确保 Redis 服务正常运行")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        traceback.print_exc()
        sys.exit(1)
