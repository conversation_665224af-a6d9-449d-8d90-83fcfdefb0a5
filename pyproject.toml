[tool.import-linter]
root_package = "src"

[[tool.import-linter.contracts]]
name = "DDD 分层架构约束"
type = "layers"
layers = [
    "src.adapter",
    "src.app", 
    [
        "src.domain",
        "src.exe", 
        "src.infrastructure"
    ]
]

[[tool.import-linter.contracts]]
name = "domain 层纯净性约束"
type = "forbidden"
source_modules = ["src.domain"]
forbidden_modules = [
    "src.adapter",
    "src.app", 
    "src.exe",
    "src.infrastructure"
]

[tool.pytest.ini_options]
testpaths = ["tests", "scripts"]
python_files = ["test_*.py", "check_*.py"]
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')"
]

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta" 