from typing import Optional

from lucas_common_components.nacos.annotate.config_annotate import nacos_config
from lucas_common_components.nacos.model.base_config import LucasBaseConfig
from pydantic import BaseModel


class RestConfig(BaseModel):
    account: Optional[str] = None
    recruitment: Optional[str] = None
    auth: Optional[str] = None
    user: Optional[str] = None


class RedisConfig(BaseModel):
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    db: Optional[int] = None
    max_connections: Optional[int] = None


class OpenSearchConfig(BaseModel):
    endpoint: str
    protocol: str
    access_key_id: str
    access_key_secret: str
    app_name: str


class PostgresqlConfig(BaseModel):
    host: Optional[str] = None
    port: Optional[int] = None
    user: Optional[str] = None
    password: Optional[str] = None
    database: Optional[str] = None
    pool_size: Optional[int] = None
    connection_timeout: Optional[int] = None


class SAEJobConfig(BaseModel):
    accessKeySecret: Optional[str] = None
    accessKeyId: Optional[str] = None
    endpoint: Optional[str] = None
    jobIdConfig: Optional[dict[str, str]] = None


@nacos_config(data_id="yai-chat")
class AIChatAppConfig(LucasBaseConfig):
    rest: Optional[RestConfig] = None
    openSearchConfig: Optional[OpenSearchConfig] = None
    redis: Optional[RedisConfig] = None
    postgresql: Optional[PostgresqlConfig] = None
    saeJob: Optional[SAEJobConfig] = None
