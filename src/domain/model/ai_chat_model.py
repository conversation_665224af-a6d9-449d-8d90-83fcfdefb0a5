from typing import Optional, Any, Dict
from pydantic import BaseModel


class MessageStatusBO(BaseModel):
    user_input: Optional[Dict] = None
    disable: Optional[bool] = None
    complete: Optional[bool] = None
    progress: Optional[int] = None
    next_interval: Optional[int] = None


class CustomBO(BaseModel):
    message_type: str
    message_id: str
    message_data: Optional[Any] = None
    message_status: Optional[MessageStatusBO] = None


class AiChatResultBO(BaseModel):
    text: Optional[str] = None
    html: Optional[str] = None
    custom: Optional[CustomBO] = None


class ReportContentResultBO(BaseModel):
    """报告内容查询结果"""

    success: bool
    task_id: str
    task_name: str
    content: Optional[str] = None
    generated_at: Optional[str] = None
