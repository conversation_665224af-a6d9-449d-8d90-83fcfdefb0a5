"""
AI聊天Gateway接口

定义AI聊天相关的数据访问接口。
按照DDD规范，Domain层定义接口，Exe层实现接口。
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any, TYPE_CHECKING

from src.domain.model.ai_chat_model import CustomBO

# 避免循环导入，仅在类型检查时导入
if TYPE_CHECKING:
    from src.infrastructure.db.models.ai.AIConversation import AIConversation
    from src.infrastructure.db.models.ai.AIMessage import AIMessage
    from src.infrastructure.db.models.ai.ReportTask import ReportTask


class AIChatGateway(ABC):
    """AI聊天Gateway接口"""

    @abstractmethod
    async def get_conversation_by_id(self, conversation_id: int) -> "Optional[AIConversation]":
        """根据ID获取会话信息"""
        pass

    @abstractmethod
    async def update_conversation_timestamp(self, conversation_id: int) -> None:
        """更新会话时间戳"""
        pass

    @abstractmethod
    async def get_messages_by_conversation_id(self, conversation_id: int, limit: int = 10) -> "List[AIMessage]":
        """根据会话ID获取消息列表"""
        pass

    @abstractmethod
    async def save_user_message(self, conversation_id: int, messages: List[Dict[str, Any]]) -> None:
        """保存用户消息"""
        pass

    @abstractmethod
    async def save_ai_text_message(self, text: str, conversation_id: int, message_id: int) -> None:
        """保存AI文本消息"""
        pass

    @abstractmethod
    async def save_ai_custom_message(self, conversation_id: int, message_id: int, custom: CustomBO) -> None:
        """保存AI自定义消息"""
        pass

    @abstractmethod
    async def get_report_task_by_id(self, task_id: str) -> "Optional[ReportTask]":
        """根据任务ID获取报告任务"""
        pass

    @abstractmethod
    async def get_user_profile(self, uniq_user_id: str) -> Optional[Any]:
        """获取用户档案信息"""
        pass

    @abstractmethod
    async def get_checkpoint_data(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取检查点数据"""
        pass 