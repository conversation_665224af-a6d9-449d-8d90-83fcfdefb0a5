import datetime

import pytz
from lucas_common_components.middlewares import UniqTenantUserInfo

import json
from typing import Optional, List

from src.adapter.vo.ai_chat_model import C<PERSON><PERSON>hat<PERSON>rgVO, ConversationVO
from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.utils.snow_flake import Snowflake

snowflake = Snowflake(worker_id=0, datacenter_id=0)


class AIConversationService:
    def __init__(self):
        pass

    @staticmethod
    async def update_time(conversation_id):
        await AIConversationRepository.update(
            conversation_id,
            {
                "updated_at": datetime.datetime.now(tz=pytz.timezone("Asia/Shanghai")),
            },
        )


    @staticmethod
    async def create_conversation(
        request: CreateChatArgVO, context: UniqTenantUserInfo
    ) -> Optional[str]:
        conversation_id = int(snowflake.generate())
        conversation_context = {"text": request.input}
        if request.conversation_action is not None:
            conversation_context["conversation_action"] = request.conversation_action

        conversation_data = {
            "id": conversation_id,
            "title": request.input,
            "context": json.dumps(conversation_context),
            "uni_user_id": int(context.uniqUserId),
            "created_at": datetime.datetime.now(tz=pytz.timezone("Asia/Shanghai")),
        }

        await AIConversationRepository.create(conversation_data)
        return str(conversation_id)

    @staticmethod
    async def update_conversation_action(
        conversation_id: int,
        conversation_action: str,
    ) -> Optional[str]:
        conversation = await AIConversationRepository.get_by_id(conversation_id)

        conversation.context["conversation_action"] = conversation_action

        await AIConversationRepository.update(
            conversation_id,
            {
                "context": json.dumps(conversation.context),
            },
        )
        return str(conversation_id)

    @staticmethod
    async def get_conversations_by_user(
        context: UniqTenantUserInfo,
        sort_by: str = "updated_at",
    ) -> List[ConversationVO]:
        """
        根据当前用户获取会话列表
        Args:
            context: 用户上下文
            sort_by: 排序字段，可选值为 "created_at" 或 "updated_at"，默认为 "updated_at"
        """
        conversations = await AIConversationRepository.get_by_user_id(
            int(context.uniqUserId),
            sort_by=sort_by
        )

        result = []
        for conv in conversations:
            conversation_vo = ConversationVO(
                id=str(conv.id),
                title=conv.title,
                context=conv.context,
                created_at=conv.created_at.isoformat() if conv.created_at else None,
                updated_at=conv.updated_at.isoformat() if conv.updated_at else None,
            )
            result.append(conversation_vo)

        return result
