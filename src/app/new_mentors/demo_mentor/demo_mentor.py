"""Demo Mentor - 使用新的轻量级架构

演示如何使用 InterruptableMentor 基类。
这种设计：
1. 实现了 Mentor 内部状态与父工作流状态的隔离。
2. 强制子类定义清晰的状态转换接口。
3. 专门为 interrupt 机制优化，结构更清晰。
"""

from typing import Dict, Any, Type, Union, Optional

from langgraph.graph import StateGraph, END
from langgraph.types import Command, Checkpointer

from src.app.new_mentors.common.interruptable_mentor import InterruptableMentor
from src.app.zhiyuan.constant import AgentTypeEnum
from .models.model import DemoMentorState, demo_mentor_command_update
from .modules.ask_question import AskQuestionNode
from .modules.handle_answer import HandleAnswerNode
from langgraph.graph.state import CompiledStateGraph


class DemoMentor(InterruptableMentor):
    """Demo Mentor - 适配了新的状态隔离架构
    
    演示了如何实现新的 InterruptableMentor 接口:
    - `_workflow_state_cls`: 定义自己的状态类。
    - `_map_parent_state_to_workflow_state`: 定义如何从父状态初始化自己的状态。
    - `_map_workflow_state_to_parent_update`: 定义如何将自己的最终状态映射回父级的更新。
      支持两种模式：返回 Command 对象或直接返回状态更新字典。
    """

    @property
    def _module_name(self) -> str:
        return "DemoMentor"

    @property
    def _workflow_state_cls(self) -> Type[DemoMentorState]:
        """返回当前工作流的状态类"""
        return DemoMentorState

    async def _create_workflow_graph(self, checkpointer: Optional[Checkpointer] = None) -> CompiledStateGraph:
        """创建并编译工作流图"""
        workflow = StateGraph(self._workflow_state_cls)

        # 添加业务节点
        workflow.add_node("AskQuestionNode", AskQuestionNode())
        workflow.add_node("HandleAnswerNode", HandleAnswerNode())

        # 设置线性流程：问题 -> (interrupt) -> 处理回答 -> 结束
        workflow.set_entry_point("AskQuestionNode")
        workflow.add_edge("AskQuestionNode", "HandleAnswerNode")
        workflow.add_edge("HandleAnswerNode", END)

        # 直接编译，不再依赖 MentorUtils
        return workflow.compile(checkpointer=checkpointer)

    def _map_parent_state_to_workflow_state(self, parent_state: Dict[str, Any]) -> Dict[str, Any]:
        """将父图状态映射到当前工作流的初始状态
        
        在此 Demo 中，我们假设父状态已经包含了初始化 DemoMentorState 所需的字段。
        在实际应用中，这里应该有更复杂的逻辑，
        例如从父状态的不同部分提取数据，并组装成 DemoMentorState。
        """
        # 为了演示，直接使用父状态。
        # 注意：这里应该根据 DemoMentorState 的定义，只选择需要的字段，以实现真正的隔离。
        return parent_state

    async def _map_workflow_state_to_parent_update(self, workflow_state: Dict[str, Any]) -> Union[Command, Dict[str, Any]]:
        """将工作流最终状态映射为父图的更新
        
        这个例子演示了返回 Command 对象的情况。
        如果父图使用条件边，可以直接返回状态更新字典。
        """
        # 示例1：返回 Command 对象（用于命令驱动的父工作流）
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await demo_mentor_command_update(workflow_state),
        )
        
        # 示例2：返回状态更新字典（用于条件边驱动的父工作流）
        # 取消下面的注释，并注释掉上面的 return 语句，以使用条件边模式
        # return {
        #     "mentor_result": await demo_mentor_command_update(workflow_state),
        #     "mentor_status": "completed",
        #     "next_step": "determine_next_mentor"
        # }


# 代码量对比：
# 旧架构（继承 BaseMentor）：83 行
# 新架构（InterruptableMentor + MentorUtils）：约 60 行
# 减少了 28% 的代码量，同时获得了更好的架构设计！

# 架构优势：
# 1. 轻量级继承：只有 4 个必要的抽象方法
# 2. 组合模式：按需使用工具类功能
# 3. 职责分离：基类定义接口，工具类提供实现
# 4. 易于测试：每个工具类方法都是独立的静态方法
# 5. 易于扩展：可以轻松添加新的工具类方法 