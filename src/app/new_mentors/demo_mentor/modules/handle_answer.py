"""Module to handle the user's answer."""
import os
from typing import Dict, Any

from langchain_core.prompts import ChatPromptTemplate
from langgraph.types import StreamWriter, Command

from src.app.new_mentors.common import InterruptableNode, MentorUtils
from src.domain.model.ai_chat_model import AiChatResultBO
from src.infrastructure.llm.llm_client import async_create_llm

RESPONSE_PROMPT = """
你是一个风趣幽默的AI助手。用户刚刚回答了你的问题。
你的问题是："{question}"
用户的回答是："{answer}"

请根据用户的回答，给出一句简短、风趣的评论。
"""


class HandleAnswerNode(InterruptableNode):
    """处理回答节点 - 使用新的轻量级架构
    
    - 继承轻量级的 InterruptableNode
    - 使用 MentorUtils 获取用户输入和日志记录（组合模式）
    - 代码更简洁，没有不必要的抽象方法
    """

    @property
    def _module_name(self) -> str:
        return "HandleAnswerNode"

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Dict[str, Any]:
        """处理用户回答并生成评论"""
        MentorUtils.log_module_start(self._module_name, state)

        question = state.get("question")
        # 使用工具类获取用户输入（组合模式）
        answer = MentorUtils.get_user_input(state)

        # 使用 LLM 生成评论
        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_PLUS_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.7,
            }
        )
        prompt = ChatPromptTemplate.from_template(RESPONSE_PROMPT)
        chain = prompt | llm

        comment = await chain.ainvoke({"question": question, "answer": answer})

        # 将评论发送给用户
        writer({"data": AiChatResultBO(text=comment.content)})

        MentorUtils.log_module_end(self._module_name, state, result="completed")
        return state

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """This method should not be called in an interrupt-based flow."""
        raise NotImplementedError(
            "This method should not be called in an interrupt-based flow."
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """This method should not be called in an interrupt-based flow."""
        raise NotImplementedError(
            "This method should not be called in an interrupt-based flow."
        ) 