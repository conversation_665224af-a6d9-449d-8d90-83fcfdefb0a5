"""Module to ask a question and interrupt the flow."""
import asyncio
from tkinter import END
from typing import Dict, Any

from langgraph.types import Command, StreamWriter
from langgraph.types import interrupt
from src.app.new_mentors.common import InterruptableNode, MentorUtils
from src.domain.model.ai_chat_model import AiChatResultBO


class AskQuestionNode(InterruptableNode):
    """提问节点 - 使用新的轻量级架构
    
    - 继承轻量级的 InterruptableNode
    - 使用 MentorUtils 进行日志记录（组合模式）
    - 代码更简洁，没有不必要的抽象方法
    """

    @property
    def _module_name(self) -> str:
        return "AskQuestionNode"

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter):
        """提问并中断工作流，resume 后继续执行"""
        MentorUtils.log_module_start(self._module_name, state)

        question = "你好，为了更好地演示，请问你最喜欢的编程语言是什么？"
        state["question"] = question
        writer({"data": AiChatResultBO(text=question)})
        MentorUtils.log_module_end(self._module_name, state, result="interrupted")

        # 进入中断，等待 resume
        answer = interrupt("请明确你要查询天气的城市！")

        # 将question 分解为两个字符循环
        for char in question:
            writer({"data": AiChatResultBO(text=char)})
            await asyncio.sleep(0.1)

        # resume 时才会走到这里
        print(f"[AskQuestionNode] 收到 resume: {answer}")
        state["city"] = answer
        return state

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """This method should not be called in an interrupt-based flow."""
        raise NotImplementedError(
            "This method should not be called in an interrupt-based flow."
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """This method should not be called in an interrupt-based flow."""
        raise NotImplementedError(
            "This method should not be called in an interrupt-based flow."
        ) 