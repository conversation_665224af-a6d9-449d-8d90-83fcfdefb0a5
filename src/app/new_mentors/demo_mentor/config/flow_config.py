"""Demo Mentor Flow Configuration.

演示简化的配置：在 interrupt 机制下，简单流程不需要复杂的协调器配置。
"""

DEMO_MENTOR_FLOW = {
    "sequence": [
        "AskQuestionNode",      # 提问并中断
        "HandleAnswerNode",     # 处理回答
    ],
    "module_configs": {
        # 在 interrupt 机制下，节点配置大大简化
        "AskQuestionNode": {"interrupts_flow": True},
        "HandleAnswerNode": {"completes_flow": True},
    },
    "flow_metadata": {
        "name": "Demo Mentor",
        "description": "演示 LangGraph interrupt 机制的最佳实践",
        "version": "2.0.0",
        "architecture": "interrupt-based",  # 标明新架构
    },
} 