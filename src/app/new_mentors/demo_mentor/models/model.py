"""Data models for the Demo Mentor.

演示在 interrupt 机制下，如何使用更简洁的状态模型。
"""
from typing import Dict, Any, Optional, List
from langgraph.graph.message import MessagesState

from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.app.zhiyuan.models.model import UserInfoDTO


class DemoMentorState(MessagesState):
    """简化的 Demo Mentor 状态模型
    
    相比 BaseMentorState，只保留必需的核心字段：
    - 继承 MessagesState：获得 LangGraph 的消息管理能力
    - 核心业务字段：用户信息、会话信息
    - Demo 特定字段：question（节点间数据传递）
    
    关于 BizRuntimeBO：
    - 在 demo 场景中基本不需要（没有复杂的业务流程）
    - 保留是为了与上层系统兼容（ChatStateBuilder 等组件期望此字段）
    - 在实际使用中会保持最小化，不依赖其复杂的内部结构
    
    移除的复杂字段：
    - current_step：LangGraph checkpoint 机制已经管理执行位置
    - questionnaire_context：简单流程不需要复杂状态管理
    - action, guideGotoDemo：旧的路由控制字段
    - pending_report_task, user_features, flow_metadata：复杂流程管理字段
    """
    
    # 核心业务字段 - 与上层系统集成必需
    userInfo: Optional[UserInfoDTO] = None
    messageId: Optional[str] = None
    conversationId: Optional[str] = None
    
    # 兼容性字段 - 上层系统期望，但在 demo 中最小化使用
    bizRuntimeBO: Optional[BizRuntimeBO] = None
    
    # 中断处理相关 - interrupt 机制需要
    interrupt_feedback: Optional[bool] = False
    
    # Demo 特定字段 - 业务逻辑需要
    question: Optional[str] = None


async def demo_mentor_command_update(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    简化的命令更新函数
    
    注意：
    1. BizRuntimeBO 主要为了兼容性而保留，实际业务逻辑不依赖它
    2. 在 interrupt 机制下，复杂的状态管理逻辑大部分不需要
    3. 这种模式特定的 command_update 函数仍然是样板代码，
       未来应该重构为通用的状态更新机制
    """
    return {
        # 核心字段
        "userInfo": state.get("userInfo"),
        "messageId": state.get("messageId"),
        "conversationId": state.get("conversationId"),
        "messages": state.get("messages"),  # MessagesState 字段
        
        # 兼容性字段（最小化使用）
        "bizRuntimeBO": state.get("bizRuntimeBO"),
        
        # 中断处理
        "interrupt_feedback": state.get("interrupt_feedback"),
        
        # Demo 特定
        "question": state.get("question"),
    } 