"""专为 interrupt 机制设计的轻量级 Mentor 基类

相比传统的 BaseMentor，这个基类：
1. 只提供必要的接口定义
2. 不包含复杂的协调器逻辑
3. 专门为 LangGraph interrupt 流程优化
"""
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, Type, Union, Optional

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, StreamWriter, Checkpointer

from langchain_core.runnables import RunnableConfig

from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient


class InterruptableMentor(ABC):
    """可中断的 Mentor 基类
    
    专门为使用 LangGraph interrupt 机制的 Mentor 设计。
    它强制实现了状态隔离，使得每个 Mentor 成为一个更加独立、内聚和可复用的组件。
    """

    @property
    @abstractmethod
    def _module_name(self) -> str:
        """模块名称，用于日志和调试"""
        pass
    
    @property
    @abstractmethod
    def _workflow_state_cls(self) -> Type[Dict[str, Any]]:
        """定义当前工作流的状态类。

        子类必须实现此属性，返回工作流自身的状态定义类。
        强烈推荐使用 Pydantic BaseModel 来确保类型安全和结构清晰。

        Returns:
            工作流的状态类。
        """
        pass

    @abstractmethod
    async def _create_workflow_graph(self, checkpointer: Optional[Checkpointer] = None) -> CompiledStateGraph:
        """创建并编译当前 Mentor 的工作流图。
        
        子类需要实现具体的工作流结构，包括：
        - 定义节点和边
        - 设置入口点
        - 使用 `StateGraph(self._workflow_state_cls).compile()` 进行编译
        
        Args:
            checkpointer: (可选) 用于状态持久化的 Checkpointer 对象。
        
        Returns:
            编译后的 LangGraph 状态图。
        """
        pass

    @abstractmethod
    def _map_parent_state_to_workflow_state(self, parent_state: Dict[str, Any]) -> Dict[str, Any]:
        """将父图状态映射到当前工作流的初始状态。
        
        这是实现状态隔离的关键。子类必须实现此方法，
        从父工作流的状态中提取所需数据，并构建成符合 `_workflow_state_cls` 定义的初始状态对象。
        
        Args:
            parent_state: 父工作流（Orchestrator）的当前状态。
            
        Returns:
            一个字典，作为当前工作流的初始状态。
        """
        pass

    @abstractmethod
    async def _map_workflow_state_to_parent_update(self, workflow_state: Dict[str, Any]) -> Union[Command, Dict[str, Any]]:
        """将当前工作流的最终状态映射为父图的更新。
        
        当 Mentor 的工作流执行完毕后，此方法被调用。
        子类必须实现此方法，根据当前工作流的最终结果，
        构建父工作流的更新。
        
        可以返回两种类型：
        1. Command 对象：用于命令驱动的父工作流，指定明确的下一个节点
        2. Dict 对象：用于条件边驱动的父工作流，更新父工作流的状态
        
        Args:
            workflow_state: 当前工作流的最终状态。
            
        Returns:
            Command 或 Dict：用于更新父工作流的命令或状态更新。
        """
        pass

    async def __call__(self, parent_state: Dict[str, Any], writer: StreamWriter) -> Union[Command, Dict[str, Any]]:
        """主入口执行逻辑
        
        此方法编排了 Mentor 的标准执行流程，实现了状态隔离和优雅的流式处理：
        1.  **状态映射**：调用 `_map_parent_state_to_workflow_state` 从父状态创建隔离的子工作流状态。
        2.  **工作流创建**：调用 `_create_workflow_graph` 获取带 Checkpointer 的子工作流。
        3.  **流式执行**：使用 `astream` + `stream_mode="messages"` 高效处理流式输出。
        4.  **状态获取**：通过 Checkpointer 的 `get_state` 方法零成本获取最终状态。
        5.  **结果映射**：调用 `_map_workflow_state_to_parent_update` 将子工作流的最终状态映射回父图的更新。
        
        Args:
            parent_state: 父工作流的状态。
            writer: 流式输出写入器。
            
        Returns:
            Command 或 Dict：用于更新父工作流的命令或状态更新。
        """
        # 1. 从父状态映射并创建当前工作流的初始状态
        workflow_state = self._map_parent_state_to_workflow_state(parent_state)
        
        # 2. 创建带 Checkpointer 的工作流
        checkpointer = await PGCheckpointClient.get_checkpoint()
        workflow = await self._create_workflow_graph(checkpointer=checkpointer)
        conversationId = parent_state.get("conversationId", str(uuid.uuid4()))
        config = RunnableConfig(configurable={"thread_id": conversationId})
        # 为 Checkpointer 定义唯一的线程ID
        # 优先从父状态获取，确保链路一致性，否则创建新的
        # 3. 使用 astream(stream_mode="messages") 高效处理流式输出
        # async for chunk in workflow.astream(
        #     workflow_state,
        #     config=config,
        #     stream_mode=["messages", "updates", "custom"],
        # ):
        #     mode = None
        #
        #     # 处理元组类型的chunk
        #     if isinstance(chunk, tuple):
        #         # 判断chunk的size
        #         if len(chunk) == 2:
        #             mode, data = chunk
        #             chunk = data
        #
        #         if len(chunk) == 3:
        #             start, mode, data = chunk
        #             chunk = data
        #
        #     if hasattr(chunk, "content") and chunk.content:
        #         writer(chunk.content)
        #
        #     if mode == "updates" and "__interrupt__" in chunk:
        #         interrupt_obj = chunk["__interrupt__"][0]
        #         interrupt_value = interrupt_obj.value
        #         writer({"data": interrupt_value})
        await workflow.ainvoke(workflow_state)
        # 4. 通过 Checkpointer 零成本获取最终状态（异步）
        final_state_snapshot = await workflow.aget_state(config)

        # 5. 获取最终状态并创建返回值
        final_state = final_state_snapshot.values if final_state_snapshot else workflow_state
        return await self._map_workflow_state_to_parent_update(final_state)