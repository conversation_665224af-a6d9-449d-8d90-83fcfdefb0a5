"""专为 interrupt 机制设计的轻量级节点基类"""

from abc import ABC, abstractmethod
from typing import Dict, Any

from langgraph.types import Command, StreamWriter


class InterruptableNode(ABC):
    """可中断节点的轻量级基类
    
    相比 BaseMentorModule，这个基类：
    - 只定义了节点必须的 __call__ 方法
    - 没有复杂的继承链和不必要的抽象方法
    """

    @property
    @abstractmethod
    def _module_name(self) -> str:
        """模块名称，用于日志和调试"""
        pass

    @abstractmethod
    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Any:
        """节点的主要执行逻辑
        
        Args:
            state: 当前状态
            writer: 流式输出写入器
            
        Returns:
            Any: 返回给 LangGraph 的状态更新
        """
        pass 