"""Mentor 工具类 - 组合模式实现基础功能

这个工具类提供所有 Mentor 需要的基础功能，通过组合而不是继承来使用。
包括：流式输出处理、错误处理、状态管理、日志记录等。
"""

import os
from typing import Dict, Any, TYPE_CHECKING, List

from langchain_core.runnables import RunnableConfig
from langgraph.types import StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient

if TYPE_CHECKING:
    from .interruptable_mentor import InterruptableMentor

logger = setup_logger(name=__name__, level="DEBUG")


class MentorUtils:
    """Mentor 工具类 - 提供基础功能的组合式实现
    
    通过静态方法提供各种基础功能，避免继承的复杂性。
    每个功能都是独立的，可以按需使用。
    """

    @staticmethod
    async def execute_interruptable_workflow(
        mentor: "InterruptableMentor", 
        state: Dict[str, Any], 
        writer: StreamWriter
    ):
        """执行可中断工作流的标准流程
        
        这是 InterruptableMentor 的核心执行逻辑，通过组合方式提供。
        
        Args:
            mentor: Mentor 实例
            state: 当前状态
            writer: 流式输出写入器
            
        Returns:
            Command: 执行结果命令
        """
        conversation_id = MentorUtils.get_conversation_id(state)
        
        # 开始日志
        MentorUtils.log_module_start(mentor._module_name, state)
        
        try:
            # 1. 准备初始状态
            init_state = mentor._prepare_init_state(state)
            
            # 2. 创建工作流图
            graph = await mentor._create_workflow_graph()
            
            # 3. 执行工作流并处理流式输出
            config = RunnableConfig(configurable={"thread_id": conversation_id})
            
            async for chunk in graph.astream(
                init_state,
                config=config,
                stream_mode=["values", "custom"],
                subgraphs=True,
            ):
                if not chunk:
                    continue
                # 处理流式输出
                await MentorUtils.process_stream_chunk(
                    chunk, writer, mentor._module_name
                )
            
            # 4. 返回到 orchestrator
            MentorUtils.log_module_end(mentor._module_name, state)
            return await mentor._create_return_command(init_state)
            
        except Exception as e:
            # 统一错误处理
            error_msg = MentorUtils.handle_error(e, mentor._module_name, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            
            # 即使出错也要返回到 orchestrator
            return await mentor._create_return_command(state)

    @staticmethod
    async def process_stream_chunk(chunk, writer: StreamWriter, module_name: str):
        """处理流式输出块
        
        Args:
            chunk: 输出块
            writer: 流式输出写入器
            module_name: 模块名称（用于日志）
        """
        try:
            mode = None

            # 处理元组类型的chunk
            if isinstance(chunk, tuple):
                start, mode, data = chunk
                chunk = data
                logger.debug(f"[{module_name}] 处理元组类型chunk: start={start}, mode={mode}")

            # 处理data字段
            if mode == "custom" and isinstance(chunk, dict) and "data" in chunk:
                data_result = chunk["data"]
                writer({"data": data_result})

            # 处理中断
            if mode == "values" and "__interrupt__" in chunk:
                interrupt_obj = chunk["__interrupt__"][0]
                interrupt_value = interrupt_obj.value
                writer({"data": interrupt_value})

        except Exception as e:
            logger.error(f"[{module_name}] 处理流式输出异常: {str(e)}")

    @staticmethod
    def get_default_init_state(state: Dict[str, Any]) -> Dict[str, Any]:
        """获取默认初始状态
        
        Args:
            state: 原始状态
            
        Returns:
            Dict[str, Any]: 默认初始状态
        """
        return {
            "userInfo": state.get("userInfo"),
            "messageId": state.get("messageId"),
            "conversationId": state.get("conversationId"),
            "messages": state.get("messages"),
            "action": state.get("action"),
            "guideGotoDemo": state.get("guideGotoDemo"),
            "interrupt_feedback": state.get("interrupt_feedback"),
            "bizRuntimeBO": state.get("bizRuntimeBO"),
            "questionnaire_context": state.get("questionnaire_context", {}),
            "current_step": state.get("current_step", 0),
            "pending_report_task": state.get("pending_report_task"),
        }

    @staticmethod
    def ensure_biz_runtime_structure(state: Dict[str, Any]):
        """确保 bizRuntimeBO 结构完整
        
        Args:
            state: 当前状态
        """
        from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO

        if "bizRuntimeBO" not in state or state["bizRuntimeBO"] is None:
            # 创建新的 BizRuntimeBO 实例
            state["bizRuntimeBO"] = BizRuntimeBO(params={}, context={})

        biz_runtime = state["bizRuntimeBO"]

        # 如果是字典，转换为 BizRuntimeBO 对象
        if isinstance(biz_runtime, dict):
            state["bizRuntimeBO"] = BizRuntimeBO(
                params=biz_runtime.get("params", {}),
                school=biz_runtime.get("school"),
                major=biz_runtime.get("major"),
                context=biz_runtime.get("context", {}),
            )
            biz_runtime = state["bizRuntimeBO"]

        # 确保 context 存在
        if biz_runtime.context is None:
            biz_runtime.context = {}

        # 确保 context 下的必要字段存在
        context = biz_runtime.context
        if "questionnaire_context" not in context:
            context["questionnaire_context"] = {}
        if "current_step" not in context:
            context["current_step"] = 0
        if "user_features" not in context:
            context["user_features"] = {}
        if "flow_metadata" not in context:
            context["flow_metadata"] = {}

    @staticmethod
    async def compile_workflow_with_checkpoint(workflow):
        """编译工作流并配置检查点
        
        Args:
            workflow: 工作流图
            
        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        checkpointer = await PGCheckpointClient.get_checkpoint()
        
        return workflow.compile(
            debug=os.getenv("DEBUG", "False").strip().lower() == "true",
            checkpointer=checkpointer,
        )

    @staticmethod
    def get_conversation_id(state: Dict[str, Any]) -> str:
        """获取会话ID
        
        Args:
            state: 当前状态
            
        Returns:
            str: 会话ID
        """
        return state.get("conversationId", "unknown")

    @staticmethod
    def get_messages(state: Dict[str, Any]) -> List:
        """获取消息列表
        
        Args:
            state: 当前状态
        
        Returns:
            List: 消息列表
        """
        return state.get("messages", [])

    @staticmethod
    def get_user_input(state: Dict[str, Any]) -> str:
        """获取用户最新输入
        
        优先从 interrupt_feedback 获取，这是 interrupt 流程的标准做法。
        
        Args:
            state: 当前状态
            
        Returns:
            str: 用户最新输入
        """
        interrupt_feedback = state.get("interrupt_feedback")
        if interrupt_feedback:
            return interrupt_feedback

        messages = MentorUtils.get_messages(state)
        if messages:
            return messages[-1].content
        return ""

    @staticmethod
    def log_module_start(module_name: str, state: Dict[str, Any]):
        """记录模块开始日志
        
        Args:
            module_name: 模块名称
            state: 当前状态
        """
        conversation_id = MentorUtils.get_conversation_id(state)
        logger.info(f"[{module_name}] 开始处理会话 {conversation_id}")
        logger.debug(f"[{module_name}] 当前状态: {state}")

    @staticmethod
    def log_module_end(module_name: str, state: Dict[str, Any], result: str = "success"):
        """记录模块结束日志
        
        Args:
            module_name: 模块名称
            state: 当前状态
            result: 执行结果
        """
        conversation_id = MentorUtils.get_conversation_id(state)
        logger.info(f"[{module_name}] 会话 {conversation_id} 处理完成，结果: {result}")

    @staticmethod
    def handle_error(error: Exception, module_name: str, state: Dict[str, Any]) -> str:
        """处理错误
        
        Args:
            error: 异常对象
            module_name: 模块名称
            state: 当前状态
            
        Returns:
            str: 用户友好的错误消息
        """
        conversation_id = MentorUtils.get_conversation_id(state)
        error_msg = f"抱歉，在处理您的请求时遇到了问题。请稍后重试。"
        
        logger.error(
            f"[{module_name}] 会话 {conversation_id} 执行异常: {str(error)}",
            exc_info=True
        )
        
        return error_msg 