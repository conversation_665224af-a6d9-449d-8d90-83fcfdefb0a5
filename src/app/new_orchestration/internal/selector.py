"""
Mentor selection logic based on rules and user input.
"""

from typing import Optional
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.mentors.career_first_v2 import CareerFirstMentor
from src.app.zhiyuan.mentors.city_first.city_first_mentor import CityFirstMentor
from src.app.zhiyuan.mentors.free_chat_qa.free_chat_qa_mentor import FreeChatQAMentor
from src.app.zhiyuan.mentors.major_first_v2 import MajorFirstMentor
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState

logger = setup_logger(name=__name__, level="DEBUG")


class MentorSelector:
    """基于规则的 Mentor 选择器"""

    @staticmethod
    async def select_mentor(state: ZhiYuanState) -> Optional[str]:
        """根据状态和规则选择合适的 Mentor
        
        Args:
            state: 当前状态
            
        Returns:
            Optional[str]: 选中的 Mentor 名称，如果没有匹配则返回 None
        """
        logger.info("[MentorSelector] 开始 Mentor 选择逻辑")
        
        # 检查 action 字段进行路由
        action = state.get("action")
        
        if action == "CareerQA":
            logger.info("[MentorSelector] 检测到职业优先模式意图，路由到 CareerFirstMentor")
            return CareerFirstMentor.__name__
            
        if action == "MajorQA":
            logger.info("[MentorSelector] 检测到专业优先模式意图，路由到 MajorFirstMentor")
            return MajorFirstMentor.__name__

        if action == "CityQA":
            logger.info("[MentorSelector] 检测到城市优先模式意图，路由到 CityFirstMentor")
            return CityFirstMentor.__name__
            
        # 如果没有明确的 action，检查是否需要自由对话模式
        if not action:
            logger.info("[MentorSelector] 未检测到特定 mentor，进入 FreeChat/默认分支判断")
            
            # 检查是否需要生成报告
            if await FreeChatQAMentor.need_report(state):
                logger.info("[MentorSelector] FreeChatQAMentor.need_report 返回 True，设置 action=FreeChat")
                state["action"] = "FreeChat"
                return FreeChatQAMentor.__name__
            else:
                logger.info("[MentorSelector] FreeChatQAMentor.need_report 返回 False")
                
        if action == "FreeChat":
            logger.info("[MentorSelector] 检测到自由对话模式意图，路由到 FreeChatQAMentor")
            return FreeChatQAMentor.__name__
            
        logger.info("[MentorSelector] 未命中任何 mentor 规则")
        return None
