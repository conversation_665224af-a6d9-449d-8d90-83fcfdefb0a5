"""
Data models for orchestration logic.
"""

from typing import List
from pydantic import BaseModel, Field

from src.app.zhiyuan.models.OrchestratorModel import SubQuestion


class Router(BaseModel):
    """用于确定下一个工作者的路由器
    
    Attributes:
        sub_questions: 子问题列表，每个子问题包含输入和对应的agent
    """

    sub_questions: List[SubQuestion] = Field(description="子问题列表")

    class Config:
        arbitrary_types_allowed = True
