"""
Coordinator node logic for LangGraph workflow.
"""

from langgraph.constants import END
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from .decomposer import TaskDecomposer
from .selector import MentorSelector

logger = setup_logger(name=__name__, level="DEBUG")


class Coordinator:
    """协调器，负责判断用户意图并路由到相应的服务"""

    def __init__(self):
        """初始化协调器"""
        self.task_decomposer = TaskDecomposer()

    async def __call__(self, state: Zhi<PERSON>uanState, writer: StreamWriter) -> Command:
        """协调器主要逻辑
        
        Args:
            state: 当前状态
            writer: 流式输出写入器
            
        Returns:
            Command: 下一步执行命令
        """
        logger.info(
            f"[Coordinator] messages: {state['messages'][-1] if state['messages'] else ''}"
        )

        conversation_id = state.get("conversationId", "unknown")
        logger.info(f"[Coordinator] 开始处理会话 {conversation_id}")
        logger.debug(f"[Coordinator] 当前 state: {state}")

        # 处理初始化分支
        if state.get("isInit", True):
            return await self._handle_init_branch(state, conversation_id)
            
        # 处理子问题序列
        if state.get("subQuestions"):
            return await self._handle_sub_questions(state, conversation_id)
            
        return None

    async def _handle_init_branch(self, state: ZhiYuanState, conversation_id: str) -> Command:
        """处理初始化分支
        
        Args:
            state: 当前状态
            conversation_id: 对话ID
            
        Returns:
            Command: 下一步执行命令
        """
        logger.info("[Coordinator] 进入 isInit 分支")
        
        # 尝试基于规则选择 Mentor
        mentor_name = await MentorSelector.select_mentor(state)
        
        if mentor_name:
            logger.info(f"[Coordinator] 命中 mentor_name: {mentor_name}，直接路由")
            return Command(
                goto=mentor_name,
                update={
                    "action": state.get("action"),
                    "isInit": False,
                    "gotoEnd": False,
                },
            )
            
        # 如果没有匹配的 Mentor，使用 LLM 进行任务分解
        logger.info("[Coordinator] 未命中 mentor_name，进入 LLM 路由决策分支")
        
        decomposition_result = await self.task_decomposer.decompose_task(state)
        
        if not decomposition_result or not decomposition_result.sub_questions:
            logger.info("[Coordinator] 没有子问题，使用默认路由")
            user_info = state.get("userInfo", "无用户信息")
            return Command(
                goto=END,
                update={
                    "isInit": False,
                    "gotoEnd": False,
                    "input": f"用户信息：\n{user_info}\n\n具体问题：\n{state['messages'][-1].content if state['messages'] else ''}",
                },
            )

        # 获取第一个子问题的 agent 作为当前 goto
        first_question = decomposition_result.sub_questions[0]
        goto = first_question.agent
        logger.info(f"[Coordinator] LLM 路由结果: {goto}")

        # 如果 goto 等于 FINISH，则结束对话
        if goto == "FINISH":
            logger.info(f"[Coordinator] 会话 {conversation_id} 完成，准备结束")
            return Command(goto=END)

        logger.info(f"[Coordinator] 初始化完成，goto: {goto}")
        user_info = state.get("userInfo", "无用户信息")
        
        return Command(
            goto=goto,
            update={
                "isInit": False,
                "gotoEnd": False,
                "input": f"用户信息：\n{user_info}\n\n具体问题：\n{first_question.input}",
                "subQuestions": decomposition_result.sub_questions,
                "index": 0,
            },
        )

    async def _handle_sub_questions(self, state: ZhiYuanState, conversation_id: str) -> Command:
        """处理子问题序列
        
        Args:
            state: 当前状态
            conversation_id: 对话ID
            
        Returns:
            Command: 下一步执行命令
        """
        current_index = state.get("index", 0) + 1
        sub_questions = state.get("subQuestions", [])
        
        # 如果已经处理完所有子问题，则结束
        if current_index >= len(sub_questions):
            logger.info(f"[Coordinator] 会话 {conversation_id} 所有Agent执行完成，准备结束")
            return Command(goto=END)

        # 获取当前子问题的 agent
        current_question = sub_questions[current_index]
        goto = current_question.agent

        logger.info(f"[Coordinator] 执行下一个Agent: {goto}, 会话ID: {conversation_id}")
        user_info = state.get("userInfo", "无用户信息")
        
        return Command(
            goto=goto,
            update={
                "gotoEnd": False,
                "input": f"用户信息：\n{user_info}\n\n具体问题：\n{current_question.input}",
                "index": current_index,
            },
        )
