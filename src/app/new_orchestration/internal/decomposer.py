"""
Task decomposition logic using LLM.
"""

import os
import traceback
from typing import Optional

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.infrastructure.llm.llm_client import create_llm
from .models import Router
from .prompts import SYSTEM_PROMPT

logger = setup_logger(name=__name__, level="DEBUG")


class TaskDecomposer:
    """任务分解器，使用 LLM 将用户问题分解为子任务"""

    def __init__(self):
        """初始化任务分解器"""
        self.llm = create_llm(
            **{
                "model_name": os.getenv("QWEN_32_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

    async def decompose_task(self, state: ZhiYuanState) -> Optional[Router]:
        """使用 LLM 分解任务
        
        Args:
            state: 当前状态
            
        Returns:
            Optional[Router]: 分解结果，包含子问题列表；如果分解失败则返回 None
        """
        logger.info("[TaskDecomposer] 开始进行 LLM 路由决策")
        
        try:
            # 准备解析器
            suggestion_parser = PydanticOutputParser(pydantic_object=Router)
            
            # 构建提示模板
            prompts = ChatPromptTemplate.from_messages(
                [SystemMessagePromptTemplate.from_template(SYSTEM_PROMPT)]
            )
            
            # 获取最近的消息（最多10条）
            messages = state.get("messages", [])
            if len(messages) > 10:
                last_messages = messages[-10:]
            else:
                last_messages = messages

            logger.info(f"[TaskDecomposer] 处理消息数量: {len(last_messages)}")
            logger.debug(f"[TaskDecomposer] 消息内容: {last_messages}")
            
            # 添加消息到提示
            prompts += last_messages
            
            # 构建处理链
            chain = prompts | self.llm | suggestion_parser

            # 获取用户信息
            user_info = state.get("userInfo", "无用户信息")
            
            # 调用 LLM 进行分解
            response = await chain.ainvoke(
                {
                    "format_instructions": suggestion_parser.get_format_instructions(),
                    "user_info": user_info,
                }
            )
            
            logger.info(f"[TaskDecomposer] LLM 路由决策结果: {response}")
            return response
            
        except Exception as e:
            logger.error(f"[TaskDecomposer] 任务分解失败: {str(e)}")
            logger.error(f"[TaskDecomposer] 错误详情: {traceback.format_exc()}", exc_info=True)
            return None
