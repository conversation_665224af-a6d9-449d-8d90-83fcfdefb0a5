"""
Orchestration module public API.

This module provides the public interface for the new orchestration system,
creating and compiling the complete StateGraph with all business nodes.
"""

import os

from langgraph.graph import StateGraph, START
from langgraph.graph.state import CompiledStateGraph
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.experts.AdmissionPolicyExpert import AdmissionPolicyExpert
from src.app.zhiyuan.experts.CollegeApplicationStrategyExpert import (
    CollegeApplicationStrategyExpert,
)
from src.app.zhiyuan.experts.JobProspectExpert import JobProspectExpert
from src.app.zhiyuan.experts.MajorExpert import MajorExpert
from src.app.zhiyuan.experts.SchoolExpert import SchoolExpert
from src.app.zhiyuan.experts.SchoolMajorCutoffExpert import SchoolMajorCutoffExpert
from src.app.zhiyuan.mentors.career_first_v2 import CareerFirstMentor
from src.app.zhiyuan.mentors.city_first.city_first_mentor import CityFirstMentor
from src.app.zhiyuan.mentors.free_chat_qa.free_chat_qa_mentor import FreeChatQAMentor
from src.app.zhiyuan.mentors.major_first_v2 import MajorFirstMentor
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient
from .internal.coordinator import Coordinator

logger = setup_logger(name=__name__, level="DEBUG")


async def get_nexus_orchestrator() -> CompiledStateGraph:
    """获取编排器实例
    
    Returns:
        CompiledStateGraph: 编译好的状态图实例
    """
    logger.info("[NexusOrchestrator] 开始创建 orchestrator 状态图")
    
    # 创建状态图
    workflow = StateGraph(ZhiYuanState)
    
    # 添加协调器节点
    coordinator = Coordinator()
    workflow.add_node(Coordinator.__name__, coordinator)
    
    # 添加专家节点
    workflow.add_node(AdmissionPolicyExpert.__name__, AdmissionPolicyExpert())
    workflow.add_node(SchoolExpert.__name__, SchoolExpert())
    workflow.add_node(MajorExpert.__name__, MajorExpert())
    workflow.add_node(SchoolMajorCutoffExpert.__name__, SchoolMajorCutoffExpert())
    workflow.add_node(JobProspectExpert.__name__, JobProspectExpert())
    workflow.add_node(
        CollegeApplicationStrategyExpert.__name__, CollegeApplicationStrategyExpert()
    )
    
    # 添加 Mentor 节点
    workflow.add_node(AgentTypeEnum.CareerFirstMentor.value, CareerFirstMentor())
    workflow.add_node(AgentTypeEnum.MajorFirstMentor.value, MajorFirstMentor())
    workflow.add_node(AgentTypeEnum.CityFirstMentor.value, CityFirstMentor())
    workflow.add_node(AgentTypeEnum.FreeChatQAMentor.value, FreeChatQAMentor())
    
    # 添加从 START 到协调器的边
    workflow.add_edge(START, Coordinator.__name__)
    
    # 获取检查点
    checkpointer = await PGCheckpointClient.get_checkpoint()
    
    # 编译状态图
    compiled_graph = workflow.compile(
        debug=os.getenv("DEBUG", "False").strip().lower() == "true",
        checkpointer=checkpointer,
    )
    
    logger.info("[NexusOrchestrator] orchestrator 状态图创建完成")
    return compiled_graph
