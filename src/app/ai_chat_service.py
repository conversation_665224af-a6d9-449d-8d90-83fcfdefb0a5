import asyncio
from typing import Any
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command
from lucas_common_components.logging import setup_logger
from sse_starlette.sse import AsyncContentStream

from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
from src.app.zhiyuan.orchestrator import get_orchestrator
from src.domain.model.ai_chat_model import (
    AiChatResultBO,
    CustomBO,
    MessageStatusBO,
    ReportContentResultBO,
)
from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.exe.command.AIMessageCommandExe import AIMessageCommandExe
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient
from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository
from src.infrastructure.db.crud.ai.ReportTaskRepository import ReportTaskRepository
from src.infrastructure.rpc.account_client import AccountClient
from src.infrastructure.session import ConnectionManager, SessionManager, Connection, ConnectionStatus
from src.utils.snow_flake import Snowflake

logger = setup_logger(name=__name__, level="DEBUG")


# ==================== 会话处理器函数 ====================

async def chat_session_processor(request: Any, user_id: str, conversation_id: str):
    """会话处理器函数 - 用于 SessionManager

    Args:
        request: 请求对象
        user_id: 用户ID
        conversation_id: 会话ID

    Yields:
        处理结果
    """
    # 获取 action
    action = await get_and_set_action(request)

    # 处理会话逻辑
    async for result in _normal_chat_handler(request, user_id, action):
        yield result


# 全局管理器实例
connection_manager = ConnectionManager()
session_manager = SessionManager(connection_manager)


def convert_to_langchain_message(message: MessageVO):
    """将 MessageVO 转换为 LangChain 消息类型"""
    content = message.text or ""  # 确保 content 不为 None
    if message.role == "user":
        return HumanMessage(content=content)
    elif message.role == "assistant":
        return AIMessage(content=content)
    elif message.role == "system":
        return SystemMessage(content=content)
    else:
        # 默认作为用户消息处理
        return HumanMessage(content=content)


sf = Snowflake(worker_id=0, datacenter_id=0)


# ==================== 新的公共接口 ====================

async def chat_handler(request: ChatInputVO, uniqUserId: str) -> AsyncContentStream:
    """新的聊天处理器 - 使用 Acceptor-Processor 模式"""
    # 检查conversation_id是否为空
    if not request.conversation_id:
        raise ValueError("conversation_id cannot be None")

    # 创建连接
    connection = await connection_manager.add_connection(request.conversation_id, uniqUserId)

    try:
        # 提交会话任务
        task_id = await session_manager.submit_session_task(
            request, uniqUserId, connection.connection_id, chat_session_processor
        )

        # 从连接的响应队列中读取结果
        async for result in _read_connection_responses(connection):
            yield result

    except Exception as e:
        logger.error(f"Error in chat_handler: {e}")
        error_result = AiChatResultBO(text=f"处理出错: {str(e)}")
        yield error_result.model_dump_json(exclude_none=True)
    finally:
        # 清理连接
        await connection_manager.remove_connection(connection.connection_id)


async def _read_connection_responses(connection: Connection) -> AsyncContentStream:
    """从连接读取响应"""
    try:
        while connection.status == ConnectionStatus.ACTIVE:
            try:
                # 等待响应，设置超时
                result = await asyncio.wait_for(
                    connection.response_queue.get(),
                    timeout=30.0
                )

                if result is None:  # 结束信号
                    break

                yield result

            except asyncio.TimeoutError:
                # 发送心跳或检查连接状态
                continue
            except Exception as e:
                logger.error(f"Error reading from connection {connection.connection_id}: {e}")
                break

    except Exception as e:
        logger.error(f"Error in _read_connection_responses: {e}")
    finally:
        # 确保连接被标记为关闭
        connection.status = ConnectionStatus.CLOSED


# ==================== 兼容性接口 ====================

async def chat_handler_legacy(request: ChatInputVO, uniqUserId: str) -> AsyncContentStream:
    """旧版聊天处理器 - 保持向后兼容"""
    # 检查conversation_id是否为空
    if not request.conversation_id:
        raise ValueError("conversation_id cannot be None")

    action = await get_and_set_action(request)
    async for result in _normal_chat_handler(request, uniqUserId, action):
        yield result


async def chat_process_action_handler(
    request: ChatInputVO, uniqUserId: str
) -> AsyncContentStream:
    # 检查conversation_id是否为空
    if not request.conversation_id:
        raise ValueError("conversation_id cannot be None")
    async for result in _status_chat_handler(request, uniqUserId):
        yield result


async def get_and_set_action(request: ChatInputVO):
    # action 为空，则设置为 report
    if not request.conversation_id:
        raise ValueError("conversation_id cannot be None")

    ai_conversation = await AIConversationRepository.get_by_id(
        int(request.conversation_id)
    )
    if not ai_conversation or not ai_conversation.context:
        return "report"  # 默认action
    return ai_conversation.context.get("conversation_action", "report")



async def query_task_status(
    task_id:str, uniqUserId: str, conversation_action: str
) -> CustomBO:
    report_task = await ReportTaskRepository.get_by_task_id(task_id)
    if not report_task:
        return CustomBO(
            message_id="",
            message_type="report",
            message_data=ReportContentResultBO(
                success=False, task_id=task_id, task_name=""
            ),
        )
    message_id = (
        report_task.extra_data.get("message_id", "")
        if report_task.extra_data
        else ""
    )
    custom=CustomBO(
        message_id=message_id,
        message_type="report",
        message_status=MessageStatusBO(
            user_input={
                "send_button_disable": not report_task.is_completed
            },
            complete=report_task.is_completed,
            progress=report_task.progress,
            next_interval=None if report_task.is_completed else 5,
        ),
        message_data=ReportContentResultBO(
            task_id=task_id,
            task_name=report_task.task_name,
            content=report_task.report_content,
            success=True,
        ),
    )
    logger.info(
        "custom:{}", custom.model_dump_json(exclude_none=True)
    )
    return custom



async def _status_chat_handler(
    request: ChatInputVO, uniqUserId: str
) -> AsyncContentStream:
    first_message = request.messages[0] if request.messages else None
    action = ""
    task_id = ""

    if first_message and hasattr(first_message, 'params') and first_message.params:
        if isinstance(first_message.params, dict):
            action = first_message.params.get("action", "")
            task_id = first_message.params.get("task_id", "")
    if action == "query_progress":
        if task_id:
            report_task = await ReportTaskRepository.get_by_task_id(task_id)
            if not report_task:
                chat_result_bo = AiChatResultBO(
                    text="report_task_not_found",
                    custom=CustomBO(
                        message_id="",
                        message_type="report",
                        message_data=ReportContentResultBO(
                            success=False, task_id=task_id, task_name=""
                        ),
                    ),
                )
                yield chat_result_bo
                return
            message_id = (
                report_task.extra_data.get("message_id", "")
                if report_task.extra_data
                else ""
            )
            chat_result_bo = AiChatResultBO(
                custom=CustomBO(
                    message_id=message_id,
                    message_type="report",
                    message_status=MessageStatusBO(
                        user_input={
                            "send_button_disable": not report_task.is_completed
                        },
                        complete=report_task.is_completed,
                        progress=report_task.progress,
                        next_interval=None if report_task.is_completed else 5,
                    ),
                    message_data=ReportContentResultBO(
                        task_id=task_id,
                        task_name=report_task.task_name,
                        content=report_task.report_content,
                        success=True,
                    ),
                ),
            )
            logger.info(
                "chat_result_bo:{}", chat_result_bo.model_dump_json(exclude_none=True)
            )
            yield chat_result_bo.model_dump_json(exclude_none=True)
            # # 如果任务已经导出完成，继续进入流程处理
            # if report_task.is_completed:
            #     action = await get_and_set_action(request)
            #     async for normal_result in _normal_chat_handler(request, uniqUserId,action):
            #         yield normal_result


async def _normal_chat_handler(
    request: ChatInputVO, uniqUserId: str, conversation_action: str
) -> AsyncContentStream:
    first_message = request.messages[0] if request.messages else None
    interrupt = ""

    if first_message and hasattr(first_message, 'params') and first_message.params:
        if isinstance(first_message.params, dict):
            interrupt = first_message.params.get("interrupt_feedback", "")
    conversation_id = request.conversation_id
    if not conversation_id:
        raise ValueError("conversation_id cannot be None")

    # 确保会话的更新时间被更新
    conversation_to_update = await AIConversationRepository.get_by_id(int(conversation_id))
    if conversation_to_update:
        logger.info(f"尝试更新会话 {conversation_id} 的 updated_at 字段...")
        await conversation_to_update.save()
        logger.info(f"会话 {conversation_id} updated_at 字段更新完成。")
    
    if interrupt:
        # 预生成 ai_message_id
        orchestrator = await get_orchestrator()
        config = RunnableConfig(configurable={"thread_id": conversation_id})
        async for chunk in orchestrator.astream(
            Command(resume="This is my feedback!"),
            config=config,
            stream_mode=["custom", "values"],
            subgraphs=True,
        ):
            if not chunk:
                continue
            logger.info(f"chunk====={chunk}")

    conversation_id = request.conversation_id
    try:
        # 存储用户输入到数据库
        await AIMessageCommandExe.save_ai_message_by_user(
            conversation_id=conversation_id, request=request
        )

        # 预生成 ai_message_id
        prepare_ai_message_id = sf.generate()
        init_state = await build_init_state(
            conversation_id, request, uniqUserId, conversation_action
        )
        ai_response_text = ""
        last_message_type = None
        init_message_type = None
        message_id = prepare_ai_message_id

        orchestrator = await get_orchestrator()
        config = RunnableConfig(configurable={"thread_id": conversation_id})
        async for chunk in orchestrator.astream(
            init_state,
            config=config,
            stream_mode=["custom", "values"],
            subgraphs=True,
        ):
            if not chunk:
                continue

            mode = None
            if isinstance(chunk, tuple):
                # 判断chunk的size
                if len(chunk) == 2:
                    mode, data = chunk
                    chunk = data

                if len(chunk) == 3:
                    _, mode, data = chunk
                    chunk = data

            if mode == "custom" and isinstance(chunk, dict) and "data" in chunk:
                chat_result_bo = chunk["data"]
                if not isinstance(chat_result_bo, AiChatResultBO):
                    continue
                # 如果 custom 为 None，自动创建一个 CustomVO
                if chat_result_bo.custom is None:
                    chat_result_bo.custom = CustomBO(message_type="text", message_id="")

                curr_message_type = get_message_type(chat_result_bo)

                # 初始化
                if init_message_type is None:
                    init_message_type = curr_message_type
                    last_message_type = curr_message_type
                    message_id = get_message_id(chat_result_bo)
                    chat_result_bo.custom.message_id = message_id
                    if curr_message_type == "text":
                        ai_response_text += chat_result_bo.text or ""
                        chat_result_bo.custom.message_type = "text"
                    else:
                        await AIMessageCommandExe.save_ai_message_by_ai_custom(
                            conversation_id=conversation_id,
                            prepare_ai_message_id=message_id,
                            custom=chat_result_bo.custom,
                        )
                    logger.info(
                        f"chat_result_bo:{chat_result_bo.model_dump_json(exclude_none=True)}"
                    )
                    yield chat_result_bo.model_dump_json(exclude_none=True)
                    continue

                # messageType未变更
                if curr_message_type == last_message_type:
                    chat_result_bo.custom.message_id = message_id
                    if curr_message_type == "text":
                        ai_response_text += chat_result_bo.text or ""
                    else:
                        await AIMessageCommandExe.save_ai_message_by_ai_custom(
                            conversation_id=conversation_id,
                            prepare_ai_message_id=message_id,
                            custom=chat_result_bo.custom,
                        )
                    logger.info(
                        f"chat_result_bo:{chat_result_bo.model_dump_json(exclude_none=True)}"
                    )
                    yield chat_result_bo.model_dump_json(exclude_none=True)
                    continue

                # messageType变更
                # 1. text -> customX
                if last_message_type == "text" and curr_message_type != "text":
                    # 保存之前累计的text
                    await AIMessageCommandExe.save_text_message(
                        ai_response_text, conversation_id, message_id
                    )
                    ai_response_text = ""
                    # 新的messageId
                    message_id = get_message_id(chat_result_bo)
                    chat_result_bo.custom.message_id = message_id
                    await AIMessageCommandExe.save_ai_message_by_ai_custom(
                        conversation_id=conversation_id,
                        prepare_ai_message_id=message_id,
                        custom=chat_result_bo.custom,
                    )
                    last_message_type = curr_message_type
                    logger.info(
                        f"chat_result_bo:{chat_result_bo.model_dump_json(exclude_none=True)}"
                    )
                    yield chat_result_bo.model_dump_json(exclude_none=True)
                    continue

                # 2. customX -> customY
                if last_message_type != "text" and curr_message_type != "text":
                    message_id = get_message_id(chat_result_bo)
                    chat_result_bo.custom.message_id = message_id
                    await AIMessageCommandExe.save_ai_message_by_ai_custom(
                        conversation_id=conversation_id,
                        prepare_ai_message_id=message_id,
                        custom=chat_result_bo.custom,
                    )
                    last_message_type = curr_message_type
                    logger.info(
                        f"chat_result_bo:{chat_result_bo.model_dump_json(exclude_none=True)}"
                    )
                    yield chat_result_bo.model_dump_json(exclude_none=True)
                    continue

                # 3. customX -> text
                if last_message_type != "text" and curr_message_type == "text":
                    message_id = get_message_id(chat_result_bo)
                    chat_result_bo.custom.message_id = message_id
                    ai_response_text = chat_result_bo.text or ""
                    last_message_type = curr_message_type
                    logger.info(
                        f"chat_result_bo:{chat_result_bo.model_dump_json(exclude_none=True)}"
                    )
                    yield chat_result_bo.model_dump_json(exclude_none=True)
                    continue

            # 循环结束后，若有未保存的text，保存
            await AIMessageCommandExe.save_text_message(
                text=ai_response_text, conv_id=conversation_id, msg_id=message_id
            )
    except Exception as e:
        logger.error(f"Initialization error in chat_handler: {str(e)}", exc_info=True)
    finally:
        # 添加完成消息到队列
        result = AiChatResultBO(text="")
        if result.html is not None:
            yield result


def get_message_type(ai_vo: AiChatResultBO):
    if not ai_vo.custom:
        return "text"
    if ai_vo.custom.message_type:
        return ai_vo.custom.message_type
    return "text"


def get_message_id(data_vo: AiChatResultBO):
    if data_vo.custom and data_vo.custom.message_id:
        return data_vo.custom.message_id
    return sf.generate()


async def build_init_state(conversation_id, request, uniqUserId, action: str):
    checkpointer = await PGCheckpointClient.get_checkpoint()
    config = RunnableConfig(configurable={"thread_id": conversation_id})
    checkpointer_data = await checkpointer.aget_tuple(config)
    checkpointer_state_dict = {"bizRuntimeBO": None}
    if checkpointer_data and hasattr(checkpointer_data, "checkpoint"):
        checkpointer_state_dict = checkpointer_data.checkpoint.get(
            "channel_values", checkpointer_state_dict
        )

    # 获取历史消息
    messages = await AIMessageRepository.get_by_conversation_id_limit10(conversation_id)

    # 转换消息为 LangChain 格式，并确保消息按时间正序排列
    langchain_messages = []
    for msg in sorted(messages, key=lambda x: x.created_at):
        text = ""
        if isinstance(msg.message_data, dict):
            text = msg.message_data.get("text", "")
        if text:
            langchain_messages.append(
                convert_to_langchain_message(MessageVO(text=text, role=msg.role))
            )

    client = AccountClient()
    user_info = await client.get_profile(uniq_user_id=uniqUserId)
    if user_info:
        user_info.id = int(uniqUserId)
    biz_runtime_bo_dict = checkpointer_state_dict.get("bizRuntimeBO")
    if isinstance(biz_runtime_bo_dict, dict):
        biz_runtime_bo = (
            BizRuntimeBO(**biz_runtime_bo_dict)
            if biz_runtime_bo_dict
            else BizRuntimeBO()
        )
    else:
        biz_runtime_bo = biz_runtime_bo_dict

    biz_runtime_bo = biz_runtime_bo if biz_runtime_bo else BizRuntimeBO()
    biz_runtime_bo.params = biz_runtime_bo.params if biz_runtime_bo.params else {}
    # 传递问卷回答
    first_message = request.messages[0] if request.messages else None
    questionnaire_question = {}
    answer = {}

    if first_message and hasattr(first_message, 'params') and first_message.params:
        if isinstance(first_message.params, dict):
            questionnaire_question = first_message.params.get("question", {})
            answer = first_message.params.get("answer", {})

    action = action if action else str(checkpointer_state_dict.get("action", ""))
    if questionnaire_question:
        biz_runtime_bo.params.update({"questionnaire_question": questionnaire_question})
    if answer:
        biz_runtime_bo.params.update({"answer": answer})
    else:
        biz_runtime_bo.params.update({"answer": None})

    init_state = {
        "userInfo": user_info,
        "messageId": str(sf.generate()),
        "conversationId": conversation_id,
        "messages": langchain_messages,  # 使用转换后的消息
        "isInit": True,
        "action": action,
        "nextAgents": [],
        "bizRuntimeBO": biz_runtime_bo,
        "interrupt_feedback": request.interrupt_feedback or False,
        "handleSummary": False,
    }
    return init_state
