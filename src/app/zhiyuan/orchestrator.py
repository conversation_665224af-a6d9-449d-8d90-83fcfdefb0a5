"""
JDSecondCoordinator - 一个基于LangGraph的多智能体系统。
该系统实现了一个监督者（Coordinator）管理多个专业代理之间的协作。
"""

import os
import traceback

# 标准库导入
from typing import List

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.constants import END

# 第三方库导入
from langgraph.graph import StateGraph, START
from langgraph.graph.state import CompiledStateGraph

# 第三方库导入
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger
from pydantic import Field, BaseModel

from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.experts.AdmissionPolicyExpert import AdmissionPolicyExpert
from src.app.zhiyuan.experts.CollegeApplicationStrategyExpert import (
    CollegeApplicationStrategyExpert,
)
from src.app.zhiyuan.experts.JobProspectExpert import JobProspectExpert
from src.app.zhiyuan.experts.MajorExpert import MajorExpert
from src.app.zhiyuan.experts.SchoolExpert import SchoolExpert
from src.app.zhiyuan.experts.SchoolMajorCutoffExpert import SchoolMajorCutoffExpert
from src.app.zhiyuan.mentors.career_first_v2 import CareerFirstMentor
from src.app.zhiyuan.mentors.city_first.city_first_mentor import CityFirstMentor
from src.app.zhiyuan.mentors.free_chat_qa.free_chat_qa_mentor import FreeChatQAMentor
from src.app.zhiyuan.mentors.major_first_v2 import MajorFirstMentor
from src.app.zhiyuan.models.OrchestratorModel import SubQuestion, ZhiYuanState
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient
from src.infrastructure.llm.llm_client import create_llm

# 标准库导入

SYSTEM_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，擅长将用户的问题拆解成多个自洽且最小化的子问题。

# 用户信息
{user_info}

# 场景
负责判断用户输入类型并管理代理调度，根据用户的请求类型和内容，确定处理策略。

# 代理职责
- AdmissionPolicyExpert: 招生政策Agent
- SchoolExpert: 学校Agent
- MajorExpert: 专业Agent
- SchoolMajorCutoffExpert: 分数线查询Agent
- JobProspectExpert: 就业形势Agent
- CollegeApplicationStrategyExpert: 志愿填报策略Agent，负责回答关于志愿填报策略、技巧、方法等问题
- FINISH: 结束对话

# 路由规则
1. 根据用户问题选择合适的专家序列
2. 如果问题涉及多个方面，可以添加多个专家
3. 如果问题超出专业范围，使用FINISH
4. 当用户询问关于志愿填报策略、技巧、方法等问题时，应该路由到CollegeApplicationStrategyExpert

# 子问题拆分原则
1. 每个子问题应该是独立且最小化的
2. 子问题之间不应该有重复或重叠
3. 每个子问题应该针对特定的专家领域
4. 保证不丢失原意，也不自行补充信息
5. 子问题的input必须包含完整的用户信息，格式如下：
   ```
   用户信息：
   {user_info}
   
   具体问题：
   [你的具体问题]
   ```
6. 在拆分子问题时，需要考虑用户信息中的背景信息，使回答更加个性化和精准

# 专家输入示例
## AdmissionPolicyExpert输入示例
当用户询问招生政策相关问题时，应该将问题拆解为具体的政策咨询点，例如：
"请问：XX省2025年高考采用的是平行志愿还是顺序志愿模式？平行志愿的投档规则是怎样的？另外，如果体检项目存在不合格项，会影响录取吗？具体有哪些专业受体检结果限制？"

输入应该包含：
1. 完整的用户信息
2. 具体的省份信息
3. 具体的年份信息
4. 具体的政策咨询点
5. 相关的限制条件或特殊情况

## CollegeApplicationStrategyExpert输入示例
当用户询问志愿填报策略相关问题时，应该将问题拆解为具体的策略咨询点，例如：
"请问：如何根据分数选择合适的学校和专业？平行志愿填报有什么技巧？如何避免滑档？"

输入应该包含：
1. 完整的用户信息
2. 具体的策略咨询点
3. 相关的限制条件或特殊情况
4. 用户的具体需求或关注点

# 输出定义:
{format_instructions}
"""

logger = setup_logger(name=__name__, level="DEBUG")


class Router(BaseModel):
    """用于确定下一个工作者的路由器。
    Attributes:
        sub_questions: 子问题列表，每个子问题包含输入和对应的agent
    """

    sub_questions: List[SubQuestion] = Field(description="子问题列表")

    class Config:
        arbitrary_types_allowed = True


class Orchestrator:
    def __init__(self):
        self.llm = create_llm(
            **{
                "model_name": os.getenv("QWEN_32_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter):
        """
        协调器函数，负责判断用户意图并路由到相应的服务。
        """
        logger.info(
            f"[{__name__}.orchestrator_func] messages: {state['messages'][-1] if state['messages'] else ''}"
        )

        # 原有的协调器逻辑
        conversation_id = state["conversationId"]
        logger.info(f"[zhiyuanCoordinator] 开始处理会话 {conversation_id}")
        logger.debug(f"[zhiyuanCoordinator] 当前 state: {state}")

        #  if state['firstInvokeAgent'] 不为true
        if state["isInit"]:
            logger.info("[zhiyuanCoordinator] 进入 isInit 分支")
            # 判断是否为志愿填报引导对话
            mentor_name = ""
            if "action" in state and state["action"] == "CareerQA":
                logger.info(
                    f"[{__name__}.orchestrator_func] 检测到志愿填报引导对话意图，路由到CareerQA"
                )
                mentor_name = CareerFirstMentor.__name__
                
            if "action" in state and state["action"] == "MajorQA":
                logger.info(
                    f"[{__name__}.orchestrator_func] 检测到专业优先模式意图，路由到MajorFirstMentor"
                )
                mentor_name = MajorFirstMentor.__name__

            if "action" in state and state["action"] == "CityQA":
                logger.info(
                    f"[{__name__}.orchestrator_func] 检测到心仪城市模式意图，路由到CityFirstMentor"
                )
                mentor_name = CityFirstMentor.__name__

            logger.debug(f"[zhiyuanCoordinator] mentor_name after action check: {mentor_name}")
            if not mentor_name:
                logger.info("[zhiyuanCoordinator] 未检测到特定 mentor，进入 FreeChat/默认分支判断")
                # 如果是要生成报告
                if await FreeChatQAMentor.need_report(state):
                    logger.info("[zhiyuanCoordinator] FreeChatQAMentor.need_report 返回 True，设置 action=FreeChat")
                    state["action"] = "FreeChat"
                else:
                    logger.info("[zhiyuanCoordinator] FreeChatQAMentor.need_report 返回 False，设置 action='' ")
                    state["action"] = ""

                if "action" in state and state["action"] == "FreeChat":
                    logger.info(
                        f"[{__name__}.orchestrator_func] 检测到自由对话模式意图，路由到FreeChatQACoordinator"
                    )
                    mentor_name = FreeChatQAMentor.__name__

            logger.debug(f"[zhiyuanCoordinator] mentor_name after FreeChat check: {mentor_name}")
            if mentor_name:
                logger.info(f"[zhiyuanCoordinator] 命中 mentor_name: {mentor_name}，直接路由")
                return Command(
                    goto=mentor_name,
                    update={
                        "action": state["action"],
                        "isInit": False,
                        "gotoEnd": False,
                    },
                )
            logger.info("[zhiyuanCoordinator] 未命中 mentor_name，进入 LLM 路由决策分支")
            suggestion_parser = PydanticOutputParser(pydantic_object=Router)
            prompts = ChatPromptTemplate.from_messages(
                [SystemMessagePromptTemplate.from_template(SYSTEM_PROMPT)]
            )
            # state["messages"] 获取后十条数据，如果不过10条，获取全部数据
            if len(state["messages"]) > 10:
                last_10_messages = state["messages"][-10:]
            else:
                last_10_messages = state["messages"]

            logger.info(f"[zhiyuanCoordinator] 处理消息数量: {len(last_10_messages)}")
            logger.info(f"[zhiyuanCoordinator] 消息内容: {last_10_messages}")
            prompts += last_10_messages
            logger.debug("[zhiyuanCoordinator] prompts 构建完成，准备拼接 LLM chain")
            chain = prompts | self.llm | suggestion_parser

            try:
                logger.info("[zhiyuanCoordinator] 开始调用LLM进行路由决策")
                user_info = state.get("userInfo", "无用户信息")
                response = await chain.ainvoke(
                    {
                        "format_instructions": suggestion_parser.get_format_instructions(),
                        "user_info": user_info,
                    }
                )
                logger.info(f"[zhiyuanCoordinator] LLM路由决策结果: {response}")

                if not response.sub_questions:
                    logger.info("[zhiyuanCoordinator] 没有子问题，使用默认路由")
                    return Command(
                        goto=END,
                        update={
                            "isInit": False,
                            "gotoEnd": False,
                            "input": f"用户信息：\n{user_info}\n\n具体问题：\n{state['messages'][-1].content if state['messages'] else ''}",
                        },
                    )

                # 获取第一个子问题的agent作为当前goto
                agents = [q.agent for q in response.sub_questions]
                agents = list(agents)
                goto = agents[0]
                logger.info(f"[zhiyuanCoordinator] LLM 路由结果: {goto}")

                # goto如果等于FINISH，则return 使用slotAgent
                if goto == "FINISH":
                    logger.info(
                        f"[zhiyuanCoordinator] 会话 {conversation_id} 完成，准备结束"
                    )
                    goto = __name__

                logger.info(
                    f"[zhiyuanCoordinator] 初始化完成，goto: {goto}, state: {{'conversationId': {conversation_id}}}"
                )
                return Command(
                    goto=goto,
                    update={
                        "isInit": False,
                        "gotoEnd": False,
                        "input": f"用户信息：\n{user_info}\n\n具体问题：\n{response.sub_questions[0].input}",
                        "subQuestions": response.sub_questions,
                        "index": 0,
                    },
                )
            except Exception:
                logger.error(
                    f"[zhiyuanCoordinator] 错误详情: {traceback.format_exc()}",
                    exc_info=True,
                )
                # 发生错误时，默认路由到DefaultExpert
                logger.info("[zhiyuanCoordinator] 发生错误，默认路由到DefaultExpert")
                return Command(
                    goto=END,
                    update={
                        "isInit": False,
                        "gotoEnd": False,
                        "input": state["messages"][-1].content
                        if state["messages"]
                        else "",
                    },
                )

        if state.get("subQuestions"):
            current_index = state["index"] + 1
            # 如果已经处理完所有子问题，则return END
            if current_index >= len(state["subQuestions"]):
                logger.info(
                    f"[zhiyuanCoordinator] 会话 {conversation_id} 所有Agent执行完成，准备结束"
                )
                return Command(goto=END)

            # 获取当前子问题的agent
            current_question = state["subQuestions"][current_index]
            goto = current_question.agent

            logger.info(
                f"[zhiyuanCoordinator] 执行下一个Agent: {goto}, 会话ID: {conversation_id}"
            )
            return Command(
                goto=goto,
                update={
                    "gotoEnd": False,
                    "input": f"用户信息：\n{state.get('userInfo', '无用户信息')}\n\n具体问题：\n{current_question.input}",
                    "index": current_index,
                },
            )
        return None


async def __create_orchestrator_agent() -> CompiledStateGraph:
    workflow = StateGraph(ZhiYuanState)
    # 意图识别，走哪个节点
    workflow.add_node(Orchestrator.__name__, Orchestrator())
    workflow.add_node(AdmissionPolicyExpert.__name__, AdmissionPolicyExpert())
    workflow.add_node(SchoolExpert.__name__, SchoolExpert())
    workflow.add_node(MajorExpert.__name__, MajorExpert())
    workflow.add_node(SchoolMajorCutoffExpert.__name__, SchoolMajorCutoffExpert())
    workflow.add_node(JobProspectExpert.__name__, JobProspectExpert())
    workflow.add_node(
        CollegeApplicationStrategyExpert.__name__, CollegeApplicationStrategyExpert()
    )

    workflow.add_node(AgentTypeEnum.CareerFirstMentor.value, CareerFirstMentor())
    workflow.add_node(AgentTypeEnum.MajorFirstMentor.value, MajorFirstMentor())
    workflow.add_node(AgentTypeEnum.CityFirstMentor.value, CityFirstMentor())
    workflow.add_node(AgentTypeEnum.FreeChatQAMentor.value, FreeChatQAMentor())

    # 添加从START到协调器的边
    workflow.add_edge(START, Orchestrator.__name__)

    checkpointer = await PGCheckpointClient.get_checkpoint()

    return workflow.compile(
        debug=os.getenv("DEBUG", "False").strip().lower() == "true",
        checkpointer=checkpointer,
    )


# 变更为异步获取orchestrator的函数
_orchestrator_instance = None


async def get_orchestrator() -> CompiledStateGraph:
    return await __create_orchestrator_agent()
