import asyncio
import uuid
from typing import TypedDict

from langgraph.graph import StateGraph
from langgraph.constants import START
from langgraph.types import interrupt, Command
from langgraph.checkpoint.memory import MemorySaver


class SubState(TypedDict):
    """The graph state."""

    sub_state_counter: int


counter_node_in_subgraph = 0


async def node_in_subgraph(state: SubState):
    """A node in the sub-graph."""
    global counter_node_in_subgraph
    counter_node_in_subgraph += 1  # This code will **NOT** run again!
    print(f"Entered `node_in_subgraph` a total of {counter_node_in_subgraph} times")
    return Command(goto="human_node")


counter_human_node = 0


async def human_node(state: SubState):
    global counter_human_node
    counter_human_node += 1  # This code will run again!
    print(f"Entered human_node in sub-graph a total of {counter_human_node} times")
    answer = interrupt("what is your name?")
    print(f"Got an answer of {answer}")


checkpointer1 = MemorySaver()

subgraph_builder = StateGraph(SubState)
subgraph_builder.add_node("some_node", node_in_subgraph)
subgraph_builder.add_node("human_node", human_node)
subgraph_builder.add_edge(START, "some_node")
subgraph = subgraph_builder.compile(checkpointer=checkpointer1)


counter_parent_node = 0


class State(TypedDict):
    """The graph state."""

    state_counter: int


async def parent_node(state: State):
    """This parent node will invoke the subgraph."""
    global counter_parent_node

    counter_parent_node += 1  # This code will run again on resuming!
    print(f"Entered `parent_node` a total of {counter_parent_node} times")

    # Please note that we're intentionally incrementing the state counter
    # in the graph state as well to demonstrate that the subgraph update
    # of the same key will not conflict with the parent graph (until
    # sub_state = {"sub_state_counter": state["state_counter"] + 1}
    return Command(goto="sub_node")


async def sub_node(state: State):
    """This parent node will invoke the subgraph."""

    # Please note that we're intentionally incrementing the state counter
    # in the graph state as well to demonstrate that the subgraph update
    # of the same key will not conflict with the parent graph (until
    sub_state = {"sub_state_counter": state["state_counter"] + 1}

    async for chunk in subgraph.astream(sub_state):
        print(chunk)

    return Command(goto="end")


builder = StateGraph(State)
builder.add_node("parent_node", parent_node)
builder.add_node("sub_node", sub_node)
builder.add_edge(START, "parent_node")

# A checkpointer must be enabled for interrupts to work!
checkpointer = MemorySaver()
graph = builder.compile(checkpointer=checkpointer)


async def main_async():
    uuid1 = uuid.uuid4()
    config1 = {
        "configurable": {
            "thread_id": uuid1,
        }
    }
    config2 = {
        "configurable": {
            "thread_id": uuid1,
        }
    }

    async for chunk in graph.astream(
        input={"state_counter": 1},
        config=config1,
        stream_mode=["custom", "values"],
        subgraphs=True,
    ):
        print(chunk)

    print("--- Resuming ---")

    async for chunk in graph.astream(
        Command(resume="39"),
        config=config2,
        stream_mode=["custom", "values"],
        subgraphs=True,
    ):
        print(chunk)


if __name__ == "__main__":
    asyncio.run(main_async())
