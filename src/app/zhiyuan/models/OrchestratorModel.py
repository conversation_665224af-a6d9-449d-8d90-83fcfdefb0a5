from typing import Optional, List

from langgraph.graph import MessagesState
from pydantic import BaseModel, Field

from src.app.zhiyuan.models.model import UserInfoDTO


class SubQuestion(BaseModel):
    """子问题模型"""

    input: Optional[str] = Field(0, description="拆解后子问题")
    agent: Optional[str] = Field(0, description="拆解后的agent")
    result: Optional[str] = None


class SchoolBO(BaseModel):
    school: Optional[str]


class MajorBO(BaseModel):
    major: Optional[str]


class BizRuntimeBO(BaseModel):
    params: Optional[dict] = None
    major: Optional[MajorBO] = None
    context: Optional[dict] = None
    school: Optional[SchoolBO] = None


class ZhiYuanState(MessagesState):
    """状态类，继承自MessagesState并添加next属性。

    Attributes:
        next: 跟踪下一个应该执行的节点
    """

    # 上下文分离如何共享信息

    userInfo: Optional[UserInfoDTO] = None
    messageId: Optional[str] = None
    conversationId: Optional[str] = None
    messages: Optional[list[dict]] = None
    isInit: Optional[bool] = True
    action: Optional[str] = None
    guideGotoDemo: Optional[str] = None
    bizRuntimeBO: Optional[BizRuntimeBO] = None
    interrupt: Optional[bool] = False
    subQuestions: Optional[List[SubQuestion]] = None
    index: Optional[int] = 0
    handleSummary: Optional[bool] = False
