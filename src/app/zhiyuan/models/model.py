from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field


class ConsumeResultStatsuEnum(Enum):
    # 消费成功
    CONSUME_CREDIT_CONSUMED = "consumed"
    # 积分不足
    CONSUME_CREDIT_INSUFFICIENT = "insufficient"


class UserInfoDTO(BaseModel):
    """用户信息DTO"""

    id: Optional[int] = Field(None, description="ID")
    username: Optional[str] = Field(None, description="名字")
    mobile: Optional[str] = Field(None, description="手机号")
    email: Optional[str] = Field(None, description="邮箱")
    wx_openid: Optional[str] = Field(None, description="微信openid")
    wx_unionid: Optional[str] = Field(None, description="微信unionid")
    gender_key: Optional[str] = Field(None, description="性别")
    exam_no: Optional[str] = Field(None, description="考生号")
    province_key: Optional[str] = Field(None, description="省份")
    school: Optional[str] = Field(None, description="学校")
    total_score: Optional[Decimal] = Field(None, description="总分")
    sort: Optional[int] = Field(None, description="排名")
    chinese_score: Optional[Decimal] = Field(None, description="语文分数")
    math_score: Optional[Decimal] = Field(None, description="数学分数")
    english_score: Optional[Decimal] = Field(None, description="英语分数")
    physics_score: Optional[Decimal] = Field(None, description="物理分数")
    chemistry_score: Optional[Decimal] = Field(None, description="化学分数")
    biology_score: Optional[Decimal] = Field(None, description="生物分数")
    politics_score: Optional[Decimal] = Field(None, description="政治分数")
    history_score: Optional[Decimal] = Field(None, description="历史分数")
    geography_score: Optional[Decimal] = Field(None, description="地理分数")
    other_score: Optional[Decimal] = Field(None, description="其他分数")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

    @staticmethod
    def from_domain(domain):
        """从领域对象转换"""
        if domain is None:
            return None
        return UserInfoDTO(
            id=domain.id,
            username=domain.username,
            mobile=domain.mobile,
            email=domain.email,
            wx_openid=domain.wx_openid,
            wx_unionid=domain.wx_unionid,
            gender_key=domain.gender_key,
            exam_no=domain.exam_no,
            province_key=domain.province_key,
            school=domain.school,
            total_score=domain.total_score,
            sort=domain.sort,
            chinese_score=domain.chinese_score,
            math_score=domain.math_score,
            english_score=domain.english_score,
            physics_score=domain.physics_score,
            chemistry_score=domain.chemistry_score,
            biology_score=domain.biology_score,
            politics_score=domain.politics_score,
            history_score=domain.history_score,
            geography_score=domain.geography_score,
            other_score=domain.other_score,
            created_at=domain.created_at,
            updated_at=domain.updated_at,
        )


class ConsumeCreditResultDTO(BaseModel):
    status: str = Field(
        ..., description="消费结果"
    )  # insufficient 积分不足，consumed 消费成功
    remainCredit: int = Field(..., description="剩余总积分")


class ConsumeCreditResponse(BaseModel):
    """积分消费响应数据模型"""

    data: Optional[ConsumeCreditResultDTO] = Field(None, description="响应数据")
    success: bool = Field(True, description="是否成功")


class GetProfileResponse(BaseModel):
    """搜索响应数据模型"""

    data: UserInfoDTO
    success: bool


async def command_update(state):
    return {
        "messages": state["messages"],
    }
