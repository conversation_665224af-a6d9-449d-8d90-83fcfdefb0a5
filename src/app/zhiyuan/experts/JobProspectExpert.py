# -*- coding: utf-8 -*-
"""
就业前景专家模块

该模块实现了一个专门处理就业前景相关问题的专家系统。
主要功能包括：
1. 接收用户关于就业前景的查询
2. 使用 OpenSearch 搜索相关的就业前景信息
3. 流式返回搜索结果
"""

from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger


from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.app.zhiyuan.models.model import command_update
from src.exe.query.JobProspectQueryExe import JobProspectQueryExe

logger = setup_logger(name=__name__, level="DEBUG")


class JobProspectExpert:
    """
    就业前景专家类

    负责处理与就业前景相关的用户查询，包括但不限于：
    - 专业就业方向
    - 行业发展前景
    - 薪资水平分析
    - 人才需求趋势
    """

    def __init__(self):
        """初始化就业前景专家"""
        logger.info("[JobProspectExpert] Initializing Job Prospect Expert Agent")

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter) -> Command:
        """
        处理用户的就业前景相关问题

        Args:
            state (ZhiYuanState): 当前会话状态，包含用户问题和上下文信息
            writer (StreamWriter): 用于流式输出结果的写入器

        Returns:
            Command: 下一步处理指令，通常是返回给协调器

        工作流程：
        1. 从状态中获取用户问题
        2. 使用 OpenSearch 搜索相关信息
        3. 流式返回搜索结果
        4. 保存结果到状态
        """
        # 获取会话ID和用户问题
        conversation_id = state.get("conversationId", "N/A_ConvID")
        logger.info(
            f"[JobProspectExpert] Starting to process conversation {conversation_id}"
        )

        user_question = state["subQuestions"][state["index"]].input
        if not user_question:
            logger.error(
                f"[JobProspectExpert] No user question found in state for conversation {conversation_id}."
            )
            writer({"data": AiChatResultBO(text="抱歉，我没有收到您的问题。")})
            return Command(
                goto=AgentTypeEnum.ORCHESTRATOR.value,
                update=await command_update(state),
            )

        logger.info(f"[JobProspectExpert] Current user question: '{user_question}'")
        full_response_content = ""

        try:
            # 使用 JobProspectQueryExe 进行搜索，该类中已实现 OpenSearch 的调用
            async for prospect_content in JobProspectQueryExe.search_job_prospect(
                query_text=user_question,
                size=3,  # 默认返回前3个最相关的结果
                conversation_id=conversation_id,
            ):
                if not prospect_content:
                    continue
                # 流式输出搜索结果
                writer({"data": AiChatResultBO(text=prospect_content)})
                full_response_content += prospect_content

            # 保存结果到状态，供后续处理使用
            state["subQuestions"][state["index"]].result = full_response_content

        except Exception as e:
            # 异常处理：记录错误并返回友好的错误提示
            logger.error(
                f"[JobProspectExpert] Error during prospect search: {str(e)}",
                exc_info=True,
            )
            writer(
                {
                    "data": AiChatResultBO(
                        text="抱歉，查询就业前景信息时发生错误，请稍后再试。"
                    )
                }
            )

        logger.info(
            f"[JobProspectExpert] Conversation {conversation_id} processing completed"
        )
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value, update=await command_update(state)
        )
