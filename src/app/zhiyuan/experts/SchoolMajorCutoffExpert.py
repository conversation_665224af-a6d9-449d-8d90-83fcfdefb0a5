"""
入学分数线 Agent —— LangGraph 节点实现
"""

import os
import time
from langgraph.prebuilt import create_react_agent
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger
from typing import Optional, Dict, Any
from pydantic import BaseModel

from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.app.zhiyuan.models.model import command_update

from src.infrastructure.llm.llm_client import create_llm
from src.exe.query.school_major_cutoff_query_exe import SchoolMajorCutoffQueryExe

logger = setup_logger(name=__name__, level="DEBUG")


# ----------------------------------------------------------------------
# 1. Tool Wrapper (Single Tool: get_all_scores)
# ----------------------------------------------------------------------
async def get_all_scores(
    school_name: Optional[str] = None,
    province_name: Optional[str] = None,
    year: Optional[int] = None,
    major_name: Optional[str] = None,
) -> Dict[str, Any]:
    """
    查询高考专业录取分数线详细信息，直接调用数据库仓储层的get_cutoff方法。
    参数说明：
    - school_name: 学校名称 (例如 "清华大学")
    - province_name: 省份名称 (例如 "山东")
    - year: 年份 (例如 2023)
    - major_name: 专业名称 (例如 "计算机科学与技术")

    使用场景提示：
    1. 查询特定院校分数线：提供 school_name, province_name, year。
    2. 查询特定专业分数线：提供 school_name, major_name, province_name, year。
    3. 查询省份整体情况（用于推断省控线或热门院校）：提供 province_name, year。

    返回：包含完整查询结果的字典
    """
    try:
        log_params = {
            "school_name": school_name,
            "province_name": province_name,
            "year": year,
            "major_name": major_name,
        }
        logger.info(
            f"[AdmissionCutoffAgent] Tool 'get_all_scores' called with params: {log_params}"
        )
        start_time = time.time()

        results = await SchoolMajorCutoffQueryExe.get_cutoff_data(
            school_name=school_name,
            province_name=province_name,
            year=year,
            major_name=major_name,
        )

        # Convert ORM objects to dictionaries
        data = [result.__dict__ for result in results]
        for item in data:
            if "_sa_instance_state" in item:
                del item["_sa_instance_state"]

        result = {"success": True, "data": data, "total": len(data)}

        execution_time = time.time() - start_time
        logger.info(
            f"[AdmissionCutoffAgent] 'get_all_scores' execution_time: {execution_time:.2f}s. Found {len(data)} records."
        )

        return result
    except Exception as e:
        logger.error(
            f"[AdmissionCutoffAgent] Error in 'get_all_scores': {str(e)}", exc_info=True
        )
        return {
            "success": False,
            "message": f"工具执行错误: {str(e)}",
            "data": [],
            "total": 0,
        }


# ----------------------------------------------------------------------
# 2. Agent 节点
# ----------------------------------------------------------------------


# AdmissionResponse can be removed if not used elsewhere, or kept if it's a general response structure.
# For now, assuming it might be used by the orchestrator or other parts, so keeping it.
class AdmissionResponse(BaseModel):
    """录取信息响应模型 (可能已废弃或由LLM直接生成)"""

    college: Optional[str] = None
    major: Optional[str] = None
    year: Optional[int] = None
    province: str = "山东省"
    score: Optional[int] = None
    rank: Optional[int] = None
    context_type: Optional[str] = None  # This was for the old query_admission_context


class SchoolMajorCutoffExpert:
    def __init__(self):
        llm = create_llm(
            model_name=os.getenv("QWEN_32_B"),
            api_key=os.getenv("ALIBABA_API_KEY"),
            api_base=os.getenv("ALIBABA_BASE_URL"),
            temperature=0.2,
        )
        tools = [get_all_scores]
        system_prompt = (
            "你是一名高考志愿咨询师，负责查询高考录取信息。\n"
            "你只有一个工具可用：`get_all_scores`。请根据用户意图，合理组合该工具的参数进行查询。\n\n"
            "工具 `get_all_scores` 参数说明:\n"
            "- school_name: 学校名称 (可选)\n"
            "- province_name: 省份名称 (可选, 但通常需要指定)。注意：必须使用简称，如'山东'而非'山东省'，'北京'而非'北京市'\n"
            "- year: 年份 (可选, 默认查询最新数据，但建议指明)\n"
            "- major_name: 专业名称 (可选)\n\n"
            "工作流程指引:\n"
            "1. **理解用户意图**：用户是想查具体学校的专业分、学校分，还是某省份的整体录取情况（如热门学校、大概的控制线范围）？\n"
            "2. **参数选择**:\n"
            "   - **查院校整体分数线**：主要使用 `school_name`, `province_name`, `year`。\n"
            "   - **查专业分数线**：主要使用 `school_name`, `major_name`, `province_name`, `year`。专业名称尽量精确，若用户提供的是大类，也直接使用。\n"
            "   - **查省份概况 (如热门院校、参考控制线)**：主要使用 `province_name`, `year`。\n"
            "3. **调用工具**：使用提取的参数调用 `get_all_scores`。\n"
            "4. **解析结果**：工具返回的是包含 `success`, `data` (列表), `total` (总数) 的字典。如果 `success` 为false或 `data` 为空，应明确告知用户未查到数据或查询失败。\n"
            "5. **回答用户**：基于 `data` 中的信息，清晰、准确地回答用户问题。只提供客观数据，不做主观判断或推荐。\n\n"
            "重要提示:\n"
            "- 所有查询参数均为可选，但通常需要组合使用才能获得有效结果。请主动分析用户问题，提取关键信息作为参数。\n"
            "- 省份（province_name）必须使用简称，如'山东'而非'山东省'，'北京'而非'北京市'。\n"
            "- 年份（year）很重要，如果用户未提供，你可以提醒用户或尝试查询最近几年的数据（通过多次调用工具实现，如果必要）。\n"
            "- 返回的 `data` 是一个包含录取信息的对象列表，你需要从中提取用户需要的信息。例如：`data: [{'school_name': 'xx大学', 'major_name': 'yy专业', 'lowest_score': '580', ...}]`\n"
            "- 如果数据量较大（`total` 值所示），提醒用户结果可能不完整，或建议用户提供更精确的查询条件。\n"
            '- 工具调用参数必须是有效的JSON格式，属性名用双引号。例如: {"school_name": "山东大学", "year": 2022, "province_name": "山东"}'
        )
        self.graph = create_react_agent(
            model=llm,
            tools=tools,
            prompt=system_prompt,
        )

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter):
        try:
            conversation_id = state["conversationId"]
            user_question: str = state["messages"][-1].content
            config = {"configurable": {"thread_id": conversation_id}}
            full_response_content = ""
            async for chunk in self.graph.astream(
                {"messages": [{"role": "user", "content": user_question}]},
                config=config,
            ):
                if "agent" in chunk and "messages" in chunk["agent"]:
                    for msg in chunk["agent"]["messages"]:
                        if hasattr(msg, "content") and msg.content:
                            full_response_content += msg.content
                            writer({"data": AiChatResultBO(text=msg.content)})
            state["subQuestions"][state["index"]].result = full_response_content
            return Command(
                goto=AgentTypeEnum.ORCHESTRATOR.value,
                update=await command_update(state),
            )
        except Exception as e:
            logger.error(f"[AdmissionCutoffAgent] 处理失败: {str(e)}", exc_info=True)
            raise
