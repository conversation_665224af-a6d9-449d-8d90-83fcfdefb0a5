import os
from typing import List

from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import Zhi<PERSON>uanState
from src.app.zhiyuan.models.model import command_update
from src.exe.query.SchoolInfoQueryExe import SchoolInfoQueryExe

logger = setup_logger(
    name=__name__, level=os.getenv("LOG_LEVEL", "INFO").upper()
)  # 日志级别可设为 INFO


class SchoolExpert:
    def __init__(self, is_test_mode: bool = False):
        logger.debug(
            "[SchoolExpert] Initializing School Expert Agent (OpenSearch Mode)"
        )  # 初始化日志用 DEBUG

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter) -> Command:
        conversation_id = state["conversationId"]  # 直接访问
        current_sub_question_index = state["index"]  # 直接访问
        user_question = state["subQuestions"][
            current_sub_question_index
        ].input  # 直接访问

        logger.info(
            f"[SchoolExpert] ConvID: {conversation_id}, Index: {current_sub_question_index}, "
            f"Question: '{user_question[:50]}...'"
        )

        accumulated_chunks: List[str] = []
        final_result_for_state: str

        try:
            async for school_info_chunk in SchoolInfoQueryExe.search_school_info(
                query_text=user_question, size=2, conversation_id=conversation_id
            ):
                if school_info_chunk:
                    writer({"data": AiChatResultBO(text=school_info_chunk)})
                    accumulated_chunks.append(school_info_chunk)

            final_result_for_state = "".join(accumulated_chunks)

            if not final_result_for_state:
                final_result_for_state = (
                    "关于您的问题，我暂时没有查询到相关的学校信息。"
                )
                # 只有在完全没有收到任何有效 chunk 时才发送此消息
                if not accumulated_chunks:
                    writer({"data": AiChatResultBO(text=final_result_for_state)})

        except Exception as e:
            logger.error(
                f"[SchoolExpert] Error for ConvID: {conversation_id}, Question: '{user_question[:50]}...': {e}",
                exc_info=True,  # 在生产中，对于特定已知异常可能不需要完整的 exc_info
            )
            final_result_for_state = "抱歉，查询学校信息时发生系统错误，请稍后再试。"
            writer({"data": AiChatResultBO(text=final_result_for_state)})

        # 更新状态中的结果
        state["subQuestions"][
            current_sub_question_index
        ].result = final_result_for_state

        logger.info(f"[SchoolExpert] ConvID: {conversation_id} processing completed.")
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await command_update(state),
        )
