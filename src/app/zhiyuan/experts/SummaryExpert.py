import os

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.app.zhiyuan.models.model import command_update
from src.infrastructure.llm.llm_client import async_create_llm

logger = setup_logger(name=__name__, level="DEBUG")

SYSTEM_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人。

# 任务
1. 对之前所有专家的回答进行总结和整合，为用户提供一个全面、连贯的答案。
2. 识别并处理与高考志愿填报无关的问题。

# 回答要求
1. 整合所有专家的回答，确保信息完整且不重复。
2. 按照逻辑顺序组织信息，使回答更加连贯。
3. 突出重要信息和建议。
4. 如果发现不同专家之间的回答有冲突，需要明确指出并给出建议。
5. 保持专业、友好的语气。
6. 在总结的最后，可以适当补充一些建议或提醒。

# 无关问题处理
1. 如果发现用户的问题与高考志愿填报完全无关，需要礼貌地指出这一点。
2. 对于无关问题，建议用户重新提问与高考志愿填报相关的问题。
3. 如果问题部分相关，可以尝试将问题引导到高考志愿填报的语境中。
4. 始终保持专业和友好的态度，避免直接拒绝回答。
"""


class SummaryExpert:
    def __init__(self):
        logger.info("[SummaryExpert] Initializing Summary Expert Agent")

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter) -> Command:
        conversation_id = state.get("conversationId", "N/A_ConvID")
        logger.info(
            f"[SummaryExpert] Starting to process conversation {conversation_id}"
        )

        # if len(state["subQuestions"]) <= 2:
        #     return Command(
        #         goto=AgentTypeEnum.ORCHESTRATOR.value,
        #         update=await command_update(state),
        #     )

        if not state.get("messages"):
            logger.error(
                f"[SummaryExpert] No messages found in state for conversation {conversation_id}."
            )
            writer({"data": AiChatResultBO(text="抱歉，没有找到之前的对话内容。")})
            return Command(
                goto=AgentTypeEnum.ORCHESTRATOR.value,
                update=await command_update(state),
            )

        # 获取最近的对话历史
        # recent_messages = (
        #     state["messages"][-10:]
        #     if len(state["messages"]) > 10
        #     else state["messages"]
        # )

        result_summary = state["subQuestions"][state["index"]].input
        for sub_question in state["subQuestions"]:
            if sub_question.result:
                result_summary += "\n" + sub_question.result

        # conversation_history = "\n".join([f"{msg.content}" for msg in recent_messages])

        # 准备 LLM 提示
        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(SYSTEM_PROMPT),
                ("user", "请根据以下对话历史进行总结：\n{result_summary}"),
            ]
        )

        # 初始化 LLM
        logger.info("[SummaryExpert] Initializing LLM for response generation")
        try:
            llm = await async_create_llm(
                **{
                    "model_name": os.getenv("QWEN_32_B", "qwen-32b-chat"),
                    "api_key": os.getenv("ALIBABA_API_KEY"),
                    "api_base": os.getenv("ALIBABA_BASE_URL"),
                    "temperature": 0.3,
                }
            )
            chain = prompts | llm

            logger.info("[SummaryExpert] Generating summary response...")
            full_response_content = ""
            async for chunk in chain.astream(
                {
                    "result_summary": result_summary,
                }
            ):
                if chunk.content:
                    full_response_content += chunk.content
                    writer({"data": AiChatResultBO(text=chunk.content)})

        except Exception as e:
            logger.error(
                f"[SummaryExpert] Error during summary generation: {str(e)}",
                exc_info=True,
            )
            writer(
                {"data": AiChatResultBO(text="抱歉，生成总结时遇到问题，请稍后再试。")}
            )

        logger.info(f"[SummaryExpert] Conversation {conversation_id} summary completed")
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await command_update(state),
        )
