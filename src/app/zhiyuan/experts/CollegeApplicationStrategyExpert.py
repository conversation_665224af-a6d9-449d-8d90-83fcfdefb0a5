import os
from typing import List

from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.app.zhiyuan.models.model import command_update
from src.exe.query.CollegeApplicationStrategyQueryExe import (
    CollegeApplicationStrategyQueryExe,
)

logger = setup_logger(name=__name__, level=os.getenv("LOG_LEVEL", "INFO").upper())


class CollegeApplicationStrategyExpert:
    def __init__(self, is_test_mode: bool = False):
        logger.debug(
            "[CollegeApplicationStrategyExpert] Initializing College Application Strategy Expert"
        )

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter) -> Command:
        conversation_id = state["conversationId"]
        current_sub_question_index = state["index"]
        user_question = state["subQuestions"][current_sub_question_index].input

        logger.info(
            f"[CollegeApplicationStrategyExpert] ConvID: {conversation_id}, Index: {current_sub_question_index}, "
            f"Question: '{user_question[:50]}...'"
        )

        accumulated_chunks: List[str] = []
        final_result_for_state: str

        try:
            async for (
                strategy_chunk
            ) in CollegeApplicationStrategyQueryExe.search_college_application_strategy(
                query_text=user_question, size=2, conversation_id=conversation_id
            ):
                if strategy_chunk:
                    writer({"data": AiChatResultBO(text=strategy_chunk)})
                    accumulated_chunks.append(strategy_chunk)

            final_result_for_state = "".join(accumulated_chunks)

            if not final_result_for_state:
                final_result_for_state = (
                    "关于您的问题，我暂时没有查询到相关的志愿填报策略信息。"
                )
                if not accumulated_chunks:
                    writer({"data": AiChatResultBO(text=final_result_for_state)})

        except Exception as e:
            logger.error(
                f"[CollegeApplicationStrategyExpert] Error for ConvID: {conversation_id}, Question: '{user_question[:50]}...': {e}",
                exc_info=True,
            )
            final_result_for_state = (
                "抱歉，查询志愿填报策略时发生系统错误，请稍后再试。"
            )
            writer({"data": AiChatResultBO(text=final_result_for_state)})

        # 更新状态中的结果
        state["subQuestions"][
            current_sub_question_index
        ].result = final_result_for_state

        logger.info(
            f"[CollegeApplicationStrategyExpert] ConvID: {conversation_id} processing completed."
        )
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await command_update(state),
        )
