# -*- coding: utf-8 -*-


from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.app.zhiyuan.models.model import command_update

from src.exe.query.AdmissionPolicyQueryExe import AdmissionPolicyQueryExe

logger = setup_logger(name=__name__, level="DEBUG")


class AdmissionPolicyExpert:
    def __init__(self):
        logger.info(
            "[AdmissionPolicyExpert] Initializing Admission Policy Expert Agent"
        )

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter) -> Command:
        conversation_id = state.get("conversationId", "N/A_ConvID")
        logger.info(
            f"[AdmissionPolicyExpert] Starting to process conversation {conversation_id}"
        )
        user_question = state["subQuestions"][state["index"]].input

        if not user_question:
            logger.error(
                f"[AdmissionPolicyExpert] No user question found in state for conversation {conversation_id}."
            )
            writer({"data": AiChatResultBO(text="抱歉，我没有收到您的问题。")})
            return Command(
                goto=AgentTypeEnum.ORCHESTRATOR.value,
                update=await command_update(state),
            )

        logger.info(f"[AdmissionPolicyExpert] Current user question: '{user_question}'")
        full_response_content = ""
        try:
            async for policy_content in AdmissionPolicyQueryExe.search_admission(
                query_text=user_question, size=3, conversation_id=conversation_id
            ):
                if not policy_content:
                    continue
                # 流式输出搜索结果
                writer({"data": AiChatResultBO(text=policy_content)})
                full_response_content += policy_content

            state["subQuestions"][state["index"]].result = full_response_content
        except Exception as e:
            logger.error(
                f"[AdmissionPolicyExpert] Error during policy search: {str(e)}",
                exc_info=True,
            )
            writer(
                {
                    "data": AiChatResultBO(
                        text="抱歉，查询招生政策时发生错误，请稍后再试。"
                    )
                }
            )

        logger.info(
            f"[AdmissionPolicyExpert] Conversation {conversation_id} processing completed"
        )
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await command_update(state),
        )
