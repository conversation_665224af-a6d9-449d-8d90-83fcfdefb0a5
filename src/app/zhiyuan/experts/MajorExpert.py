import os
from typing import List, Dict, Any

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum
from src.app.zhiyuan.models.OrchestratorModel import ZhiYuanState
from src.app.zhiyuan.models.model import command_update
from src.exe.query.MajorInfoQueryExe import MajorInfoQueryExe
from src.infrastructure.llm.llm_client import async_create_llm
from src.infrastructure.db.crud.ai.MajorInfoRepository import MajorInfoRepository

logger = setup_logger(name=__name__, level="DEBUG")

SYSTEM_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人

# 场景
根据专业信息对用户的问题进行回答，包括但不限于：
1. 专业基本信息：学习内容、主要课程、就业方向等
2. 专业特点和要求：学习难度、所需基础、能力要求等
3. 专业对比：不同专业的区别、就业前景比较等
4. 专业推荐：根据兴趣领域推荐相关专业
5. 交叉学科：结合多个领域的专业信息
6. 选科影响：根据选科组合推荐适合的专业

# 专业信息
{major_info}
"""


EXTRACT_MAJOR_PROMPT = """
你是一个专业名称提取助手。你的任务是从用户的问题中提取出相关的专业名称。
请仔细分析用户的问题，提取出所有可能相关的专业名称。
只返回专业名称列表，用逗号分隔，不要包含任何其他解释或说明。

例如：
用户问题：我想了解计算机专业的情况
输出：计算机科学与技术,软件工程,计算机应用技术

用户问题：{user_question}
"""


class MajorExpert:
    def __init__(self):
        """
        初始化专业问答代理
        """

    @staticmethod
    async def extract_major_names_from_question(user_question: str) -> List[str]:
        """
        使用大模型从用户问题中抽取专业名称列表
        :param user_question: 用户输入的问题
        :return: 专业名称列表
        """
        try:
            prompts = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(EXTRACT_MAJOR_PROMPT),
                    ("user", "{user_question}"),
                ]
            )

            llm = await async_create_llm(
                **{
                    "model_name": os.getenv("QWEN_32_B"),
                    "api_key": os.getenv("ALIBABA_API_KEY"),
                    "api_base": os.getenv("ALIBABA_BASE_URL"),
                    "temperature": 0.2,
                }
            )

            chain = prompts | llm

            response = await chain.ainvoke({"user_question": user_question})
            # 将返回的字符串按逗号分割，并去除空白字符
            major_names = [name.strip() for name in response.content.split(",")]
            logger.info(f"Extracted major names: {major_names}")
            return major_names
        except Exception as e:
            logger.error(f"Error extracting major names: {str(e)}")
            return [user_question]  # 出错时返回原问题作为关键词

    @staticmethod
    async def search_majors_by_name(user_question: str) -> List[Dict[str, Any]]:
        """
        先用大模型抽取专业名称列表，再对每个名称模糊查询，合并去重返回。
        :param user_question: 用户输入
        :return: 匹配的专业信息列表
        """
        try:
            major_names = await MajorExpert.extract_major_names_from_question(
                user_question
            )
            all_majors = []
            seen_ids = set()
            for name in major_names:
                majors = await MajorInfoRepository.get_by_name_fuzzy(name)
                for major in majors:
                    if major.id not in seen_ids:
                        all_majors.append(major.__dict__)
                        seen_ids.add(major.id)
            return all_majors
        except Exception as e:
            logger.error(
                f"Error searching majors for question '{user_question}': {str(e)}"
            )
            return []

    async def __call__(self, state: ZhiYuanState, writer: StreamWriter) -> Command:
        conversation_id = state.get("conversationId", "N/A_ConvID")
        logger.info(f"[MajorExpert] Starting to process conversation {conversation_id}")
        user_question = state["subQuestions"][state["index"]].input

        if not user_question:
            logger.error(
                f"[MajorExpert] No user question found in state for conversation {conversation_id}."
            )
            writer({"data": AiChatResultBO(text="抱歉，我没有收到您的问题。")})
            return Command(
                goto=AgentTypeEnum.ORCHESTRATOR.value,
                update=await command_update(state),
            )

        logger.info(f"[MajorExpert] Current user question: '{user_question}'")
        full_response_content = ""
        logger.info("[MajorExpert] Searching major...")
        try:
            async for major_content in MajorInfoQueryExe.search_major_info(
                query_text=user_question
            ):
                if not major_content:
                    continue
                # 流式输出搜索结果
                logger.info(f"[MajorExpert] Retrieved major content: {major_content}")
                writer({"data": AiChatResultBO(text=major_content)})
                full_response_content += major_content

            state["subQuestions"][state["index"]].result = full_response_content
        except Exception as e:
            logger.error(
                f"[MajorExpert] Error during policy search: {str(e)}",
                exc_info=True,
            )
            writer(
                {
                    "data": AiChatResultBO(
                        text="抱歉，查询招生政策时发生错误，请稍后再试。"
                    )
                }
            )

        logger.info(
            f"[MajorExpert] Conversation {conversation_id} processing completed"
        )
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await command_update(state),
        )
