"""
城市导向志愿填报指导主入口

基于用户的城市偏好，提供个性化的志愿填报指导服务。
"""

import time
from typing import Dict, Any
from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from .modules.city_experts_group import CityFirstExpertsGroup
from ..common.base.base_mentor import BaseMentor
from .coordinator import CityFirstCoordinator
from .modules.initial_city_preference_questionnaire import (
    InitialCityPreferenceQuestionnaire,
)
from .modules.report_generation import ReportGeneration
from .modules.user_persona_clarification import UserPersonaClarification
from .models.model import city_first_mentor_command_update, CityFirstMentorState
from ...constant import AgentTypeEnum

logger = setup_logger(name=__name__, level="DEBUG")


class CityFirstMentor(BaseMentor):
    """城市导向志愿填报指导主入口

    基于通用导师架构，提供城市导向的志愿填报指导服务。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "CityFirstMentor"

    async def _create_workflow_graph(self):
        """创建工作流图

        Args:
            checkpointer: 检查点保存器

        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        # 创建模块实例
        modules = {
            "CityFirstCoordinator": CityFirstCoordinator(),
            "InitialCityPreferenceQuestionnaire": InitialCityPreferenceQuestionnaire(),
            "UserPersonaClarification": UserPersonaClarification(),
            "CityFirstExpertsGroup": CityFirstExpertsGroup(),
            "ReportGeneration": ReportGeneration(),
        }

        # 创建基础工作流
        workflow = self._create_base_workflow(CityFirstMentorState, modules)

        # 设置入口点
        self._set_workflow_entry_point(workflow, "CityFirstCoordinator")

        # 编译工作流
        return await self._compile_workflow(workflow)

    def _prepare_init_state(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """准备初始状态

        Args:
            state: 原始状态

        Returns:
            Dict[str, Any]: 处理后的初始状态
        """
        # 使用基础状态准备方法
        init_state = self._get_default_init_state(state)

        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(init_state)

        # 添加职业优先模式特定的初始化到 bizRuntimeBO.context
        biz_runtime = init_state["bizRuntimeBO"]
        context = biz_runtime.context
        context["flow_metadata"] = {
            "mode": "city_first",
            "version": "2.0.0",
            "started_at": time.time(),
        }

        return init_state

    async def _return_to_orchestrator(self, state: Dict[str, Any]) -> Command:
        """返回到编排器

        Args:
            state: 当前状态

        Returns:
            Command: 返回编排器的命令
        """
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await city_first_mentor_command_update(state),
        )

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CityFirstCoordinator",
            update=await city_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CityFirstMentor",
            update=await city_first_mentor_command_update(state),
        )
