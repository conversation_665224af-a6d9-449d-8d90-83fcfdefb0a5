"""
城市导向流程配置

定义城市导向志愿填报指导的完整流程配置。
"""

# 城市导向流程配置
CITY_FIRST_FLOW = {
    "sequence": [
        "InitialCityPreferenceQuestionnaire",  # 1. 初始城市偏好问卷
        "UserPersonaClarification",  # 2. 用户画像澄清
        "CityFirstExpertsGroup",  # 3. 专家团
        "ReportGeneration",  # 4. 报告生成
    ],
    "module_configs": {
        "InitialCityPreferenceQuestionnaire": {
            "questionnaire_id": "initial_city_preference",
            "message_type": "text",  # 文本问答类型
            "auto_advance": True,
            "timeout_seconds": 300,
            "retry_count": 2,  # 减少重试次数
            "question_type": "text",  # 文本输入类型
        },
        "UserPersonaClarification": {
            "questionnaire_id": "user_persona_clarification",
            "message_type": "bipolar",
            "auto_advance": True,
            "timeout_seconds": 600,
            "retry_count": 3,
            "question_type": "bipolar",
        },
        "ReportGeneration": {
            "auto_advance": False,
            "support_query": True,
            "timeout_seconds": 120,
            "report_type": "city_first",
        },
    },
    "flow_metadata": {
        "name": "城市导向志愿填报指导",
        "description": "基于用户的城市偏好，提供个性化的志愿填报指导",
        "version": "1.0",
        "action": "CityQA",  # 注册的action名称
    },
}
