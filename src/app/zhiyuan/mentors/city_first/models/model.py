"""
城市导向流程的数据模型定义

基于 major_first_v2 的模型结构，适配城市导向的业务场景。
"""

from typing import Dict, Any

from src.app.zhiyuan.mentors.common.models import BaseMentorState


class CityFirstMentorState(BaseMentorState):
    """专业优先模式状态模型

    继承自统一的基础状态模型，添加专业优先模式特定的字段。
    """

    # 专业优先模式特定字段可以在这里添加
    # 目前使用基础状态模型已经足够
    pass


# 城市导向流程的状态更新函数
async def city_first_mentor_command_update(state: Dict[str, Any]) -> Dict[str, Any]:
    """城市导向流程的命令更新函数

    Args:
        state: 当前状态

    Returns:
        Dict[str, Any]: 更新后的状态
    """
    # 确保 bizRuntimeBO 存在
    return {
        "userInfo": state.get("userInfo"),
        "messageId": state.get("messageId"),
        "conversationId": state.get("conversationId"),
        "messages": state.get("messages"),
        "action": state.get("action"),
        "guideGotoDemo": state.get("guideGotoDemo"),
        "interrupt_feedback": state.get("interrupt_feedback"),
        "bizRuntimeBO": state.get("bizRuntimeBO"),
    }
