"""
城市导向流程协调器

负责协调城市导向志愿填报指导流程中各个模块的执行顺序和状态管理。
"""

from typing import Dict, Any

from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from .config.flow_config import CITY_FIRST_FLOW
from .models.model import city_first_mentor_command_update
from ..common.base.base_coordinator import BaseCoordinator

logger = setup_logger(name=__name__, level="DEBUG")


class CityFirstCoordinator(BaseCoordinator):
    """城市导向流程协调器

    基于通用协调器架构，管理城市导向志愿填报指导的完整流程。
    """

    def __init__(self):
        """初始化协调器"""
        super().__init__(CITY_FIRST_FLOW)

    @property
    def _module_name(self) -> str:
        """协调器名称"""
        return "CityFirstCoordinator"

    async def _get_command_update_function(self, state: Dict[str, Any]):
        """获取命令更新函数

        Args:
            state: 当前状态

        Returns:
            命令更新函数的结果
        """
        return await city_first_mentor_command_update(state)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器（自己）

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CityFirstCoordinator",
            update=await city_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CityFirstMentor",
            update=await city_first_mentor_command_update(state),
        )
