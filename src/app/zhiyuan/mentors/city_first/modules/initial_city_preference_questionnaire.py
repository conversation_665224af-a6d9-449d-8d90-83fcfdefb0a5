"""
初始城市偏好问卷模块

城市导向流程的第一个节点，询问用户是否已有心仪的城市。
使用简单的文本问答形式
"""

import time
from typing import Dict, Any

from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from ..models.model import city_first_mentor_command_update
from ...common.base.base_text_questionnaire import BaseTextQuestionnaire

logger = setup_logger(name=__name__, level="DEBUG")


class InitialCityPreferenceQuestionnaire(BaseTextQuestionnaire):
    """初始城市偏好问卷模块

    询问用户是否已有心仪的城市，为后续的城市导向流程提供基础信息。
    使用简单的文本问答形式，而不是复杂的结构化问卷。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "InitialCityPreferenceQuestionnaire"

    @property
    def _questionnaire_id(self) -> str:
        """问卷ID"""
        return "initial_city_preference"

    @property
    def _question_text(self) -> str:
        """问题文本"""
        return "你目前心里是否已经有一些比较心仪去往的城市了呢？如果有的话，可以告诉我具体是哪个或者哪些城市吗？"

    @property
    def _max_retries(self) -> int:
        """最大重试次数"""
        return 1

    async def _validate_answer(
        self, answer: str, state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证用户答案

        Args:
            answer: 用户答案
            state: 当前状态

        Returns:
            Dict[str, Any]: 验证结果
        """
        if not answer or not answer.strip():
            return {"valid": False, "message": "请告诉我您的想法"}

        return {"valid": True, "message": ""}

    async def _process_answer(
        self, answer: str, state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理用户答案

        Args:
            answer: 原始答案
            state: 当前状态

        Returns:
            Dict[str, Any]: 处理后的答案数据
        """
        processed_data = {"raw_answer": answer, "processed_at": time.time()}

        logger.info(f"[{self._module_name}] 处理后的答案数据: {processed_data}")
        return processed_data

    async def _get_default_answer(self, state: Dict[str, Any]) -> str:
        """获取默认答案

        Args:
            state: 当前状态

        Returns:
            str: 默认答案
        """
        return "暂时还没有明确的城市偏好，希望通过测评来了解"

    async def _get_retry_message_for_empty(
        self, retry_count: int, state: Dict[str, Any]
    ) -> str:
        """获取空答案重试消息

        Args:
            retry_count: 当前重试次数
            state: 当前状态

        Returns:
            str: 重试消息
        """
        if retry_count == 1:
            return "请告诉我您目前的想法，比如'南方城市'、'一线城市'或者'还没想好'。"
        else:
            return "如果暂时没有想法，您可以直接说'还没想好'，我会通过后续的评估帮您发现合适的城市和院校。"

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CityFirstCoordinator",
            update=await city_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CityFirstMentor",
            update=await city_first_mentor_command_update(state),
        )
