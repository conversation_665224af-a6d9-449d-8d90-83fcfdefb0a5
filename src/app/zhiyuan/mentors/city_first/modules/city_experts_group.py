from typing import Dict, Any

from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.mentors.city_first.models.model import (
    city_first_mentor_command_update,
)
from src.app.zhiyuan.mentors.common.base.base_experts_group import BaseExpertsGroup

logger = setup_logger(name=__name__, level="DEBUG")


class CityFirstExpertsGroup(BaseExpertsGroup):
    """报告生成模块 v2.0

    基于通用架构的报告生成实现，支持报告生成和查询。
    """

    @property
    def _module_name(self) -> str:
        return "CityFirstExpertsGroup"

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CityFirstCoordinator",
            update=await city_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CityFirstMentor",
            update=await city_first_mentor_command_update(state),
        )
