"""
自由对话问答流程配置

定义自由对话问答的完整流程配置。
"""

# 自由对话问答流程配置
FREE_CHAT_QA_FLOW = {
    "sequence": [
        "FreeChat",  # 1. 自由对话模块
        "UserPersonaClarification",  # 2. 用户画像澄清 (当用户选择生成报告时)
        "FreeChatExpertsGroup",  # 3. 专家团
        "ReportGeneration",  # 4. 报告生成
    ],
    "module_configs": {
        "FreeChat": {
            "auto_advance": False,  # 不自动前进，等待用户输入
            "timeout_seconds": 0,  # 无超时限制
            "retry_count": 0,  # 无重试限制
            "llm_enabled": True,  # 启用LLM分析
            "analysis_temperature": 0.1,  # 分析时使用低温度
            "response_temperature": 0.3,  # 回答时使用中等温度
        },
        "UserPersonaClarification": {
            "questionnaire_id": "user_persona_clarification",
            "message_type": "bipolar",
            "auto_advance": True,
            "timeout_seconds": 600,
            "retry_count": 3,
            "question_type": "bipolar",
            "trigger_condition": "report_requested",  # 仅在请求报告时触发
        },
        "ReportGeneration": {
            "auto_advance": False,
            "support_query": True,
            "timeout_seconds": 120,
            "report_type": "free_chat_qa",
            "trigger_condition": "persona_completed",  # 仅在画像澄清完成后触发
        },
    },
    "flow_metadata": {
        "name": "自由对话问答",
        "description": "基于用户基础信息的智能问答，适时触发深度报告生成",
        "version": "1.0",
        "action": "FreeChatQA",  # 注册的action名称
        "entry_conditions": [
            "user_basic_info_completed"  # 需要用户已完成基础信息填写
        ],
    },
}

# 问题分类配置
QUESTION_CATEGORIES = {
    "direct_answer": {
        "name": "可直接回答",
        "description": "免费服务范畴的通用问题",
        "keywords": [
            "分数线",
            "录取线",
            "政策",
            "规则",
            "什么是",
            "介绍",
            "基本信息",
            "就业",
            "前景",
            "趋势",
            "排名",
            "学费",
            "地址",
            "联系方式",
            "历年",
            "数据",
            "统计",
            "平均",
            "最低",
            "最高",
        ],
        "patterns": [
            r".*分数线.*",
            r".*录取.*分.*",
            r".*政策.*",
            r".*规则.*",
            r"什么是.*",
            r".*介绍.*",
            r".*就业.*前景.*",
        ],
    },
    "report_trigger": {
        "name": "触发报告建议",
        "description": "需要深度个性化分析的复杂问题",
        "keywords": [
            "推荐",
            "建议",
            "选择",
            "方案",
            "规划",
            "匹配",
            "适合",
            "冲稳保",
            "志愿表",
            "填报",
            "怎么办",
            "如何",
            "帮我",
            "给我",
            "分析",
            "个性化",
            "定制",
            "专属",
            "详细",
            "具体",
            "深入",
            "权衡",
        ],
        "patterns": [
            r".*推荐.*专业.*",
            r".*推荐.*学校.*",
            r".*志愿.*方案.*",
            r".*冲稳保.*",
            r".*如何.*选择.*",
            r".*帮我.*分析.*",
            r".*给我.*建议.*",
        ],
    },
}

# LLM配置
LLM_CONFIG = {
    "analysis": {
        "model_name": "qwen_32b",
        "temperature": 0.1,
        "max_tokens": 1000,
        "timeout": 30,
    },
    "response": {
        "model_name": "qwen_32b",
        "temperature": 0.3,
        "max_tokens": 2000,
        "timeout": 60,
    },
}

# 报告建议配置
REPORT_SUGGESTION_CONFIG = {
    "trigger_threshold": 0.7,  # 触发报告建议的置信度阈值
    "max_direct_answers": 3,  # 最多提供的直接回答次数
    "suggestion_cooldown": 300,  # 建议冷却时间(秒)
    "options": [
        {
            "value": "need_report",
            "text": "需要完整报告",
            "description": "获得深度个性化分析和具体方案",
        },
        {
            "value": "direct_answer",
            "text": "直接回答问题",
            "description": "基于现有信息提供通用性建议",
        },
    ],
}
