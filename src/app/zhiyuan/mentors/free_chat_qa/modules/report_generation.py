from typing import Dict, Any

from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.mentors.free_chat_qa.models.model import (
    free_chat_qa_mentor_command_update,
)
from src.app.zhiyuan.mentors.common.base.base_report_generation import (
    BaseReportGeneration,
)

logger = setup_logger(name=__name__, level="DEBUG")


class ReportGeneration(BaseReportGeneration):
    """报告生成模块 v2.0

    基于通用架构的报告生成实现，支持报告生成和查询。
    """

    @property
    def _module_name(self):
        return "FreeChatReportGeneration"

    async def _handle_free_chat_phase(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        conversation_id = str(state["conversationId"])

        if self._get_next_task(conversation_id):
            return await super()._handle_free_chat_phase(state, writer)
        else:
            self._mark_completed_and_advance(state)
            state["action"] = ""
            return await self._return_to_mentor(state)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="FreeChatQACoordinator",
            update=await free_chat_qa_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="FreeChatQAMentor",
            update=await free_chat_qa_mentor_command_update(state),
        )
