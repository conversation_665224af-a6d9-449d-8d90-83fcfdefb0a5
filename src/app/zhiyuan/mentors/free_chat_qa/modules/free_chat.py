"""
自由对话模块

基于用户已提供的基础信息，在自由对话中恰当时机触发【生成交付报告】的建议，
引导有深度需求的用户进入报告生成流程。
"""

import time
from typing import Dict, Any, Optional

from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO
from src.utils.snow_flake import Snowflake
from ..config.prompts import FREE_CHAT_ANALYSIS_PROMPT, FREE_CHAT_RESPONSE_PROMPT
from ..models.model import free_chat_qa_mentor_command_update
from ...common.base import BaseMentorModule
from ...common.services import PersistenceService

# 创建 Snowflake 实例
sf = Snowflake(worker_id=0, datacenter_id=0)

logger = setup_logger(name=__name__, level="DEBUG")


class FreeChat(BaseMentorModule):
    """自由对话模块

    智能判断用户问题性质，对深度个性化问题触发报告生成建议，
    对通用问题直接回答，实现商业化转化。
    """

    def __init__(self):
        """初始化服务依赖"""
        self.persistence_service = PersistenceService()

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "FreeChat"

    @property
    def _analysis_prompt(self) -> str:
        """问题分析提示词"""
        return FREE_CHAT_ANALYSIS_PROMPT

    @property
    def _response_prompt(self) -> str:
        """直接回答提示词"""
        # 扩展基础提示词，添加搜索结果部分
        return (
            FREE_CHAT_RESPONSE_PROMPT
            + """

# 搜索结果
以下是与用户问题相关的搜索结果，请参考这些信息来回答用户问题：

{search_results}

# 回答要求
1. 基于上述搜索结果和你的知识，提供准确、全面的回答
2. 如果搜索结果包含与问题直接相关的信息，优先使用这些信息
3. 如果搜索结果不足或不相关，使用你的知识提供通用性回答
4. 保持回答的专业性和准确性，避免臆测或提供未经验证的信息
5. 回答应该清晰、结构化，便于用户理解
"""
        )

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """自由对话主要执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        self._log_module_start(state)
        # 获取用户输入
        user_input = self._get_user_input(state)
        if not user_input:
            writer(
                {
                    "data": AiChatResultBO(
                        text="请输入您的问题，我会为您提供专业的志愿填报建议。"
                    )
                }
            )
            return await self._return_to_mentor(state)

        # 检查是否是对选项的回答
        user_answer = await self._analyze_user_answer(state)
        if user_answer:
            # 用户选择需要完整报告，进入画像澄清环节
            logger.info(f"[{self._module_name}] 用户选择需要完整报告，进入画像澄清环节")
            self._mark_completed_and_advance(state)
            return await self._return_to_coordinator(state)
        # 如果不是答案，就发送问题
        else:
            return await self._trigger_report_suggestion(state, writer, user_input)

    async def _analyze_user_answer(self, state: Dict[str, Any]) -> Optional[str]:
        """分析用户对选项的回答

        Args:
            state: 当前状态

        Returns:
            str: 用户选择的选项值，如果不是选项回答则返回None
        """
        biz_runtime = state.get("bizRuntimeBO")
        answer = (
            biz_runtime.params.get("answer", {})
            if biz_runtime and hasattr(biz_runtime, "params")
            else None
        )
        message_id = self._get_current_context(state).get(
            "free_chat_next_intent_message_id"
        )
        if answer and answer.get("id") == message_id and answer.get("value"):
            # 获取用户选择的值
            selected_value = answer["value"]

            # 更新之前发送的消息，将用户选择的值填充回原始消息
            try:
                # 获取消息ID
                message_id = self._get_current_context(state).get(
                    "free_chat_next_intent_message_id"
                )
                if message_id:
                    from src.infrastructure.db.crud.ai.AIMessageRepository import (
                        AIMessageRepository,
                    )

                    # 获取原始消息
                    message = await AIMessageRepository.get_by_id(int(message_id))
                    if message and message.message_data:
                        # 更新消息数据，添加用户选择的值
                        message_data = message.message_data
                        message_data["value"] = selected_value

                        # 更新消息
                        await AIMessageRepository.update(
                            int(message_id), {"message_data": message_data}
                        )
                        logger.info(
                            f"[{self._module_name}] 已更新消息ID {message_id} 的选项值为 {selected_value}"
                        )
            except Exception as e:
                logger.error(f"[{self._module_name}] 更新消息选项值失败: {str(e)}")

            return selected_value
        return None

    async def _trigger_report_suggestion(
        self,
        state: Dict[str, Any],
        writer: StreamWriter,
        user_input: str,
    ) -> Command:
        """触发报告生成建议

        Args:
            state: 当前状态
            writer: 写入器
            user_input: 用户输入

        Returns:
            Command: 执行命令
        """
        logger.info(f"[{self._module_name}] 触发报告生成建议")

        # 发送转场文案
        transition_text = "同学您好！我理解您的问题涉及到比较深入的个性化分析和方案匹配。\n\n这确实需要综合考虑个人分数、兴趣偏好、发展规划等多个维度的复杂决策。\n\n为了给您更全面、系统、精准的建议，我推荐为您生成一份专属的《高考志愿填报分析报告》。这份报告能够基于您的详细情况和多维度偏好，进行深度的数据分析和匹配，给出更具体的院校专业建议和风险评估。\n\n若您【需要完整报告】，我将先与您核对并补充一些关键信息，然后为您生成报告；若您希望【直接回答问题】，我将尝试基于现有信息给您一些方向性的解答，但可能无法提供详尽的个性化方案。"
        writer(
            {
                "data": AiChatResultBO(
                    text=transition_text,
                    custom=CustomBO(message_type="text", message_id=str(sf.generate())),
                )
            }
        )

        # 发送选项按钮
        message_id = str(sf.generate())
        response = AiChatResultBO(
            custom=CustomBO(
                message_id=message_id,
                message_type="select_buttons",
                message_status=MessageStatusBO(
                    user_input={"send_button_disable": True}
                ),
                message_data={
                    "id": "free_chat_next_intent",
                    "message_id": message_id,  # 添加消息ID到message_data中，用于后续更新
                    "options": [
                        {"value": "need_report", "text": "需要完整报告"},
                        {"value": "direct_answer", "text": "直接回答问题"},
                    ],
                },
            ),
        )

        writer({"data": response})

        # 保存触发信息到上下文
        self._update_current_context(
            state,
            {
                "report_suggestion_triggered": True,
                "free_chat_next_intent_message_id": message_id,
                "trigger_question": user_input,
                "trigger_time": time.time(),
            },
        )

        # 返回到主入口等待用户选择
        return await self._return_to_mentor(state)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="FreeChatQACoordinator",
            update=await free_chat_qa_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="FreeChatQAMentor",
            update=await free_chat_qa_mentor_command_update(state),
        )
