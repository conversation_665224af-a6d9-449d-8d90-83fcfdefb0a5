from typing import Dict, Any

from langgraph.types import Command

from src.app.zhiyuan.mentors.common.base.base_normal_person_clarification import (
    BaseNormalUserPersonaClarification,
)
from src.app.zhiyuan.mentors.free_chat_qa.models.model import (
    free_chat_qa_mentor_command_update,
)


class UserPersonaClarification(BaseNormalUserPersonaClarification):
    """用户画像澄清模块 v2.0

    基于通用架构的画像澄清实现，使用统一的服务层。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "FreeChatUserPersonaClarification"

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="FreeChatQACoordinator",
            update=await free_chat_qa_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="FreeChatQAMentor",
            update=await free_chat_qa_mentor_command_update(state),
        )
