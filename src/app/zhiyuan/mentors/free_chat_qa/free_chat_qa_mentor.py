"""
自由对话问答主入口

基于用户已提供的基础信息，在自由对话中恰当时机触发【生成交付报告】的建议，
引导有深度需求的用户进入报告生成流程，实现商业化转化。
"""

import os
import time
from typing import Dict, Any, Optional

from langchain_core.output_parsers import PydanticOutputParser
from pydantic import Field, BaseModel
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from src.infrastructure.llm.llm_client import async_create_llm
from .config.flow_config import FREE_CHAT_QA_FLOW
from .config.prompts import FREE_CHAT_ANALYSIS_PROMPT
from .coordinator import FreeChatQACoordinator
from .models.model import free_chat_qa_mentor_command_update, FreeChatMentorState
from .modules.free_chat import FreeChat
from .modules.free_chat_experts_group import FreeChatExpertsGroup
from .modules.report_generation import ReportGeneration
from .modules.user_persona_clarification import UserPersonaClarification
from ..common.base.base_mentor import BaseMentor
from ..common.utils.biz_logic_util import user_info_to_str
from ...constant import AgentTypeEnum

logger = setup_logger(name=__name__, level="DEBUG")


class NeedReportResult(BaseModel):
    """用户意图。
    Attributes:
        need_report: 是否要生成报告
        reasoning: 判断理由
    """

    need_report: bool = Field(description="是否要生成报告")

    reasoning: str = Field(description="判断理由")


class FreeChatQAMentor(BaseMentor):
    """自由对话问答主入口

    基于通用导师架构，提供智能的自由对话问答服务，
    适时触发深度报告生成，实现商业化转化。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "FreeChatQAMentor"

    @property
    def _flow_config(self) -> Dict[str, Any]:
        """流程配置"""
        return FREE_CHAT_QA_FLOW

    async def _create_workflow_graph(self):
        """创建工作流图

        Args:
            checkpointer: 检查点保存器

        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        # 创建模块实例
        modules = {
            "FreeChatQACoordinator": FreeChatQACoordinator(),
            "FreeChat": FreeChat(),
            "UserPersonaClarification": UserPersonaClarification(),
            "FreeChatExpertsGroup": FreeChatExpertsGroup(),
            "ReportGeneration": ReportGeneration(),
        }

        # 创建基础工作流
        workflow = self._create_base_workflow(FreeChatMentorState, modules)

        # 设置入口点
        self._set_workflow_entry_point(workflow, "FreeChatQACoordinator")

        # 编译工作流
        return await self._compile_workflow(workflow)

    def _prepare_init_state(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """准备初始状态

        Args:
            state: 原始状态

        Returns:
            Dict[str, Any]: 处理后的初始状态
        """
        # 使用基础状态准备方法
        init_state = self._get_default_init_state(state)

        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(init_state)

        # 添加职业优先模式特定的初始化到 bizRuntimeBO.context
        biz_runtime = init_state["bizRuntimeBO"]
        context = biz_runtime.context
        context["flow_metadata"] = {
            "mode": "free_chat",
            "version": "2.0.0",
            "started_at": time.time(),
        }

        return init_state

    async def _return_to_orchestrator(self, state: Dict[str, Any]) -> Command:
        """返回到编排器

        Args:
            state: 当前状态

        Returns:
            Command: 返回编排器的命令
        """
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await free_chat_qa_mentor_command_update(state),
        )

    @staticmethod
    async def need_report(state: Dict[str, Any]) -> bool:
        """分析用户问题的性质 - 是否要生成报告

        Args:
            user_input: 用户输入
            state: 当前状态

        Returns:
            bool: 问题分析结果 - 是否要生成报告
        """

        # 1. 分析是否已进入用户画像澄清等环节
        biz_runtime = state["bizRuntimeBO"]
        context = (biz_runtime.context or {}).get("questionnaire_context", {})
        if (
            context.get("FreeChat", {}).get("current_status", "") == "completed"
            and not context.get("FreeChatReportGeneration", {}).get(
                "current_status", ""
            )
            == "completed"
        ):
            logger.info("[FreeChatQAMentor.need_report] 进入 FreeChat completed 且未生成报告分支，返回 True")
            return True

        # 2. 分析是否是用户回答
        logger.debug("[FreeChatQAMentor.need_report] 检查是否为用户回答分支")
        handle_user_answer_result = await FreeChatQAMentor.handle_user_answer(state)
        if handle_user_answer_result is not None:
            logger.info(f"[FreeChatQAMentor.need_report] handle_user_answer 返回: {handle_user_answer_result}")
            return handle_user_answer_result

        user_input = ""
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1]
            user_message = getattr(last_message, "content", str(last_message))
            user_input = user_message if isinstance(user_message, str) else ""
        logger.debug(f"[FreeChatQAMentor.need_report] user_input: {user_input}")

        try:
            # 收集用户基础信息
            user_info = state.get("userInfo")
            user_info_str = (
                user_info_to_str(user_info) if user_info else "暂无用户基础信息"
            )
            logger.debug(f"[FreeChatQAMentor.need_report] user_info_str: {user_info_str}")

            need_report_result = PydanticOutputParser(pydantic_object=NeedReportResult)

            # 使用LLM分析问题性质
            llm = await async_create_llm(
                **{
                    "model_name": os.getenv("QWEN_32_B"),
                    "api_key": os.getenv("ALIBABA_API_KEY"),
                    "api_base": os.getenv("ALIBABA_BASE_URL"),
                    "temperature": 0.1,  # 降低温度确保判断一致性
                }
            )
            logger.info("[FreeChatQAMentor.need_report] LLM 实例创建完成")

            prompts = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(
                        FREE_CHAT_ANALYSIS_PROMPT
                    ),
                ]
            )

            chain = prompts | llm | need_report_result
            logger.debug("[FreeChatQAMentor.need_report] LLM chain 构建完成")

            # 构建分析数据
            analysis_data = {
                "user_question": user_input,
                "user_info": user_info_str,
                "format_instructions": need_report_result.get_format_instructions(),
            }
            logger.info(f"[FreeChatQAMentor.need_report] 调用 LLM 分析数据: {analysis_data}")

            response = await chain.ainvoke(analysis_data)
            logger.info(f"[FreeChatQAMentor.need_report] LLM 返回结果: {response}")
            return response.need_report
        except Exception as e:
            logger.error(f"[FreeChatQAMentor.need_report] LLM 分析异常: {e}", exc_info=True)
            raise

    @staticmethod
    async def handle_user_answer(state: Dict[str, Any]) -> Optional[bool]:
        biz_runtime = state.get("bizRuntimeBO")
        answer = (
            biz_runtime.params.get("answer", {})
            if biz_runtime and hasattr(biz_runtime, "params")
            else None
        )
        selected_value = answer["value"] if answer else None

        if selected_value == "need_report":
            return True
        elif selected_value == "direct_answer":
            return False
        return None

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="FreeChatQACoordinator",
            update=await free_chat_qa_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="FreeChatQAMentor",
            update=await free_chat_qa_mentor_command_update(state),
        )
