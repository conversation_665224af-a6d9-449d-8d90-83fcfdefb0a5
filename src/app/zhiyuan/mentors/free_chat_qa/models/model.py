"""
自由对话问答流程的数据模型定义

基于通用架构，适配自由对话问答的业务场景。
"""

from typing import Dict, Any, Optional

from src.app.zhiyuan.mentors.common.models import BaseMentorState
from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.app.zhiyuan.models.model import UserInfoDTO


class FreeChatMentorState(BaseMentorState):
    """专业优先模式状态模型

    继承自统一的基础状态模型，添加专业优先模式特定的字段。
    """

    # 专业优先模式特定字段可以在这里添加
    # 目前使用基础状态模型已经足够
    userInfo: Optional[UserInfoDTO]
    messageId: Optional[str]
    conversationId: Optional[str]
    messages: Optional[list[dict]]
    action: Optional[str]
    guideGotoDemo: Optional[str]
    bizRuntimeBO: Optional[BizRuntimeBO]
    interrupt_feedback: Optional[bool]


# 自由对话问答流程的状态更新函数
async def free_chat_qa_mentor_command_update(state: Dict[str, Any]) -> Dict[str, Any]:
    """自由对话问答流程的命令更新函数

    Args:
        state: 当前状态

    Returns:
        Dict[str, Any]: 更新后的状态
    """

    return {
        "userInfo": state.get("userInfo"),
        "messageId": state.get("messageId"),
        "conversationId": state.get("conversationId"),
        "messages": state.get("messages"),
        "action": state.get("action"),
        "guideGotoDemo": state.get("guideGotoDemo"),
        "interrupt_feedback": state.get("interrupt_feedback"),
        "bizRuntimeBO": state.get("bizRuntimeBO"),
    }
