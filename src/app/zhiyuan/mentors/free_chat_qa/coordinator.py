"""
自由对话问答流程协调器

负责协调自由对话问答流程中各个模块的执行顺序和状态管理。
"""

from typing import Dict, Any
from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from ..common.base.base_coordinator import BaseCoordinator
from .config.flow_config import FREE_CHAT_QA_FLOW
from .models.model import free_chat_qa_mentor_command_update

logger = setup_logger(name=__name__, level="DEBUG")


class FreeChatQACoordinator(BaseCoordinator):
    """自由对话问答流程协调器

    基于通用协调器架构，管理自由对话问答的完整流程。
    """

    def __init__(self):
        """初始化协调器"""
        super().__init__(FREE_CHAT_QA_FLOW)

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "FreeChatQACoordinator"

    async def _get_command_update_function(self, state: Dict[str, Any]):
        """获取命令更新函数

        Args:
            state: 当前状态

        Returns:
            命令更新函数的结果
        """
        return await free_chat_qa_mentor_command_update(state)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="FreeChatQACoordinator",
            update=await free_chat_qa_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="FreeChatQAMentor",
            update=await free_chat_qa_mentor_command_update(state),
        )
