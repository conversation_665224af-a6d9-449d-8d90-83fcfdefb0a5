from enum import Enum


class ModuleStatusEnum(Enum):
    """统一的模块状态枚举"""

    # 问卷相关状态
    TRANSITION_SENT = "transition_sent"  # 转场文案已发送
    QUESTIONNAIRE_INIT = "questionnaire_init"
    QUESTIONNAIRE_SENT = "questionnaire_sent"
    QUESTIONNAIRE_ANSWERED = "questionnaire_answered"

    # 澄清相关状态
    CLARIFICATION_INIT = "clarification_init"
    CLARIFICATION_SENT = "clarification_sent"
    CLARIFICATION_RECEIVED = "clarification_received"
    AWAITING_FURTHER_CLARIFICATION = "awaiting_further_clarification"

    # 报告相关状态
    REPORT_INIT_RESPONSE_SEND = "report_init_response_send"
    REPORT_COST_PAYED = "report_cost_payed"
    REPORT_GENERATING = "report_generating"
    REPORT_GENERATED = "report_generated"

    # 通用状态
    COMPLETED = "completed"
    ERROR = "error"
    SKIPPED = "skipped"


class QuestionnaireTypeEnum(Enum):
    """问卷类型枚举"""

    BIPOLAR = "bipolar"  # 双极问卷
    LIKERT_TABLE = "likert_table"  # 李克特量表
    MULTIPLE_CHOICE = "multiple_choice"  # 多选题
    TEXT_INPUT = "text_input"  # 文本输入
