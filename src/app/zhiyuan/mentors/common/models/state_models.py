from typing import Optional, Dict, Any, List
from langgraph.graph.message import MessagesState
from pydantic import BaseModel

from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.app.zhiyuan.models.model import UserInfoDTO


class BaseMentorState(MessagesState):
    """统一的基础状态模型

    所有志愿填报模式的状态都应该继承此基类，确保状态结构的一致性。
    """

    # 基础信息
    userInfo: Optional[UserInfoDTO] = None
    messageId: Optional[str] = None
    conversationId: Optional[str] = None
    messages: Optional[List[Dict[str, Any]]] = None

    # 业务信息
    action: Optional[str] = None
    guideGotoDemo: Optional[bool] = False
    bizRuntimeBO: Optional[BizRuntimeBO] = None
    interrupt_feedback: Optional[bool] = False

    # 流程控制
    current_step: Optional[int] = 0
    questionnaire_context: Optional[Dict[str, Any]] = None

    # 扩展字段
    pending_report_task: Optional[Dict[str, Any]] = None
    user_features: Optional[Dict[str, Any]] = None
    flow_metadata: Optional[Dict[str, Any]] = None


class FlowConfig(BaseModel):
    """流程配置模型"""

    sequence: List[str]  # 模块执行序列
    module_configs: Dict[str, Dict[str, Any]] = {}  # 模块配置
    flow_metadata: Dict[str, Any] = {}  # 流程元数据

    class Config:
        arbitrary_types_allowed = True


class ModuleConfig(BaseModel):
    """模块配置模型"""

    questionnaire_id: Optional[str] = None
    message_type: Optional[str] = None
    auto_advance: bool = True
    timeout_seconds: Optional[int] = None
    retry_count: int = 3

    class Config:
        arbitrary_types_allowed = True


class ProgressInfo(BaseModel):
    """进度信息模型"""

    current_step: int
    total_steps: int
    progress_percentage: int
    current_module: str
    completed_modules: List[str]
    remaining_modules: List[str]
    flow_metadata: Dict[str, Any] = {}

    class Config:
        arbitrary_types_allowed = True
