from typing import Dict, Any, List, Optional, TypedDict
from pydantic import BaseModel


class QuestionnaireScaleLabel(TypedDict):
    """问卷量表标签"""

    value: int
    text: str


class QuestionnairePole(BaseModel):
    """问卷极点模型"""

    title: str  # 极点标题
    examples: Optional[str] = None  # 示例说明

    class Config:
        arbitrary_types_allowed = True


class QuestionnaireQuestion(BaseModel):
    """问卷问题模型"""

    id: str  # 问题ID
    title: str  # 问题标题
    description: Optional[str] = None  # 问题描述
    type: str = "bipolar"  # 问题类型
    required: bool = True  # 是否必答

    # 双极问卷特有字段
    poles: Optional[Dict[str, QuestionnairePole]] = None

    # 李克特量表特有字段
    options: Optional[List[Dict[str, Any]]] = None
    scale_min: Optional[int] = None
    scale_max: Optional[int] = None

    # 多选题特有字段
    choices: Optional[List[Dict[str, Any]]] = None
    max_selections: Optional[int] = None

    class Config:
        arbitrary_types_allowed = True


class QuestionnaireData(BaseModel):
    """问卷数据模型"""

    id: str  # 问卷ID
    title: str  # 问卷标题
    description: Optional[str] = None  # 问卷描述
    questions: List[QuestionnaireQuestion]  # 问题列表

    class Config:
        arbitrary_types_allowed = True


class QuestionnaireQuestionDetail(BaseModel):
    """问卷问题详情"""

    id: str
    title: str
    description: str
    scale: "QuestionnaireScale"
    questions: List[QuestionnaireQuestion]


class QuestionnaireAnswer(BaseModel):
    """问卷答案模型"""

    question_id: str  # 问题ID
    value: Any  # 答案值
    text: Optional[str] = None  # 文本答案（如果有）
    timestamp: Optional[float] = None  # 答题时间戳

    class Config:
        arbitrary_types_allowed = True


class QuestionnaireScale(BaseModel):
    """问卷量表"""

    type: str
    displayType: str
    points: int
    labels: List[QuestionnaireScaleLabel]


class QuestionnaireContext(BaseModel):
    """问卷上下文模型"""

    status: Optional[str] = None  # 当前状态
    questionnaire: Optional[QuestionnaireData] = None  # 问卷数据
    answers: Optional[List[QuestionnaireAnswer]] = None  # 答案列表
    questionnaire_message_id: Optional[str] = None  # 问卷消息ID
    created_at: Optional[float] = None  # 创建时间
    updated_at: Optional[float] = None  # 更新时间

    class Config:
        arbitrary_types_allowed = True


class BipolarQuestionData(BaseModel):
    """双极问卷数据模型"""

    id: str
    title: str
    questions: List[QuestionnaireQuestion]

    class Config:
        arbitrary_types_allowed = True


class LikertTableData(BaseModel):
    """李克特量表数据模型"""

    id: str
    title: str
    description: Optional[str] = None
    majors: List[Dict[str, Any]]  # 专业列表
    evaluation_dimensions: List[str]  # 评估维度

    class Config:
        arbitrary_types_allowed = True
