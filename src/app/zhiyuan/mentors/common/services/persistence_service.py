import time
from typing import Dict, Any, Optional
from lucas_common_components.logging import setup_logger

from src.exe.command.AIMessageCommandExe import AIMessageCommandExe
from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository

logger = setup_logger(name=__name__, level="DEBUG")


class PersistenceService:
    """统一的数据持久化服务

    提供问卷答案保存、用户特征保存、历史数据获取等功能。
    """

    async def save_questionnaire_answer(
        self,
        conversation_id: str,
        module_name: str,
        answer: Any,
        questionnaire_message_id: str = None,
        user_answer_content: str = None,
    ):
        """保存问卷答案

        Args:
            conversation_id: 会话ID
            module_name: 模块名称
            answer: 问卷答案
            questionnaire_message_id: 问卷消息ID
            user_answer_content: 用户答案内容
        """
        try:
            # 取出消息上的问卷信息，根据id把answer上对应的value填充进去
            questionnaire_ai_message = await AIMessageRepository.get_by_id(
                int(questionnaire_message_id)
            )
            if not questionnaire_ai_message:
                logger.error(
                    f"[{module_name}] 未找到问卷消息，ID: {questionnaire_message_id}"
                )
                return

            message_data = questionnaire_ai_message.message_data
            answer_map = {item["id"]: item["value"] for item in answer}
            questions = message_data["question"]["questions"]
            for question in questions:
                answer_value = answer_map.get(question["id"])
                question.update({"value": answer_value})

            # 保存问卷答案（包含完整的问卷信息和答案）
            message_data["answers"] = ({"id": module_name, "answer": answer},)
            message_status = questionnaire_ai_message.message_status or {}
            message_status.update({"disable": True})

            await AIMessageCommandExe.update(
                int(questionnaire_message_id),
                {"message_data": message_data, "message_status": message_status}
            )

            logger.info(f"[{module_name}] 问卷答案持久化成功，答案数量: {len(answer)}")

            # 保存用户画像
            user_features = [
                {"feature_name": item["id"], "feature_value": item["value"]}
                for item in answer
            ]

            ai_conversation = await AIConversationRepository.get_by_id(
                int(conversation_id)
            )
            if ai_conversation:
                exist_user_features = ai_conversation.user_features or {}
                exist_user_features.update(
                    {
                        module_name: user_features,
                        f"{module_name}_raw_answer": user_answer_content,
                    }
                )
                await AIConversationRepository.update(
                    int(conversation_id), {"user_features": exist_user_features}
                )
        except Exception as e:
            logger.error(f"[{module_name}] 持久化问卷答案失败: {str(e)}", exc_info=True)

    async def save_user_features(
        self,
        conversation_id: str,
        features: Dict[str, Any],
        module_name: Optional[str] = None,
    ):
        """保存用户特征

        Args:
            conversation_id: 会话ID
            features: 用户特征数据
            module_name: 模块名称（可选）
        """
        try:
            # 获取会话
            ai_conversation = await AIConversationRepository.get_by_id(
                int(conversation_id)
            )
            if not ai_conversation:
                logger.warning(f"未找到会话ID: {conversation_id}")
                return

            # 获取现有的用户特征 - 从数据库或其他存储中获取
            # 注意：这里需要根据实际的数据库表结构来调整
            existing_features = {}
            # 如果 AIConversation 有 user_features 字段
            if (
                hasattr(ai_conversation, "user_features")
                and ai_conversation.user_features
            ):
                existing_features = ai_conversation.user_features
            # 否则可以从其他地方获取或使用空字典

            if module_name:
                # 按模块保存
                if module_name not in existing_features:
                    existing_features[module_name] = {}
                existing_features[module_name].update(features)
            else:
                # 直接合并
                existing_features.update(features)

            # 添加时间戳
            existing_features["updated_at"] = time.time()

            # 保存到数据库
            await AIConversationRepository.update(
                int(conversation_id), {"user_features": existing_features}
            )

            logger.info(
                f"用户特征已保存 - 会话: {conversation_id}, 模块: {module_name}"
            )

        except Exception as e:
            logger.error(f"保存用户特征失败: {str(e)}")
            raise
