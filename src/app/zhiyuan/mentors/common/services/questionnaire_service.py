from typing import Dict, Any, List, Optional
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO
from src.utils.snow_flake import Snowflake

logger = setup_logger(name=__name__, level="DEBUG")
sf = Snowflake(worker_id=0, datacenter_id=0)


class QuestionnaireService:
    """统一的问卷处理服务

    提供问卷创建、答案解析、验证等功能。
    """

    def create_bipolar_questionnaire_with_vo(
        self,
        questionnaire_detail,  # QuestionnaireQuestionDetail 对象
    ) -> AiChatResultBO:
        """使用VO对象创建双极问卷

        Args:
            questionnaire_detail: QuestionnaireQuestionDetail 对象

        Returns:
            AiChatResultBO: 问卷响应对象
        """
        try:
            return AiChatResultBO(
                text="",
                custom=CustomBO(
                    message_id=sf.generate(),
                    message_type="bipolar",
                    message_status=MessageStatusBO(
                        user_input={"send_button_disable": True}
                    ),
                    message_data={"question": questionnaire_detail.dict()},
                ),
            )

        except Exception as e:
            logger.error(f"创建双极问卷失败: {str(e)}")
            raise

    def parse_questionnaire_answer(self, biz_runtime) -> Optional[List[Dict[str, Any]]]:
        """解析问卷答案

        Args:
            biz_runtime: 业务运行时对象

        Returns:
            Optional[List[Dict[str, Any]]]: 解析后的答案列表
        """
        try:
            if (
                not biz_runtime
                or not hasattr(biz_runtime, "params")
                or not biz_runtime.params
            ):
                logger.debug("问卷答案解析失败: 无 biz_runtime 或 params")
                return None

            # 记录原始参数用于调试
            logger.debug(f"问卷答案解析 - 原始 params: {biz_runtime.params}")

            # 尝试从不同的路径获取答案（按照老版本的格式）
            answer_paths = [
                ["questionnaire_question", "answer"],  # 老版本的主要路径
                ["question", "answer"],  # 备用路径1
                ["answer"],  # 备用路径2
            ]

            for path in answer_paths:
                current = biz_runtime.params
                logger.debug(f"尝试路径: {' -> '.join(path)}")

                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                        logger.debug(f"找到键 '{key}', 值类型: {type(current)}")
                    else:
                        logger.debug(f"未找到键 '{key}' 或当前值不是字典")
                        current = None
                        break

                if current is not None:
                    logger.info(
                        f"成功解析问卷答案，路径: {' -> '.join(path)}, 答案: {current}"
                    )
                    # 确保返回列表格式
                    if isinstance(current, list):
                        return current
                    elif isinstance(current, dict):
                        return [current]
                    else:
                        logger.warning(
                            f"未知的答案格式: {type(current)}, 值: {current}"
                        )
                        return None

            logger.warning(f"未找到问卷答案，已尝试路径: {answer_paths}")
            return None

        except Exception as e:
            logger.error(f"解析问卷答案失败: {str(e)}")
            return None

    def validate_questionnaire_answer(self, answer: List[Dict[str, Any]]) -> bool:
        """验证问卷答案

        Args:
            answer: 问卷答案

        Returns:
            bool: 验证结果
        """
        try:
            if not answer or not isinstance(answer, list):
                return False

            for item in answer:
                if not isinstance(item, dict):
                    return False

                # 检查必需字段
                if "id" not in item:
                    logger.warning("答案缺少 id 字段")
                    return False

                if "value" not in item:
                    logger.warning("答案缺少 value 字段")
                    return False

            return True

        except Exception as e:
            logger.error(f"验证问卷答案失败: {str(e)}")
            return False
