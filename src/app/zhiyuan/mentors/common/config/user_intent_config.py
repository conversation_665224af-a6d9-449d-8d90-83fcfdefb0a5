"""
用户意图识别配置
"""

from typing import Dict, List
from enum import Enum


class UserIntentEnum(Enum):
    """用户意图枚举"""

    GENERATE_NEXT_REPORT = "generate_next_report"
    ASK_ABOUT_REPORT = "ask_about_report"
    GENERAL_QUESTION = "general_question"


class UserIntentConfig:
    """用户意图识别配置类"""

    # 意图关键词配置
    INTENT_KEYWORDS = {
        UserIntentEnum.GENERATE_NEXT_REPORT.value: [
            "生成下一个报告",
            "继续生成",
            "下一步",
            "开始生成",
            "生成报告",
            "我要看报告",
            "给我报告",
            "继续",
            "下一个",
            "开始分析",
            "学业画像",
            "未来趋势",
            "专业匹配",
            "填报方案",
            "职业规划",
        ],
        UserIntentEnum.ASK_ABOUT_REPORT.value: [
            "这个报告",
            "刚才的分析",
            "上面的内容",
            "报告中",
            "分析结果",
            "为什么",
            "怎么理解",
            "什么意思",
            "能详细说说",
            "如何理解",
            "画像",
            "趋势",
            "匹配",
            "方案",
            "规划",
        ],
        UserIntentEnum.GENERAL_QUESTION.value: [
            "志愿填报",
            "专业选择",
            "院校",
            "分数线",
            "录取",
            "高考",
            "大学",
            "职业",
            "就业",
            "前景",
            "发展",
        ],
    }

    @classmethod
    def get_intent_keywords(cls) -> Dict[str, List[str]]:
        """获取意图关键词配置"""
        return cls.INTENT_KEYWORDS

    @classmethod
    def get_keywords_by_intent(cls, intent: UserIntentEnum) -> List[str]:
        """根据意图获取关键词列表"""
        return cls.INTENT_KEYWORDS.get(intent.value, [])

    @classmethod
    def parse_intent_by_keywords(cls, user_message: str) -> UserIntentEnum:
        """通过关键词匹配解析用户意图"""
        user_message_lower = user_message.lower()

        # 按优先级检查意图
        for intent_key, keywords in cls.INTENT_KEYWORDS.items():
            for keyword in keywords:
                if keyword in user_message_lower:
                    return UserIntentEnum(intent_key)

        # 默认返回一般性问题
        return UserIntentEnum.GENERAL_QUESTION
