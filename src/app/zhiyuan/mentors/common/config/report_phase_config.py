"""
报告生成阶段配置
"""


class ReportGenerationPhase:
    """报告生成阶段常量"""

    FIRST_STEP = "first_step"
    FREE_CHAT = "free_chat"  # 自由对话阶段
    GENERATING_STEP_IN = "generating_step_in"  # 进入生成报告阶段（转场文案）
    GENERATING = "generating"  # 开始生成报告阶段
    COMPLETED = "completed"  # 全部完成阶段


class ReportMessageConfig:
    """报告相关消息配置"""

    # 积分消费配置
    CREDIT_COST = 299

    # 初始化响应文案
    INIT_RESPONSE = """\n🎓 我是您的专属志愿填报顾问！我将为您生成五份完整的分析报告：\n
📊 《一篇充满期待与力量的开篇信》- 全面分析您的学业表现和潜力\n
🔮 《未来趋势与潜力方向》- 识别最有前景的发展方向\n
🎯 《专业与院校匹配》- 精准匹配适合的专业院校\n
📋 《志愿填报方案》- 制定详细的填报策略\n
🚀 《大学成长与职业发展规划》- 规划长远发展路径\n
我们将逐一为您生成\n"""

    # 确认按钮配置
    CONFIRM_BUTTON_CONFIG = {
        "id": "confirm_report_credits",
        "options": [{"value": "confirm", "text": "确认生成报告"}],
    }

    @classmethod
    def get_credit_confirm_message(cls) -> str:
        """获取积分确认消息"""
        return f"\n\n解锁报告需消耗 {cls.CREDIT_COST} 积分, 是否确认生成？"

    @classmethod
    def get_credit_insufficient_message(cls, credit_gap: int) -> str:
        """获取积分不足消息"""
        return f"您的积分不足 (还需 {credit_gap} 积分)，无法生成报告。请先充值。"

    @classmethod
    def get_task_start_message(cls, task_name: str, order: int) -> str:
        """获取任务开始消息"""
        return f"正在为您生成第 {order} 份报告《{task_name}》，这个过程可能需要一些时间进行数据匹配和分析，请稍等片刻..."

    @classmethod
    def get_all_completed_message(cls) -> str:
        """获取全部完成消息"""
        return "您的所有分析报告都已完成！"

    @classmethod
    def get_next_report_prompt(cls, task_name: str) -> str:
        """获取下一个报告提示消息"""
        return f"是否需要我为你生成下一份报告：《{task_name}》。或者您可以告诉我您对现有报告有什么疑问？"
