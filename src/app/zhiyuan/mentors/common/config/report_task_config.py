"""
报告任务配置
"""

from typing import List, Dict, Any


class ReportTaskConfig:
    """报告任务配置类"""

    # 预设报告任务配置
    PRESET_TASKS = [
        {
            "key": "student_profile",
            "name": "一篇充满期待与力量的开篇信",
            "description": "基于学业成绩、选科组合、兴趣特长等，构建全面的一篇充满期待与力量的开篇信",
            "order": 1,
            "dependencies": [],
            "icon": "📊",
            "sections": [
                "学业表现分析",
                "学科优势识别",
                "学习能力评估",
                "综合素质画像",
            ],
        },
        {
            "key": "future_trends",
            "name": "未来趋势与潜力方向",
            "description": "分析行业发展趋势，识别学生的潜力发展方向和未来机会",
            "order": 2,
            "dependencies": ["student_profile"],
            "icon": "🔮",
            "sections": [
                "行业发展趋势",
                "新兴领域机会",
                "个人潜力匹配",
                "未来发展路径",
            ],
        },
        {
            "key": "major_school_matching",
            "name": "专业与院校匹配",
            "description": "基于学生画像和未来趋势，精准匹配最适合的专业和院校",
            "order": 3,
            "dependencies": ["student_profile", "future_trends"],
            "icon": "🎯",
            "sections": [
                "专业适配度分析",
                "院校层次匹配",
                "地域偏好考虑",
                "录取概率评估",
            ],
        },
        {
            "key": "application_strategy",
            "name": "志愿填报方案",
            "description": "制定详细的志愿填报策略，包括冲刺、稳妥、保底的完整方案",
            "order": 4,
            "dependencies": [
                "student_profile",
                "future_trends",
                "major_school_matching",
            ],
            "icon": "📋",
            "sections": [
                "填报策略设计",
                "志愿梯度配置",
                "风险评估与规避",
                "备选方案制定",
            ],
        },
        {
            "key": "development_planning",
            "name": "大学成长与职业发展规划",
            "description": "规划大学四年的成长路径和长远的职业发展方向",
            "order": 5,
            "dependencies": [
                "student_profile",
                "future_trends",
                "major_school_matching",
                "application_strategy",
            ],
            "icon": "🚀",
            "sections": [
                "大学学习规划",
                "能力提升路径",
                "实习就业指导",
                "长期职业规划",
            ],
        },
    ]

    @classmethod
    def get_preset_tasks(cls) -> List[Dict[str, Any]]:
        """获取预设任务配置"""
        return cls.PRESET_TASKS

    @classmethod
    def get_task_by_key(cls, task_key: str) -> Dict[str, Any]:
        """根据任务key获取任务配置"""
        for task in cls.PRESET_TASKS:
            if task["key"] == task_key:
                return task
        return {}

    @classmethod
    def get_next_available_task(cls, completed_keys: List[str]) -> Dict[str, Any]:
        """获取下一个可执行的任务"""
        for task in cls.PRESET_TASKS:
            # 检查任务是否已完成
            if task["key"] in completed_keys:
                continue

            # 检查依赖是否满足
            dependencies_met = all(
                dep in completed_keys for dep in task["dependencies"]
            )
            if dependencies_met:
                return task

        return {}  # 所有任务都已完成
