import os
import uuid
from abc import ABC
from typing import Dict, Any
from typing import Optional, List

from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger
from lucas_common_components.trace import trace_context

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO
from src.app.zhiyuan.models.model import (
    UserInfoDTO,
    ConsumeCreditResultDTO,
    ConsumeResultStatsuEnum,
)
from src.exe.command.AIMessageCommandExe import AIMessageCommandExe
from src.infrastructure.db.crud.ai.ReportTaskRepository import ReportTaskRepository
from src.infrastructure.db.models.ai.ReportTask import ReportTaskStatus
from src.infrastructure.external.sae_job_client import submit_sae_job
from src.infrastructure.llm.llm_client import async_create_llm
from src.infrastructure.rpc.account_client import AccountClient
from src.utils.snow_flake import Snowflake
from ..models import ModuleStatusEnum
from ...common.base.base_module import BaseMentorModule
from ..config.report_task_config import ReportTaskConfig
from ..config.user_intent_config import UserIntentConfig
from ..config.report_phase_config import ReportMessageConfig

logger = setup_logger(name=__name__, level="DEBUG")
sf = Snowflake(worker_id=0, datacenter_id=0)


class BaseReportGeneration(BaseMentorModule, ABC):
    """报告生成基类"""

    @property
    def _module_name(self):
        return self.__class__.__name__

    def __init__(self):
        """初始化报告生成代理"""
        self.account_client = AccountClient()

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """报告生成节点主入口"""
        try:
            conversation_id = state["conversationId"]
            logger.info(f"[{self._module_name}] 开始处理会话: {conversation_id}")

            current_status = self._get_current_context_status(state)
            # 获取当前状态
            if current_status is None:
                return await self._handle_init_status(state, writer)
            if current_status == ModuleStatusEnum.REPORT_INIT_RESPONSE_SEND.value:
                # 开场文案已发送，处理积分
                return await self._handle_credits(state, writer)
            elif current_status == ModuleStatusEnum.REPORT_COST_PAYED.value:
                # 积分已扣减，直接生成第一个报告
                return await self._start_report_generation(state, writer)
            elif current_status == ModuleStatusEnum.REPORT_GENERATING.value:
                # 正在生成中状态
                return await self._handle_report_generating(state, writer)
            elif current_status == ModuleStatusEnum.REPORT_GENERATED.value:
                # 报告生成完成，进入自由对话
                return await self._handle_free_chat_phase(state, writer)

        except Exception as e:
            logger.error(f"[{self._module_name}] 处理异常: {str(e)}", exc_info=True)
            writer(
                {"data": AiChatResultBO(text="抱歉，系统遇到了一些问题，请稍后再试。")}
            )

        # 默认留在当前代理
        return await self._return_to_mentor(state)

    async def _get_next_task(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取下一个要生成的任务"""
        try:
            # 使用数据库查询
            completed_tasks = (
                await ReportTaskRepository.get_completed_tasks_by_conversation(
                    int(conversation_id)
                )
            )
            completed_keys = [task.task_key for task in completed_tasks]

            # 使用工具函数获取下一个任务key
            next_task_key = self.get_next_task_key(completed_keys)

            if next_task_key:
                # 返回任务配置
                return self.get_task_config(next_task_key)
            else:
                return None

        except Exception as e:
            logger.error(f"获取下一个任务失败: {str(e)}", exc_info=True)
            return None

    def get_task_config(self, task_key: str) -> Optional[Dict[str, Any]]:
        """获取任务配置"""
        return ReportTaskConfig.get_task_by_key(task_key) or None

    def get_next_task_key(self, completed_tasks: List[str]) -> Optional[str]:
        """获取下一个应该执行的任务key"""
        next_task = ReportTaskConfig.get_next_available_task(completed_tasks)
        return next_task.get("key") if next_task else None

    async def _start_report_generation(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        """开始报告生成"""
        try:
            conversation_id = str(state["conversationId"])
            # 获取下一个要生成的任务
            next_task = await self._get_next_task(conversation_id)
            if not next_task:
                writer(
                    {
                        "data": AiChatResultBO(
                            text=ReportMessageConfig.get_all_completed_message(),
                            custom=CustomBO(
                                message_id=sf.generate(),
                                message_type="text"
                            ),
                        )
                    }
                )
                self._mark_completed_and_advance(state)
                return await self._return_to_coordinator(state)

            # 发送单个报告开场文案
            response_text = self._generate_task_start_message(next_task)
            writer(
                {
                    "data": AiChatResultBO(
                        text=response_text,
                        custom=CustomBO(message_id=sf.generate(), message_type="text"),
                    )
                }
            )



            # 生成任务ID
            task_id = (
                f"report_{conversation_id}_{next_task['key']}_{uuid.uuid4().hex[:8]}"
            )

            prepare_ai_message_id = sf.generate()
            custom_bo = CustomBO(
                message_id=prepare_ai_message_id,
                message_type="report",
                message_status=MessageStatusBO(
                    user_input={"send_button_disable": True},
                    complete=False,
                    progress=0,
                    next_interval=5,
                ),
                message_data={"task_id": task_id, "task_name": next_task["name"]},
            )
            await AIMessageCommandExe.save_ai_message_by_ai_custom(
                conversation_id=conversation_id,
                prepare_ai_message_id=prepare_ai_message_id,
                custom=custom_bo,
            )
            # 创建数据库任务
            await self._create_database_task(
                prepare_ai_message_id, task_id, conversation_id, next_task, state
            )

            # 生成响应
            response = AiChatResultBO(text="", custom=custom_bo)

            writer({"data": response})
            self._update_context_status(state, ModuleStatusEnum.REPORT_GENERATING.value)
            logger.info(f"[{self._module_name}] 任务启动成功: {task_id}")
            return await self._return_to_mentor(state)

        except Exception as e:
            logger.error(f"开始报告生成失败: {str(e)}", exc_info=True)
            writer({"data": AiChatResultBO(text="抱歉，启动报告生成时遇到了问题。")})
            return await self._return_to_mentor(state)

    async def _create_database_task(
        self,
        prepare_ai_message_id,
        task_id: str,
        conversation_id: str,
        task_config: Dict[str, Any],
        state: Dict[str, Any],
    ):
        """创建数据库任务"""
        try:
            user_info = state.get("userInfo")
            task_data = {
                "id": int(sf.generate()),
                "task_id": task_id,
                "conversation_id": int(conversation_id),
                "user_id": user_info.id if user_info and user_info.id else 0,
                "task_name": task_config["name"],
                "task_key": task_config["key"],
                "status": ReportTaskStatus.CREATED,
                "progress": 0,
                "extra_data": {
                    "order": task_config["order"],
                    "message_id": prepare_ai_message_id,
                    "action": state["action"],
                },
            }

            # 保存到数据库
            await ReportTaskRepository.create(task_data)

            # 调用SAE JOB服务
            sae_task_data = {
                "task_id": task_id,
                "task_key": task_config["key"],
                "conversation_id": int(conversation_id),
                "user_id": user_info.id if (user_info and user_info.id) else 0,
                "message_id": prepare_ai_message_id,
                "trace_id": trace_context.get_trace_id(),
            }
            await ReportTaskRepository.update_status(
                task_id, ReportTaskStatus.PROCESSING
            )
            sae_result = await submit_sae_job(sae_task_data)

            if sae_result and sae_result.get("success"):
                # 更新本地任务状态和SAE JOB任务ID
                await ReportTaskRepository.update_status(
                    task_id, ReportTaskStatus.PROCESSING
                )
            else:
                error_msg = (
                    sae_result.get("error", "SAE JOB任务创建失败")
                    if sae_result
                    else "SAE JOB服务无响应"
                )
                await ReportTaskRepository.mark_failed(task_id, error_msg)
                logger.error(f"SAE JOB任务创建失败: {task_id} - {error_msg}")

        except Exception as e:
            logger.error(f"创建数据库任务失败: {str(e)}", exc_info=True)

    async def _handle_report_generating(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        # 检查是否与生产中的任务
        # 未完成 - 回复任务生成中
        conversation_id = state["conversationId"]
        all_tasks = await ReportTaskRepository.get_by_conversation_id(
            int(conversation_id)
        )
        processing_tasks = [task for task in all_tasks if task.is_processing]
        if processing_tasks:
            task = processing_tasks[0]
            writer(
                {
                    "data": AiChatResultBO(
                        text=f"您的{task.task_name}正在生成中，请稍等片刻。",
                        custom=CustomBO(message_id=sf.generate(), message_type="text"),
                    )
                }
            )
            return await self._return_to_mentor(state)
        else:
            last_complete_task = max(
                (task for task in all_tasks if task.is_completed),
                key=lambda task: task.extra_data["order"],
                default=None,  # 如果没有已完成任务，返回 None
            )
            if last_complete_task:
                # 已完成 - 回复任务已完成，更新状态到 REPORT_GENERATED
                # 1. 生成转场文案
                complete_task_order = int(last_complete_task.extra_data["order"])
                task_configs = self._preset_report_tasks()
                response_content = f"第 {complete_task_order} 份报告《{task_configs[complete_task_order - 1]['name']}》已生成完毕！ 您可以点击报告，查看内容。"
                if complete_task_order < len(task_configs):
                    response_content += f"接下来将要生成第 {complete_task_order + 1} 份报告《{task_configs[complete_task_order]['name']}》，请回复 “继续” 开始生成..."
                data_vo = AiChatResultBO(
                    text=response_content,
                    custom=CustomBO(message_id=sf.generate(), message_type="text"),
                )
                writer({"data": data_vo})
                self._update_context_status(
                    state, ModuleStatusEnum.REPORT_GENERATED.value
                )
                return await self._return_to_mentor(state)
            return await self._return_to_coordinator(state)

    async def _handle_free_chat_phase(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        """处理自由对话阶段"""
        try:
            user_message = ""
            messages = state.get("messages")
            if messages and len(messages) > 0:
                last_message = messages[-1]
                user_message = getattr(last_message, "content", str(last_message))

            # 解析用户意图
            intent = await self._parse_user_intent(user_message)
            logger.info(f"[{self._module_name}] 用户意图: {intent}")

            if intent == "generate_next_report":
                # 用户要生成下一个报告
                return await self._start_report_generation(state, writer)
            elif intent == "ask_about_report":
                # 用户询问报告相关问题
                return await self._answer_report_question(state, writer)
            else:
                # 一般性问题，引导用户
                return await self._handle_general_question(state, writer)

        except Exception as e:
            logger.error(f"处理自由对话阶段失败: {str(e)}", exc_info=True)
            writer(
                {"data": AiChatResultBO(text="抱歉，我遇到了一些问题，请稍后再试。")}
            )
            return await self._return_to_mentor(state)

    async def _parse_user_intent(self, user_message: str) -> str:
        """使用LLM解析用户意图"""
        try:
            # 初始化LLM
            llm = await async_create_llm(
                **{
                    "model_name": os.getenv("QWEN_32_B"),
                    "api_key": os.getenv("ALIBABA_API_KEY"),
                    "api_base": os.getenv("ALIBABA_BASE_URL"),
                    "temperature": 0.2,
                }
            )

            # 构建提示词
            prompt = """
            # 任务
            分析用户消息，判断用户意图属于以下哪一类：

            1. generate_next_report: 用户想要生成下一个报告或继续生成报告
            2. ask_about_report: 用户在询问已生成报告的内容或解释
            3. general_question: 用户在问一般性的志愿填报相关问题

            # 用户消息
            {user_message}

            # 输出格式
            仅输出一个意图类别名称，不要有任何其他内容：generate_next_report、ask_about_report或general_question
            """

            from langchain_core.prompts import ChatPromptTemplate

            prompts = ChatPromptTemplate.from_template(prompt)
            chain = prompts | llm

            # 调用LLM
            response = await chain.ainvoke({"user_message": user_message})
            intent = response.content.strip().lower()

            # 验证返回的意图是否有效
            valid_intents = [
                "generate_next_report",
                "ask_about_report",
                "general_question",
            ]
            if intent in valid_intents:
                logger.info(f"[{self._module_name}] LLM识别的用户意图: {intent}")
                return intent
            else:
                logger.warning(
                    f"[{self._module_name}] LLM返回了无效意图: {intent}，使用默认意图"
                )
                return "general_question"

        except Exception as e:
            logger.error(f"LLM解析用户意图失败: {str(e)}", exc_info=True)
            # 降级到关键词匹配
            return self._parse_user_intent_by_keywords(user_message)

    def _parse_user_intent_by_keywords(self, user_message: str) -> str:
        """通过关键词匹配解析用户意图（作为备选方案）"""
        intent = UserIntentConfig.parse_intent_by_keywords(user_message)
        return intent.value

    async def _answer_report_question(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        """回答报告相关问题"""
        try:
            user_message = ""
            messages = state.get("messages")
            if messages and len(messages) > 0:
                last_message = messages[-1]
                user_message = getattr(last_message, "content", str(last_message))

            conversation_id = str(state["conversationId"])

            # 获取相关的报告
            completed_tasks = (
                await ReportTaskRepository.get_completed_tasks_by_conversation(
                    int(conversation_id)
                )
            )

            if not completed_tasks:
                writer(
                    {
                        "data": AiChatResultBO(
                            text="您还没有生成任何报告。请先告诉我您想要生成哪种类型的分析报告？",
                            custom=CustomBO(
                                message_id=sf.generate(), message_type="text"
                            ),
                        )
                    }
                )
                return

            # 构建报告上下文
            report_context = self._build_report_context(completed_tasks)

            # 调用 LLM 回答问题
            answer = await self._call_llm_for_report_qa(
                user_message, report_context, state
            )

            writer(
                {
                    "data": AiChatResultBO(
                        text=answer,
                        custom=CustomBO(message_id=sf.generate(), message_type="text"),
                    )
                }
            )

        except Exception as e:
            logger.error(f"回答报告问题失败: {str(e)}", exc_info=True)
            writer(
                {"data": AiChatResultBO(text="抱歉，我无法回答您的问题，请稍后再试。")}
            )

    async def _call_llm_for_report_qa(
        self, user_question: str, report_context: str, state: Dict[str, Any]
    ) -> str:
        """调用 LLM 回答报告相关问题"""
        try:
            # 构建用户信息
            user_info = state.get("userInfo")
            user_context = ""
            if user_info:
                user_context = f"用户信息：{user_info.name if hasattr(user_info, 'name') else '用户'}"

            # 构建 LLM 提示词
            prompt = f"""# 角色
    你是一个专业的高考志愿填报顾问，正在为用户解答关于其个人分析报告的问题。

    # 用户信息
    {user_context}

    # 用户的分析报告内容
    {report_context}

    # 用户问题
    {user_question}

    # 任务要求
    1. 基于用户的具体报告内容回答问题
    2. 回答要专业、准确、有针对性
    3. 如果报告中没有相关信息，要诚实说明
    4. 提供实用的建议和指导
    5. 语言要亲切、鼓励，符合教育咨询的语调

    # 回答格式
    直接回答用户的问题，不需要额外的格式化。
    """

            # 调用 LLM
            from src.infrastructure.llm.llm_client import async_create_llm

            llm = await async_create_llm()
            response = await llm.ainvoke(prompt)

            # 提取回答内容
            if hasattr(response, "content"):
                answer = response.content
            else:
                answer = str(response)

            logger.info(f"[{self._module_name}] LLM 报告答疑成功")
            return answer

        except Exception as e:
            logger.error(f"调用 LLM 回答报告问题失败: {str(e)}", exc_info=True)
            # 降级处理：返回基础回答
            return "感谢您的问题。基于您已生成的分析报告，我建议您可以从以下几个方面考虑：\n\n1. 仔细查看报告中的具体建议\n2. 结合自己的实际情况进行判断\n3. 如需更详细的解答，建议查看完整的报告内容\n\n如果您需要生成更多报告或有其他问题，请随时告诉我。"

    def _build_report_context(self, completed_tasks: List[Any]) -> str:
        """构建报告上下文"""
        try:
            context_parts = []

            for task in completed_tasks:
                context_parts.append(f"## {task.task_name}")
                context_parts.append(f"内容：{task.report_content}")
                context_parts.append(f"生成时间：{task.updated_at}")
                context_parts.append("")
            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"构建报告上下文失败: {str(e)}", exc_info=True)
            return "报告内容获取失败"

    async def _handle_general_question(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        """处理一般性问题"""

        conversation_id = str(state["conversationId"])
        next_task = await self._get_next_task(conversation_id)

        # 有报告，引导用户查看报告或生成下一个
        if next_task:
            writer(
                {
                    "data": AiChatResultBO(
                        text=ReportMessageConfig.get_next_report_prompt(
                            next_task["name"]
                        ),
                        custom=CustomBO(
                            message_id=sf.generate(),
                            message_type="text"
                        ),
                    )
                }
            )
            return await self._return_to_mentor(state)
        else:
            writer(
                {
                    "data": AiChatResultBO(
                        text=ReportMessageConfig.get_all_completed_message(),
                        custom=CustomBO(
                            message_id=sf.generate(), message_type="text"
                        ),
                    )
                }
            )
            self._mark_completed_and_advance(state)
            return await self._return_to_coordinator(state)


    def _generate_task_start_message(self, task_config: Dict[str, Any]) -> str:
        return ReportMessageConfig.get_task_start_message(
            task_config["name"], task_config["order"]
        )

    def _preset_report_tasks(self) -> List:
        return ReportTaskConfig.get_preset_tasks()

    def _init_response(self, state: Dict[str, Any]) -> str:
        return ReportMessageConfig.INIT_RESPONSE

    async def _handle_init_status(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        # 首次进入
        # 1.1 发送开场文案
        writer({"data": AiChatResultBO(text=self._init_response(state))})

        writer(
            {
                "data": AiChatResultBO(
                    text=ReportMessageConfig.get_credit_confirm_message()
                )
            }
        )
        confirm_message_id = str(sf.generate())
        confirm_response = AiChatResultBO(
            text="",
            custom=CustomBO(
                message_id=confirm_message_id,
                message_type="select_buttons",
                message_status=MessageStatusBO(
                    # todo - 暂时放开输入
                    # user_input={"send_button_disable": True}
                ),
                message_data={
                    "id": ReportMessageConfig.CONFIRM_BUTTON_CONFIG["id"],
                    "message_id": confirm_message_id,
                    "options": ReportMessageConfig.CONFIRM_BUTTON_CONFIG["options"],
                },
            ),
        )
        writer({"data": confirm_response})

        self._update_current_context(
            state,
            {
                "confirm_response_message_id": confirm_message_id,
                "current_status": ModuleStatusEnum.REPORT_INIT_RESPONSE_SEND.value,
            },
        )
        return await self._return_to_mentor(state)

    async def _handle_credits(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        conversation_id = self._get_conversation_id(state)
        user: UserInfoDTO = self._get_user_info(state)
        consume_result: ConsumeCreditResultDTO = (
            await self.account_client.consume_credit(
                user_id=str(user.id), consume_related_id=conversation_id
            )
        )
        if (
            consume_result.status
            == ConsumeResultStatsuEnum.CONSUME_CREDIT_CONSUMED.value
        ):
            # todo - 只要有回复就确认
            # biz_runtime = state.get("bizRuntimeBO")
            # answer = (
            #     biz_runtime.params.get("answer", {})
            #     if biz_runtime and hasattr(biz_runtime, "params")
            #     else None
            # )
            # confirm_response_message_id = answer.get("id")
            # confirm_value = answer.get("value")
            confirm_response_message_id = self._get_current_context(state).get(
                "confirm_response_message_id"
            )
            confirm_value = "confirm"
            if confirm_response_message_id and confirm_value:
                await self._save_confirm_operation(
                    confirm_response_message_id, confirm_value
                )
                self._update_context_status(
                    state, ModuleStatusEnum.REPORT_COST_PAYED.value
                )
                return await self._return_to_coordinator(state)
            else:
                writer({"data": AiChatResultBO(text="回答格式错误")})
                return await self._return_to_mentor(state)
        else:
            credit_gap = ReportMessageConfig.CREDIT_COST - consume_result.remainCredit
            alert_message = ReportMessageConfig.get_credit_insufficient_message(
                credit_gap
            )
            alert_response = AiChatResultBO(
                text=alert_message,
                custom=CustomBO(
                    message_id="",
                    message_type="text",
                    message_status=MessageStatusBO(
                        user_input={"send_button_disable": False}
                    ),
                    message_data={"message": alert_message},
                ),
            )
            writer({"data": alert_response})

            recharge_form = AiChatResultBO(
                custom=CustomBO(
                    message_id="",
                    message_type="recharge_form",
                    message_status=MessageStatusBO(
                        user_input={"send_button_disable": True}
                    ),
                ),
            )
            writer({"data": recharge_form})
            return await self._return_to_mentor(state)

    async def _save_confirm_operation(self, message_id, selected_value):
        from src.infrastructure.db.crud.ai.AIMessageRepository import (
            AIMessageRepository,
        )

        # 获取原始消息
        message = await AIMessageRepository.get_by_id(int(message_id))
        if message and message.message_data:
            # 更新消息数据，添加用户选择的值
            message_data = message.message_data
            message_data["value"] = selected_value

            # 更新消息
            await AIMessageRepository.update(
                int(message_id), {"message_data": message_data}
            )
            logger.info(
                f"[{self._module_name}] 已更新消息ID {message_id} 的选项值为 {selected_value}"
            )
