import os
import time
from abc import abstractmethod
from typing import Dict, Any, Optional, List

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository
from src.infrastructure.llm.llm_client import async_create_llm, sf
from .base_module import BaseMentorModule
from ..services.questionnaire_service import QuestionnaireService
from ..services.persistence_service import PersistenceService
from ..models.enums import ModuleStatusEnum
from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO

logger = setup_logger(name=__name__, level="DEBUG")


class BaseQuestionnaire(BaseMentorModule):
    """统一的问卷处理基类

    提供问卷发送、答案处理、转场文案生成等通用功能。
    子类只需要实现具体的问卷内容和提示词。
    """

    def __init__(self):
        """初始化服务依赖"""
        self.questionnaire_service = QuestionnaireService()
        self.persistence_service = PersistenceService()

    @property
    @abstractmethod
    def _questionnaire_id(self) -> str:
        """问卷标识符，用于数据存储和检索"""
        pass

    @property
    @abstractmethod
    def _transition_prompt(self) -> str:
        """转场提示词，用于生成个性化转场文案"""
        pass

    @abstractmethod
    async def _generate_questionnaire_response(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> AiChatResultBO:
        """生成问卷内容

        子类需要实现具体的问卷结构和内容。

        Returns:
            AiChatResultBO: 问卷响应对象
        """
        pass

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """问卷模块主要执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        self._log_module_start(state)

        try:
            # 验证必需字段
            if not self._validate_required_fields(
                state, ["conversationId", "userInfo"]
            ):
                return await self._handle_validation_error(state, writer)

            # 获取当前模块上下文
            current_status = self._get_current_context_status(state)

            if not current_status:
                # 首次进入，发送转场文案
                return await self._send_init_text(state, writer)
            elif current_status == ModuleStatusEnum.QUESTIONNAIRE_INIT.value:
                # 转场文案已发送，发送问卷
                return await self._send_questionnaire(state, writer)
            elif current_status == ModuleStatusEnum.QUESTIONNAIRE_SENT.value:
                # 问卷已发送，处理答案
                return await self._handle_questionnaire_answer(state, writer)
            else:
                # 其他状态，返回协调器
                return await self._return_to_coordinator(state)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._return_to_coordinator(state)

    async def _send_transition_text(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """发送转场文案

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        try:
            # 生成转场文案
            await self._generate_transition_text(state, writer)

            # 更新状态
            self._update_context_status(state, ModuleStatusEnum.TRANSITION_SENT.value)

            self._log_module_end(state, "transition_sent")

            return await self._return_to_coordinator(state)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._return_to_coordinator(state)

    async def _send_questionnaire(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """发送问卷

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        try:
            # 生成问卷响应
            questionnaire_response = await self._generate_questionnaire_response(
                state, writer
            )

            # 发送问卷
            writer({"data": questionnaire_response})

            # 更新状态和上下文
            self._update_context_status(
                state, ModuleStatusEnum.QUESTIONNAIRE_SENT.value
            )

            if questionnaire_response and questionnaire_response.custom:
                message_data = questionnaire_response.custom.message_data or {}
                self._update_current_context(
                    state,
                    {
                        "questionnaire": message_data.get("question")
                        if isinstance(message_data, dict)
                        else None,
                        "questionnaire_message_id": questionnaire_response.custom.message_id,
                    },
                )

            self._log_module_end(state, "questionnaire_sent")

            # 创建包含状态更新的 Command，确保状态被持久化
            return await self._return_to_mentor_with_state_update(state)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._return_to_mentor(state)

    async def _handle_questionnaire_answer(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理问卷答案

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        try:
            # 解析问卷答案
            answer = self._parse_questionnaire_answer(state)

            if not answer:
                # 未获取到答案，提示用户重新提交
                logger.warning(f"[{self._module_name}] 问卷答案解析失败，state.bixRuntimeBO: {state.get('bizRuntimeBO')}")
                writer(
                    {
                        "data": AiChatResultBO(
                            text="未收到您的问卷答案，请重新提交您的问卷回答。"
                        )
                    }
                )
                return await self._return_to_mentor(state)

            # 保存答案
            await self._save_questionnaire_answer(state, answer)

            # 转场文案
            # await self._generate_transition_text(state, writer)
            # 标记完成并推进
            self._mark_completed_and_advance(state)

            self._log_module_end(state, "answer_processed")

            # 创建包含状态更新的 Command，确保状态被持久化
            return await self._return_to_coordinator_with_state_update(state)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._return_to_coordinator(state)

    def _parse_questionnaire_answer(
        self, state: Dict[str, Any]
    ) -> Optional[List[Dict[str, Any]]]:
        """解析问卷答案

        Args:
            state: 当前状态

        Returns:
            Optional[List[Dict[str, Any]]]: 解析后的答案
        """
        biz_runtime = self._get_biz_runtime(state)
        if (
            not biz_runtime
            or not hasattr(biz_runtime, "params")
            or not biz_runtime.params
        ):
            return None

        return self.questionnaire_service.parse_questionnaire_answer(biz_runtime)

    async def _save_questionnaire_answer(
        self, state: Dict[str, Any], answer: List[Dict[str, Any]]
    ):
        """保存问卷答案

        Args:
            state: 当前状态
            answer: 问卷答案
        """
        conversation_id = self._get_conversation_id(state)

        if conversation_id:
            last_messages = await AIMessageRepository.get_by_conversation_id_limit10(
                int(conversation_id)
            )
            questionnaire_ai_message_id = self._get_current_context(state).get(
                "questionnaire_message_id"
            )
            # todo 修复bug
            user_answer_message_content = next(
                (msg.message_data for msg in last_messages if msg.role == "user"), ""
            )

            await self.persistence_service.save_questionnaire_answer(
                conversation_id,
                self._module_name,
                answer,
                questionnaire_ai_message_id or "",
                user_answer_message_content,
            )

            # 更新上下文
            self._update_current_context(state, {"answer": answer})

    async def _send_init_text(self, state: Dict[str, Any], writer: StreamWriter):
        """发送开始文案"""
        init_response = await self._generate_init_text(state, writer)
        if init_response:
            writer(
                {
                    "data": AiChatResultBO(
                        text=init_response,
                        custom=CustomBO(message_id=sf.generate(), message_type="text"),
                    )
                }
            )
        self._update_context_status(state, ModuleStatusEnum.QUESTIONNAIRE_INIT.value)
        # 返回主控制器
        return await self._return_to_coordinator(state)

    @abstractmethod
    async def _generate_init_text(self, state: Dict[str, Any], writer: StreamWriter):
        """生成转场文案"""

        pass

    async def _handle_validation_error(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理验证错误

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        writer({"data": AiChatResultBO(text="系统信息不完整，请重新开始。")})
        return await self._return_to_coordinator(state)

    async def _generate_transition_text(
        self, state: Dict[str, Any], writer: StreamWriter
    ):
        """生成转场文案

        Args:
            state: 当前状态
            writer: 流式输出写入器
        """
        # 这里需要实现实际的LLM调用逻辑
        start_time = time.time()

        logger.info(f"[{self._module_name}] 初始化LLM")
        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_32_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(self._transition_prompt),
            ]
        )
        chain = prompts | llm

        logger.info(f"[{self._module_name}] 开始生成转场文案")
        current_context = self._get_current_context(state)
        user_info = self._get_user_info(state)
        full_response_content = ""

        message_id = sf.generate()
        # 使用流式输出处理
        async for chunk in chain.astream(
            {
                "questionnaire_questions": current_context.get("questionnaire"),
                "questionnaire_answers": current_context.get("answer"),
                "user_info": user_info,
            }
        ):
            if chunk.content:
                full_response_content += chunk.content
                writer(
                    {
                        "data": AiChatResultBO(
                            text=chunk.content,
                            custom=CustomBO(message_id=message_id, message_type="text"),
                        )
                    }
                )

            # 更新状态
        strategy_time = time.time() - start_time
        logger.info(f"[{self._module_name}] 转场文案生成耗时: {strategy_time:.2f}秒")
