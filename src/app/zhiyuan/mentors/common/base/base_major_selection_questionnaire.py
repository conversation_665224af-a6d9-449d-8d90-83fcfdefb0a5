import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List

from langgraph.types import StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO
from src.exe.command.MajorRecommendationCommandExe import (
    MajorRecommendationCommandExe,
    RecommendMajorResult,
    RecommendMajor,
)
from src.utils.snow_flake import Snowflake
from ..models.questionnaire_models import (
    QuestionnaireQuestionDetail,
    QuestionnaireQuestion,
    QuestionnairePole,
    QuestionnaireScale,
    QuestionnaireScaleLabel,
)
from ...common.base.base_questionnaire import BaseQuestionnaire

# 专业选择问卷转场提示词
MAJOR_SELECTION_TRANSITION_PROMPT = """
基于用户的专业选择问卷回答，生成一段转场文案，引导用户进入画像澄清环节。

要求：
1. 总结用户对专业的偏好特点
2. 表达对用户配合的感谢
3. 自然过渡到画像澄清环节
4. 语气要友好、专业

用户信息:
{user_info}

问卷内容:
{questionnaire_questions}

用户问卷回答：
{questionnaire_answers}

请生成一段100-150字的转场文案，直接输出文案内容。
"""

logger = setup_logger(name=__name__, level="DEBUG")

# 创建 Snowflake 实例
sf = Snowflake(worker_id=0, datacenter_id=0)


class BaseMajorSelectionQuestionnaire(BaseQuestionnaire, ABC):
    """专业选择问卷模块 v2.0

    基于通用架构的问卷实现，生成专业评估李克特量表。
    """

    @property
    def _questionnaire_id(self) -> str:
        """问卷标识符"""
        return "major_selection"

    @property
    def _transition_prompt(self) -> str:
        """转场提示词"""
        return MAJOR_SELECTION_TRANSITION_PROMPT

    @abstractmethod
    async def _collect_user_data(self, state: Dict[str, Any]) -> Dict[str, Any]:
        pass

    async def _generate_questionnaire_response(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> AiChatResultBO:
        """生成专业选择问卷

        Returns:
            AiChatResultBO: 问卷响应对象
        """
        # 尝试生成个性化问卷，如果失败则使用默认问卷

        # 收集用户数据

        # 发送 loading
        loading_message_id = sf.generate()
        writer(
            {
                "data": AiChatResultBO(
                    custom=CustomBO(
                        message_type="loading",
                        message_id=loading_message_id,
                        message_status=MessageStatusBO(
                            user_input={"send_button_disable": True}
                        )
                    )
                )
            }
        )

        start_time = time.time()
        user_data = await self._collect_user_data(state)
        generation_time = time.time() - start_time
        logger.info(f"[{self._module_name}] 分析用户信息耗时: {generation_time:.2f}秒")

        # 生成专业推荐
        start_time = time.time()
        recommend_result: RecommendMajorResult = (
            await MajorRecommendationCommandExe.generate_major_recommendations(
                user_data
            )
        )
        generation_time = time.time() - start_time
        logger.info(
            f"[{self._module_name}] LLM生成专业推荐信息耗时: {generation_time:.2f}秒"
        )

        # 如果LLM推荐成功，构建动态问卷
        if recommend_result and len(recommend_result.recommend_majors) > 1:
            writer(
                {
                    "data": AiChatResultBO(
                        text="\n请对每个专业表达您的兴趣程度，这将帮助我们为您制定更精准的志愿填报方案："
                    )
                }
            )
            return await self._create_dynamic_questionnaire(
                recommend_result.recommend_majors
            )

        # 兜底：返回默认问卷
        logger.info(f"[{self._module_name}] 使用默认问卷")
        return self._get_default_questionnaire_response()

    async def _create_dynamic_questionnaire(
        self, recommend_majors: List[RecommendMajor]
    ) -> AiChatResultBO:
        """构建动态问卷"""
        try:
            questions = []
            for recommendation in recommend_majors:
                questions.append(
                    QuestionnaireQuestion(
                        id=str(recommendation.id),  # 确保是字符串
                        title=recommendation.name,
                        poles={
                            "left": QuestionnairePole(title="不感兴趣", examples=""),
                            "right": QuestionnairePole(title="高意愿", examples=""),
                        },
                    )
                )

            # 如果推荐的专业太少，使用默认问卷
            if len(questions) <= 1:
                logger.warning(
                    f"[{self._module_name}] LLM推荐专业数量过少({len(questions)}个)，使用默认问卷"
                )
                return self._get_default_questionnaire_response()

            question = QuestionnaireQuestionDetail(
                id=self._questionnaire_id,
                title="个性化专业选择评估问卷",
                description="请对以下为您推荐的专业方向表达您的意向程度...",
                scale=QuestionnaireScale(
                    type="likert_table",
                    displayType="likert_table",
                    points=3,
                    labels=[
                        QuestionnaireScaleLabel(value=0, text="不感兴趣"),
                        QuestionnaireScaleLabel(value=1, text="可备选"),
                        QuestionnaireScaleLabel(value=2, text="高意愿"),
                    ],
                ),
                questions=questions,
            )

            return AiChatResultBO(
                text="",
                custom=CustomBO(
                    message_id=sf.generate(),
                    message_type="likert_table",
                    message_status=MessageStatusBO(
                        user_input={"send_button_disable": True}
                    ),
                    message_data={"question": question.model_dump()},
                ),
            )

        except Exception as e:
            logger.error(f"[{self._module_name}] 构建动态问卷失败: {str(e)}")
            return self._get_default_questionnaire_response()

    def _get_default_questionnaire_response(self) -> AiChatResultBO:
        """获取默认问卷响应"""
        logger.info(f"[{self._module_name}] 使用默认专业选择问卷")

        question = QuestionnaireQuestionDetail(
            id=self._questionnaire_id,
            title="专业选择评估问卷",
            description="请对以下专业方向表达您的意向程度...",
            scale=QuestionnaireScale(
                type="likert_table",
                displayType="likert_table",
                points=3,
                labels=[
                    QuestionnaireScaleLabel(value=0, text="不感兴趣"),
                    QuestionnaireScaleLabel(value=1, text="可备选"),
                    QuestionnaireScaleLabel(value=2, text="高意愿"),
                ],
            ),
            questions=[
                QuestionnaireQuestion(
                    id="080901",  # 计算机科学与技术的专业代码
                    title="计算机科学与技术",
                    poles={
                        "left": QuestionnairePole(title="不感兴趣", examples=""),
                        "right": QuestionnairePole(title="高意愿", examples=""),
                    },
                ),
                QuestionnaireQuestion(
                    id="080902",  # 软件工程的专业代码
                    title="软件工程",
                    poles={
                        "left": QuestionnairePole(title="不感兴趣", examples=""),
                        "right": QuestionnairePole(title="高意愿", examples=""),
                    },
                ),
                QuestionnaireQuestion(
                    id="080701",  # 电子信息工程的专业代码
                    title="电子信息工程",
                    poles={
                        "left": QuestionnairePole(title="不感兴趣", examples=""),
                        "right": QuestionnairePole(title="高意愿", examples=""),
                    },
                ),
                QuestionnaireQuestion(
                    id="080201",  # 机械工程的专业代码
                    title="机械工程",
                    poles={
                        "left": QuestionnairePole(title="不感兴趣", examples=""),
                        "right": QuestionnairePole(title="高意愿", examples=""),
                    },
                ),
                QuestionnaireQuestion(
                    id="020301K",  # 金融学的专业代码
                    title="金融学",
                    poles={
                        "left": QuestionnairePole(title="不感兴趣", examples=""),
                        "right": QuestionnairePole(title="高意愿", examples=""),
                    },
                ),
                QuestionnaireQuestion(
                    id="100201K",  # 临床医学的专业代码
                    title="临床医学",
                    poles={
                        "left": QuestionnairePole(title="不感兴趣", examples=""),
                        "right": QuestionnairePole(title="高意愿", examples=""),
                    },
                ),
            ],
        )
        """获取默认的专业选择问卷"""
        return AiChatResultBO(
            text="",
            custom=CustomBO(
                message_id=sf.generate(),
                message_type="likert_table",
                message_status=MessageStatusBO(
                    user_input={"send_button_disable": True},
                ),
                message_data={"question": question.model_dump()},
            ),
        )

    async def _generate_init_text(self, state: Dict[str, Any], writer: StreamWriter):
        return "根据我们对您各项维度的深入分析，我将为您推荐相关专业方向。这个过程将花费一些时间，请稍候。"
