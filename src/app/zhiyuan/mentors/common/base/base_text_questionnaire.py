"""
基础文本问卷模块

提供简单文本问答形式的问卷基类，用户回答直接在 message.text 中，
而不是复杂的结构化问卷格式。
"""

import time
from abc import abstractmethod
from typing import Dict, Any, Optional
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO
from src.utils.snow_flake import Snowflake
from .base_module import BaseMentorModule
from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)

logger = setup_logger(name=__name__, level="DEBUG")

sf = Snowflake(worker_id=0, datacenter_id=0)


class BaseTextQuestionnaire(BaseMentorModule):
    """基础文本问卷模块

    用于处理简单的文本问答形式的问卷：
    1. 发送一个问题文本
    2. 用户回答在 message.text 中
    3. 保存问卷和答案到数据库和 context
    4. 支持重试和验证机制
    """

    def __init__(self):
        """初始化基础文本问卷模块"""
        super().__init__()
        self._max_retry_count = 3
        self._current_retry_count = 0

    @property
    @abstractmethod
    def _questionnaire_id(self) -> str:
        """问卷ID，子类必须实现"""
        pass

    @property
    @abstractmethod
    def _question_text(self) -> str:
        """问题文本，子类必须实现"""
        pass

    @property
    def _max_retries(self) -> int:
        """最大重试次数，子类可以重写"""
        return 3

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """文本问卷主要执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        self._log_module_start(state)

        try:
            # 检查是否已经发送过问卷
            current_context = self._get_current_context(state)
            questionnaire_sent = current_context.get("questionnaire_sent", False)

            if not questionnaire_sent:
                # 第一次进入，发送问卷
                return await self._send_questionnaire(state, writer)
            else:
                # 用户已回答，处理答案
                return await self._handle_user_answer(state, writer)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._return_to_coordinator(state)

    async def _send_questionnaire(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """发送文本问卷

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        logger.info(f"[{self._module_name}] 发送文本问卷: {self._questionnaire_id}")

        # 获取问题文本
        question_text = self._question_text

        # 可以添加前置说明
        intro_text = await self._get_intro_text(state)
        if intro_text:
            full_text = f"{intro_text}\n\n{question_text}"
        else:
            full_text = question_text

        # 发送问题
        response = AiChatResultBO(
            text=full_text,
            custom=CustomBO(
                message_id=sf.generate(),
                message_type="text",
                message_status=MessageStatusBO(
                    user_input={"send_button_disable": False}
                ),
            ),
        )
        writer({"data": response})

        # 更新上下文状态
        self._update_current_context(
            state,
            {
                "questionnaire_sent": True,
                "questionnaire": question_text,
                "sent_at": time.time(),
                "retry_count": 0,
            },
        )

        # 返回到 Mentor 等待用户回答
        return await self._return_to_mentor(state)

    async def _handle_user_answer(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理用户答案

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        # 获取用户输入
        user_input = self._get_user_input(state)

        if not user_input or not user_input.strip():
            # 用户输入为空，请求重新输入
            return await self._handle_empty_answer(state, writer)

        # 验证答案
        validation_result = await self._validate_answer(user_input, state)

        if not validation_result["valid"]:
            # 答案无效，请求重新输入
            return await self._handle_invalid_answer(
                state, writer, validation_result["message"]
            )

        # 答案有效，保存并继续
        await self._save_answer(state, user_input)

        # 发送确认消息
        confirmation_msg = await self._get_confirmation_message(user_input, state)
        if confirmation_msg:
            writer(
                {
                    "data": AiChatResultBO(
                        text=confirmation_msg,
                        custom=CustomBO(message_id=sf.generate(), message_type="text"),
                    )
                }
            )

        # 标记完成并前进到下一步
        self._mark_completed_and_advance(state)

        self._log_module_end(state, "completed")
        return await self._return_to_coordinator(state)

    async def _handle_empty_answer(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理空答案

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        current_context = self._get_current_context(state)
        retry_count = current_context.get("retry_count", 0) + 1

        if retry_count > self._max_retries:
            # 超过最大重试次数，使用默认答案
            default_answer = await self._get_default_answer(state)
            await self._save_answer(state, default_answer)

            self._mark_completed_and_advance(state)
            return await self._return_to_coordinator(state)

        # 更新重试次数
        self._update_current_context(state, {"retry_count": retry_count})

        # 发送重试提示
        retry_msg = await self._get_retry_message_for_empty(retry_count, state)
        writer(
            {
                "data": AiChatResultBO(
                    text=retry_msg,
                    custom=CustomBO(message_id=sf.generate(), message_type="text"),
                )
            }
        )

        return await self._return_to_mentor(state)

    async def _handle_invalid_answer(
        self, state: Dict[str, Any], writer: StreamWriter, error_message: str
    ) -> Command:
        """处理无效答案

        Args:
            state: 当前状态
            writer: 流式输出写入器
            error_message: 错误信息

        Returns:
            Command: 执行命令
        """
        current_context = self._get_current_context(state)
        retry_count = current_context.get("retry_count", 0) + 1

        if retry_count > self._max_retries:
            # 超过最大重试次数，接受当前答案
            user_input = self._get_user_input(state)
            await self._save_answer(state, user_input)

            self._mark_completed_and_advance(state)
            return await self._return_to_coordinator(state)

        # 更新重试次数
        self._update_current_context(state, {"retry_count": retry_count})

        # 发送重试提示
        retry_msg = await self._get_retry_message_for_invalid(
            retry_count, error_message, state
        )
        writer(
            {
                "data": AiChatResultBO(
                    text=retry_msg,
                    custom=CustomBO(message_id=sf.generate(), message_type="text"),
                )
            }
        )

        return await self._return_to_mentor(state)

    async def _save_questionnaire_to_db(
        self, state: Dict[str, Any], question_text: str
    ):
        """保存问卷信息到数据库

        Args:
            state: 当前状态
            question_text: 问题文本
        """
        try:
            conversation_id = self._get_conversation_id(state)
            if not conversation_id:
                logger.warning(f"[{self._module_name}] 无法获取会话ID，跳过数据库保存")
                return

            # 保存问卷信息
            questionnaire_data = {
                "questionnaire_id": self._questionnaire_id,
                "question_text": question_text,
                "question_type": "text",
                "sent_at": time.time(),
            }

            # 更新会话的 user_features
            conversation = await AIConversationRepository.get_by_id(
                int(conversation_id)
            )
            if conversation:
                if not conversation.user_features:
                    conversation.user_features = {}

                conversation.user_features[
                    f"{self._questionnaire_id}_questionnaire"
                ] = questionnaire_data
                await AIConversationRepository.update(
                    int(conversation_id), {"user_features": conversation.user_features}
                )

                logger.info(f"[{self._module_name}] 问卷信息已保存到数据库")

        except Exception as e:
            logger.error(f"[{self._module_name}] 保存问卷信息到数据库失败: {str(e)}")

    async def _save_answer(self, state: Dict[str, Any], answer: str):
        """保存用户答案

        Args:
            state: 当前状态
            answer: 用户答案
        """
        try:
            self._update_current_context(
                state,
                {"answer": answer},
            )
            # 保存到上下文

            # 保存到数据库
            conversation_id = self._get_conversation_id(state)
            if conversation_id:
                conversation = await AIConversationRepository.get_by_id(
                    int(conversation_id)
                )
                if conversation:
                    if not conversation.user_features:
                        conversation.user_features = {}

                    # 保存原始答案
                    conversation.user_features[
                        f"{self._questionnaire_id}_raw_answer"
                    ] = answer

                    # 保存处理后的答案（子类可以重写处理逻辑）
                    processed_answer = await self._process_answer(answer, state)
                    conversation.user_features[
                        f"{self._questionnaire_id}_processed_answer"
                    ] = processed_answer

                    await AIConversationRepository.update(
                        int(conversation_id),
                        {"user_features": conversation.user_features},
                    )

                    logger.info(
                        f"[{self._module_name}] 用户答案已保存: {answer[:50]}..."
                    )

        except Exception as e:
            logger.error(f"[{self._module_name}] 保存用户答案失败: {str(e)}")

    # 以下方法子类可以重写以自定义行为

    async def _get_intro_text(self, state: Dict[str, Any]) -> Optional[str]:
        """获取问卷前置说明文本，子类可以重写

        Args:
            state: 当前状态

        Returns:
            Optional[str]: 前置说明文本，None表示无前置说明
        """
        return None

    async def _validate_answer(
        self, answer: str, state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证用户答案，子类可以重写

        Args:
            answer: 用户答案
            state: 当前状态

        Returns:
            Dict[str, Any]: 验证结果 {"valid": bool, "message": str}
        """
        # 默认验证：非空即有效
        if answer and answer.strip():
            return {"valid": True, "message": ""}
        else:
            return {"valid": False, "message": "答案不能为空"}

    async def _process_answer(self, answer: str, state: Dict[str, Any]) -> Any:
        """处理用户答案，子类可以重写

        Args:
            answer: 原始答案
            state: 当前状态

        Returns:
            Any: 处理后的答案
        """
        # 默认不处理，直接返回原始答案
        return answer

    async def _get_confirmation_message(
        self, answer: str, state: Dict[str, Any]
    ) -> Optional[str]:
        """获取确认消息，子类可以重写

        Args:
            answer: 用户答案
            state: 当前状态

        Returns:
            Optional[str]: 确认消息，None表示无确认消息
        """
        return None

    async def _get_default_answer(self, state: Dict[str, Any]) -> str:
        """获取默认答案，子类可以重写

        Args:
            state: 当前状态

        Returns:
            str: 默认答案
        """
        return "暂无明确想法"

    async def _get_retry_message_for_empty(
        self, retry_count: int, state: Dict[str, Any]
    ) -> str:
        """获取空答案重试消息，子类可以重写

        Args:
            retry_count: 当前重试次数
            state: 当前状态

        Returns:
            str: 重试消息
        """
        if retry_count == 1:
            return "请您回答一下这个问题，这对我为您提供个性化建议很重要。"
        elif retry_count == 2:
            return "如果您暂时没有明确的想法，也可以简单说一下您的大致方向或者说'暂无明确想法'。"
        else:
            return "没关系，我们继续下一步。"

    async def _get_retry_message_for_invalid(
        self, retry_count: int, error_message: str, state: Dict[str, Any]
    ) -> str:
        """获取无效答案重试消息，子类可以重写

        Args:
            retry_count: 当前重试次数
            error_message: 错误信息
            state: 当前状态

        Returns:
            str: 重试消息
        """
        return f"抱歉，{error_message}。请您重新回答一下。"
