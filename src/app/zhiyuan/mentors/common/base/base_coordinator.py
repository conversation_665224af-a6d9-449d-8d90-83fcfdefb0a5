from abc import abstractmethod
from typing import Dict, Any, Optional
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from .base_module import BaseMentorModule
from src.domain.model.ai_chat_model import AiChatResultBO
from src.app.zhiyuan.constant import AgentTypeEnum

logger = setup_logger(name=__name__, level="DEBUG")


class BaseCoordinator(BaseMentorModule):
    """统一的协调器基类

    提供流程管理、模块路由、状态验证等通用功能。
    子类只需要提供流程配置即可。
    """

    def __init__(self, flow_config: Dict[str, Any]):
        """初始化协调器

        Args:
            flow_config: 流程配置，包含模块序列和配置信息
        """
        self.module_sequence: list = flow_config.get("sequence", [])
        self.module_configs: dict = flow_config.get("module_configs", {})
        self.flow_metadata: dict = flow_config.get("flow_metadata", {})

    @property
    @abstractmethod
    def _module_name(self) -> str:
        """协调器名称"""
        pass

    @abstractmethod
    async def _get_command_update_function(self, state: Dict[str, Any]):
        """获取命令更新函数

        不同模式有不同的命令更新函数，子类需要实现。

        Args:
            state: 当前状态

        Returns:
            命令更新函数的结果
        """
        pass

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """协调器主要执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        self._log_module_start(state)

        try:
            # 验证状态
            if not self._validate_state(state):
                return await self._handle_invalid_state(state, writer)


            # 获取当前模块
            current_module = self._get_current_module(state)

            if current_module:
                # 路由到当前模块
                self._log_module_end(state, f"routing_to_{current_module}")

                # 确保状态更新被正确传递
                state_update = self._create_state_update(state)

                return Command(
                    goto=current_module,
                    update=state_update,
                )
            else:
                # 流程完成
                return await self._handle_flow_completion(state, writer)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._handle_error_recovery(state)

    def _get_current_module(self, state: Dict[str, Any]) -> Optional[str]:
        """获取当前应该执行的模块

        Args:
            state: 当前状态

        Returns:
            Optional[str]: 当前模块名称，None 表示流程完成
        """
        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        current_step = context.get("current_step", 0) or 0

        # 添加调试日志
        logger.info(
            f"[{self._module_name}] 获取当前模块 - current_step: {current_step}, sequence_length: {len(self.module_sequence)}"
        )

        # 验证步骤范围
        if current_step < 0:
            logger.warning(f"[{self._module_name}] current_step 为负数，重置为 0")
            current_step = 0
            context["current_step"] = 0
        elif current_step > len(self.module_sequence):
            logger.warning(f"[{self._module_name}] current_step 超出范围，设置为流程完成")
            return None

        if current_step < len(self.module_sequence):
            current_module = self.module_sequence[current_step]
            logger.info(
                f"[{self._module_name}] 路由到模块: {current_module} (步骤 {current_step})"
            )
            return current_module

        logger.info(f"[{self._module_name}] 流程完成，无更多模块")
        return None

    def _get_next_module(self, state: Dict[str, Any]) -> Optional[str]:
        """获取下一个模块

        Args:
            state: 当前状态

        Returns:
            Optional[str]: 下一个模块名称，None 表示没有下一个模块
        """
        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        current_step = context.get("current_step", 0) or 0
        next_step = current_step + 1

        if next_step < len(self.module_sequence):
            return self.module_sequence[next_step]

        return None

    def _validate_state(self, state: Dict[str, Any]) -> bool:
        """验证状态有效性

        Args:
            state: 当前状态

        Returns:
            bool: 状态是否有效
        """
        try:
            # 检查必需字段
            required_fields = ["conversationId", "userInfo"]
            if not self._validate_required_fields(state, required_fields):
                return False

            # 检查步骤范围
            self._ensure_biz_runtime_structure(state)
            biz_runtime = state["bizRuntimeBO"]
            context = biz_runtime.context
            current_step = context.get("current_step", 0) or 0
            if current_step < 0 or current_step > len(self.module_sequence):
                logger.warning(f"[{self._module_name}] 步骤超出范围: {current_step}")
                return False

            return True

        except Exception as e:
            logger.error(f"[{self._module_name}] 状态验证异常: {str(e)}")
            return False

    def _get_module_progress(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """获取模块进度信息

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 进度信息
        """
        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        current_step = context.get("current_step", 0) or 0
        total_steps = len(self.module_sequence)

        progress = {
            "current_step": current_step,
            "total_steps": total_steps,
            "progress_percentage": int((current_step / total_steps) * 100)
            if total_steps > 0
            else 0,
            "current_module": self.module_sequence[current_step]
            if current_step < total_steps
            else "完成",
            "completed_modules": self.module_sequence[:current_step],
            "remaining_modules": self.module_sequence[current_step:],
            "flow_metadata": context.get("flow_metadata", self.flow_metadata),
        }

        return progress

    async def _handle_interrupt_recovery(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理中断恢复

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 恢复命令
        """
        logger.info(f"[{self._module_name}] 检测到中断恢复状态")

        # 重置中断标志
        state["interrupt_feedback"] = False

        # 返回到第一个模块
        if self.module_sequence:
            first_module = self.module_sequence[0]

            # 确保 bizRuntimeBO 结构并重置步骤
            self._ensure_biz_runtime_structure(state)
            biz_runtime = state["bizRuntimeBO"]
            biz_runtime.context["current_step"] = 0

            return Command(
                goto=first_module,
                update=await self._get_command_update_function(state),
            )

        return await self._handle_flow_completion(state, writer)

    async def _handle_flow_completion(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理流程完成

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 完成命令
        """
        logger.info(f"[{self._module_name}] 流程完成")

        completion_message = (
            f"{self.flow_metadata.get('name', '志愿填报')}流程已完成，感谢您的参与！"
        )
        writer({"data": AiChatResultBO(text=completion_message)})

        # 返回到编排器
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await self._get_command_update_function(state),
        )

    async def _handle_invalid_state(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理无效状态

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 错误处理命令
        """
        logger.warning(f"[{self._module_name}] 状态验证失败")

        writer({"data": AiChatResultBO(text="系统状态异常，请稍后重试。")})

        # 重置状态
        # biz_runtime = state["bizRuntimeBO"]
        # context = biz_runtime.context
        # context["current_step"] = 0
        # context["questionnaire_context"] = {}
        # context["user_features"] = {}
        # context["flow_metadata"] = {}

        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await self._get_command_update_function(state),
        )

    async def _handle_error_recovery(self, state: Dict[str, Any]) -> Command:
        """处理错误恢复

        Args:
            state: 当前状态

        Returns:
            Command: 错误恢复命令
        """

        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await self._get_command_update_function(state),
        )
