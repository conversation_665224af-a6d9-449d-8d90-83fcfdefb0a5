from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)

logger = setup_logger(name=__name__, level="DEBUG")


class BaseMentorModule(ABC):
    """所有志愿填报模块的基础抽象类

    提供统一的接口定义和公共方法实现，确保所有模块的行为一致性。
    """

    @property
    @abstractmethod
    def _module_name(self) -> str:
        """模块名称，用于日志和状态管理"""
        pass

    @abstractmethod
    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """模块主要执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        pass

    @abstractmethod
    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        子类必须实现此方法，因为不同模式有不同的协调器名称。

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        pass

    @abstractmethod
    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        子类必须实现此方法，因为不同模式有不同的主入口名称。

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        pass

    def _get_conversation_context(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """获取当前对话的上下文信息

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 对话上下文
        """
        self._ensure_biz_runtime_structure(state)

        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        questionnaire_context = context["questionnaire_context"]
        return questionnaire_context

    def _get_current_context(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """获取当前模块的上下文信息

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 模块上下文
        """
        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        questionnaire_context = context["questionnaire_context"]
        return questionnaire_context.get(self._module_name, {})

    def _get_current_context_status(self, state: Dict[str, Any]) -> str:
        current_context = self._get_current_context(state)
        return current_context.get("current_status")

    def _collect_questionnaire_history(self, state: Dict[str, Any]):
        """收集问卷回答历史"""
        questionnaire_context = self._get_conversation_context(state)
        history = []

        logger.info(
            f"[{self._module_name}] 开始收集问卷历史，questionnaire_context 包含 {len(questionnaire_context)} 个模块"
        )

        for module_name, module_context in questionnaire_context.items():
            logger.debug(f"[{self._module_name}] 检查模块: {module_name}")

            if module_name == self._module_name:
                logger.debug(f"[{self._module_name}] 跳过自己")
                continue  # 跳过自己

            questionnaire = module_context.get("questionnaire")
            answer = module_context.get("answer")

            logger.debug(
                f"[{self._module_name}] 模块 {module_name}: questionnaire={'存在' if questionnaire else '缺失'}, answer={'存在' if answer else '缺失'}"
            )

            if questionnaire and answer:
                history.append(
                    {
                        "module": module_name,
                        "questionnaire": questionnaire,
                        "answer": answer,
                    }
                )
                logger.info(
                    f"[{self._module_name}] 成功收集模块 {module_name} 的问卷历史"
                )
            else:
                logger.warning(
                    f"[{self._module_name}] 模块 {module_name} 数据不完整，跳过"
                )

        logger.info(
            f"[{self._module_name}] 问卷历史收集完成，共收集到 {len(history)} 个有效历史"
        )
        return history

    async def _collect_clarification_data(self, state: Dict[str, Any]):
        conversation_id = self._get_conversation_id(state)
        conversation = await AIConversationRepository.get_by_id(int(conversation_id))
        user_features = conversation.user_features or {}
        clarification_data = ""
        for _, item in user_features.items():
            if isinstance(item, dict) and item.get("user_clarifications", {}):
                for clarification in item.get("user_clarifications", {}):
                    clarification_data += (
                        f"澄清内容：{clarification['clarification_content']}\n"
                    )
                    clarification_data += f"用户回应：{clarification['response']}\n\n"
        return clarification_data

    def _ensure_biz_runtime_structure(self, state: Dict[str, Any]):
        """确保 bizRuntimeBO 结构完整

        Args:
            state: 当前状态
        """

        if "bizRuntimeBO" not in state or state["bizRuntimeBO"] is None:
            # 创建新的 BizRuntimeBO 实例
            state["bizRuntimeBO"] = BizRuntimeBO(params={}, context={})

        biz_runtime = state["bizRuntimeBO"]

        # 如果是字典，转换为 BizRuntimeBO 对象
        if isinstance(biz_runtime, dict):
            state["bizRuntimeBO"] = BizRuntimeBO(
                params=biz_runtime.get("params", {}),
                school=biz_runtime.get("school"),
                major=biz_runtime.get("major"),
                context=biz_runtime.get("context", {}),
            )
            biz_runtime = state["bizRuntimeBO"]

        # 确保 context 存在
        if biz_runtime.context is None:
            biz_runtime.context = {}

        # 确保 context 下的必要字段存在
        context = biz_runtime.context
        if "questionnaire_context" not in context:
            context["questionnaire_context"] = {}
        if "current_step" not in context:
            context["current_step"] = 0
        if "user_features" not in context:
            context["user_features"] = {}
        if "flow_metadata" not in context:
            context["flow_metadata"] = {}

    def _update_current_context(self, state: Dict[str, Any], updates: Dict[str, Any]):
        """更新当前模块的上下文信息

        Args:
            state: 当前状态
            updates: 要更新的内容
        """
        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        questionnaire_context = context["questionnaire_context"]

        if self._module_name not in questionnaire_context:
            questionnaire_context[self._module_name] = {}

        questionnaire_context[self._module_name].update(updates)

        # 添加时间戳以确保状态变化
        import time

        questionnaire_context[self._module_name]["updated_at"] = time.time()

        logger.debug(f"[{self._module_name}] 上下文已更新: {updates}")

    def _update_context_status(self, state: Dict[str, Any], status: str):
        """更新模块状态

        Args:
            state: 当前状态
            status: 新状态
        """
        self._update_current_context(state, {"current_status": status})
        logger.info(f"[{self._module_name}] 状态更新为: {status}")

    def _mark_completed_and_advance(self, state: Dict[str, Any]):
        """标记模块完成并推进到下一步

        Args:
            state: 当前状态
        """
        # 标记当前模块完成
        self._update_context_status(state, "completed")

        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        # 推进步骤
        biz_runtime = state["bizRuntimeBO"]
        context = biz_runtime.context
        current_step = context.get("current_step", 0) or 0
        new_step = current_step + 1
        context["current_step"] = new_step

        logger.info(
            f"[{self._module_name}] 模块完成，步骤推进: {current_step} -> {new_step}"
        )
        logger.info(
            f"[{self._module_name}] 更新后的 current_step: {context.get('current_step')}"
        )

    def _create_state_update(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """创建状态更新字典，确保 LangGraph 能正确保存到 checkpoint

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 用于更新的状态字典
        """
        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(state)

        # 返回完整的状态，确保所有更改都被保存
        return {
            "userInfo": state.get("userInfo"),
            "conversationId": state.get("conversationId"),
            "messages": state.get("messages", []),
            "bizRuntimeBO": state.get("bizRuntimeBO"),
        }

    async def _return_to_coordinator_with_state_update(
        self, state: Dict[str, Any]
    ) -> Command:
        """返回协调器并包含状态更新

        Args:
            state: 当前状态

        Returns:
            Command: 包含状态更新的返回协调器命令
        """
        from langgraph.types import Command

        # 获取原始的返回命令
        original_command = await self._return_to_coordinator(state)

        # 创建新的 Command 包含状态更新
        return Command(
            goto=original_command.goto, update=self._create_state_update(state)
        )

    async def _return_to_mentor_with_state_update(
        self, state: Dict[str, Any]
    ) -> Command:
        """返回主入口并包含状态更新

        Args:
            state: 当前状态

        Returns:
            Command: 包含状态更新的返回主入口命令
        """
        from langgraph.types import Command

        # 获取原始的返回命令
        original_command = await self._return_to_mentor(state)

        # 创建新的 Command 包含状态更新
        return Command(
            goto=original_command.goto, update=self._create_state_update(state)
        )

    def _get_conversation_id(self, state: Dict[str, Any]) -> Optional[str]:
        """安全获取会话ID

        Args:
            state: 当前状态

        Returns:
            Optional[str]: 会话ID
        """
        return state.get("conversationId")

    def _get_user_info(self, state: Dict[str, Any]) -> Optional[Any]:
        """安全获取用户信息

        Args:
            state: 当前状态

        Returns:
            Optional[Any]: 用户信息
        """
        return state.get("userInfo")

    def _get_biz_runtime(self, state: Dict[str, Any]) -> Optional[Any]:
        """安全获取业务运行时信息

        Args:
            state: 当前状态

        Returns:
            Optional[Any]: 业务运行时信息
        """
        return state.get("bizRuntimeBO")

    def _get_messages(self, state: Dict[str, Any]) -> list:
        """安全获取消息列表

        Args:
            state: 当前状态

        Returns:
            list: 消息列表
        """
        return state.get("messages", [])

    def _get_user_input(self, state: Dict[str, Any]) -> str:
        """获取用户最新输入

        Args:
            state: 当前状态

        Returns:
            str: 用户输入内容
        """
        messages = self._get_messages(state)
        if messages:
            last_message = messages[-1]
            # 使用 getattr 安全获取 content，兼容 LangChain 消息对象
            user_message = getattr(last_message, "content", str(last_message))
            return user_message if isinstance(user_message, str) else ""
        return ""

    def _validate_required_fields(
        self, state: Dict[str, Any], required_fields: list
    ) -> bool:
        """验证必需字段是否存在

        Args:
            state: 当前状态
            required_fields: 必需字段列表

        Returns:
            bool: 验证结果
        """
        for field in required_fields:
            if not state.get(field):
                logger.warning(f"[{self._module_name}] 缺少必需字段: {field}")
                return False
        return True

    def _log_module_start(self, state: Dict[str, Any]):
        """记录模块开始执行

        Args:
            state: 当前状态
        """
        conversation_id = self._get_conversation_id(state)
        current_step = state.get("current_step", 0)
        logger.info(
            f"[{self._module_name}] 开始执行 - 会话ID: {conversation_id}, 步骤: {current_step}"
        )

    def _log_module_end(self, state: Dict[str, Any], result: str = "success"):
        """记录模块执行结束

        Args:
            state: 当前状态
            result: 执行结果
        """
        conversation_id = self._get_conversation_id(state)
        logger.info(
            f"[{self._module_name}] 执行结束 - 会话ID: {conversation_id}, 结果: {result}"
        )

    def _handle_error(self, error: Exception, state: Dict[str, Any]) -> str:
        """统一的错误处理

        Args:
            error: 异常对象
            state: 当前状态

        Returns:
            str: 错误消息
        """
        conversation_id = self._get_conversation_id(state)
        error_msg = f"[{self._module_name}] 执行异常 - 会话ID: {conversation_id}, 错误: {str(error)}"
        logger.error(error_msg, exc_info=True)
        return "处理过程中出现错误，请稍后重试"
