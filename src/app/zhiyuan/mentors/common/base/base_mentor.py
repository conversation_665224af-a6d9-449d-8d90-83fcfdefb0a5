import os
from abc import abstractmethod
from typing import Dict, Any

from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, StreamWriter
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient
from .base_module import BaseMentorModule

logger = setup_logger(name=__name__, level="DEBUG")


class BaseMentor(BaseMentorModule):
    """统一的主入口基类

    提供 LangGraph 工作流管理、数据库连接、流处理等通用功能。
    子类只需要定义工作流结构即可。
    """

    @property
    @abstractmethod
    def _module_name(self) -> str:
        """模式名称"""
        pass

    @abstractmethod
    async def _create_workflow_graph(self) -> CompiledStateGraph:
        """创建工作流图

        子类需要实现具体的工作流结构。

        Args:
            checkpointer: 检查点保存器

        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        pass

    @abstractmethod
    def _prepare_init_state(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """准备初始状态

        子类可以重写此方法来定制初始状态。

        Args:
            state: 原始状态

        Returns:
            Dict[str, Any]: 处理后的初始状态
        """
        pass

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """主入口执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        self._log_module_start(state)

        conversation_id = self._get_conversation_id(state) or "unknown"

        # 准备初始状态
        init_state = self._prepare_init_state(state)

        config = RunnableConfig(configurable={"thread_id": conversation_id})
        try:
            # 创建工作流图
            graph = await self._create_workflow_graph()
            async for chunk in graph.astream(
                init_state,
                config=config,
                stream_mode=["values", "custom"],
                subgraphs=True,
            ):
                if not chunk:
                    continue

                # 处理流式输出
                await self._process_stream_chunk(chunk, writer)

            # 执行工作流

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})

        # 返回到编排器
        self._log_module_end(state)
        return await self._return_to_orchestrator(init_state)

    async def _process_stream_chunk(self, chunk, writer: StreamWriter):
        """处理流式输出块

        Args:
            chunk: 输出块
            writer: 流式输出写入器
        """
        try:
            mode = None

            # 处理元组类型的chunk
            if isinstance(chunk, tuple):
                start, mode, data = chunk
                chunk = data
                logger.debug(
                    f"[{self._module_name}] 处理元组类型chunk: start={start}, mode={mode}"
                )

            # 处理data字段
            if mode == "custom" and isinstance(chunk, dict) and "data" in chunk:
                data_result = chunk["data"]
                writer({"data": data_result})

            # 处理中断
            if mode == "values" and "__interrupt__" in chunk:
                interrupt_obj = chunk["__interrupt__"][0]
                interrupt_value = interrupt_obj.value
                writer({"data": interrupt_value})

        except Exception as e:
            logger.error(f"[{self._module_name}] 处理流式输出异常: {str(e)}")

    def _get_default_init_state(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """获取默认初始状态

        Args:
            state: 原始状态

        Returns:
            Dict[str, Any]: 默认初始状态
        """
        return {
            "userInfo": state.get("userInfo"),
            "messageId": state.get("messageId"),
            "conversationId": state.get("conversationId"),
            "messages": state.get("messages"),
            "action": state.get("action"),
            "guideGotoDemo": state.get("guideGotoDemo"),
            "interrupt_feedback": state.get("interrupt_feedback"),
            "bizRuntimeBO": state.get("bizRuntimeBO"),
            "questionnaire_context": state.get("questionnaire_context", {}),
            "current_step": state.get("current_step", 0),
            "pending_report_task": state.get("pending_report_task"),
        }

    @abstractmethod
    async def _return_to_orchestrator(self, state: Dict[str, Any]) -> Command:
        """返回到编排器

        子类必须实现此方法，因为不同模式有不同的命令更新函数。

        Args:
            state: 当前状态

        Returns:
            Command: 返回编排器的命令
        """
        pass

    def _create_base_workflow(self, state_class, modules: Dict[str, Any]) -> StateGraph:
        """创建基础工作流

        Args:
            state_class: 状态类
            modules: 模块字典

        Returns:
            StateGraph: 状态图
        """
        workflow = StateGraph(state_class)

        # 添加所有模块节点
        for module_name, module_instance in modules.items():
            workflow.add_node(module_name, module_instance)

        return workflow

    def _set_workflow_entry_point(self, workflow: StateGraph, entry_point: str):
        """设置工作流入口点

        Args:
            workflow: 工作流图
            entry_point: 入口点名称
        """
        workflow.set_entry_point(entry_point)

    async def _compile_workflow(self, workflow: StateGraph) -> CompiledStateGraph:
        """编译工作流

        Args:
            workflow: 工作流图
            checkpointer: 检查点保存器

        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        checkpointer = await PGCheckpointClient.get_checkpoint()

        return workflow.compile(
            debug=os.getenv("DEBUG", "False").strip().lower() == "true",
            checkpointer=checkpointer,
        )
