import os
import time
from abc import ABC
from typing import Dict, Any

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.types import Command, StreamWriter

from src.domain.model.ai_chat_model import AiChatResultBO
from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.infrastructure.llm.llm_client import async_create_llm
from ...common.base.base_module import BaseMentorModule
from ...common.services.persistence_service import PersistenceService


# 画像澄清引导语生成提示词
CLARIFICATION_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，专注于用户画像澄清。

# 任务
基于用户之前的问卷回答，生成个性化的画像澄清引导语和澄清点。

# 用户信息
{user_info}

# 问卷回答历史
{questionnaire_history}

# 要求
1. 你的回答**必须严格以以下句子开头，且前面不能有任何其他内容，否则视为错误**：
   "感谢你认真填写个性化专业选择评估问卷！让我回顾一下你告诉我的信息，以便再次确认："
2. **绝对禁止**在开头前添加任何感谢、总结、称呼、铺垫等冗长内容，否则视为错误。
3. 错误示例（严禁出现）：
   错误：感谢您认真填写个性化专业选择评估问卷，张文峰同学！从您的回答中可以看出……
   错误：亲爱的同学，感谢您的配合……
4. 直接根据问卷回答，提取2-4个关键的画像澄清点，每个澄清点应：
   - 准确反映用户在问卷中的回答倾向
   - 具有解释性和深度
   - 便于用户确认或澄清
   - 不要重复感谢、总结或泛泛而谈
5. 澄清点格式示例（无需多余铺垫）：
   "1. 你对“低风险”的侧重点在于职业生涯的长期稳定性和可预测性，而非工作内容的单调性。
   2. 你对我当前推荐的专业列表表示认可，但同时表达了对交叉学科方向的额外兴趣。"
6. 最后用以下文案结尾，不得添加其他内容：
    你是否希望进一步完善、修改上述内容，或者提供更多细节？你可以直接回复补充内容，这样我能更精准地为你生成后续的志愿填报方案。如果没有补充了，可以回复 “确认” 或 “没问题了”，即可开始生成报告...

# 输出格式
请直接输出引导语和澄清点，不需要额外的格式标记。
"""

# 澄清回应分析提示词
RESPONSE_ANALYSIS_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，专注于分析用户的澄清回应。

# 任务
分析用户对画像澄清的回应，判断是否需要进一步澄清。

# 用户信息
{user_info}

# 用户回应
{user_response}

# 澄清点
{clarification_points}

# 问卷回答历史
{questionnaire_history}

# 要求
1. 必须判断用户回应的类型：
   - "完全认同"：用户完全同意所有澄清点
   - "细微修正"：用户基本同意但有小的补充或修正
   - "重大澄清"：用户对某些澄清点有重要的不同意见或补充

2. 如果是"完全认同"，回复确认信息并表示会直接进入下一环节；回复信息里要包含"完全认同"的相关文案；回复信息里不能包含需要用户再次确认的内容
3. 如果是"细微修正"或"重大澄清"，生成感谢回应并记录用户的澄清信息

# 输出格式
请直接输出回应内容，语气要友好、专业。
"""


class BaseNormalUserPersonaClarification(BaseMentorModule, ABC):
    def __init__(self):
        """初始化模块"""
        self.persistence_service = PersistenceService()
        self.max_interactions = 3

    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        """用户画像澄清主要执行逻辑

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 下一步执行命令
        """
        self._log_module_start(state)

        try:
            # 验证必需字段
            if not self._validate_required_fields(
                state, ["conversationId", "userInfo"]
            ):
                return await self._handle_validation_error(state, writer)

            # 获取当前模块上下文
            current_context = self._get_current_context(state)
            current_status = current_context.get("current_status")

            if not current_status:
                # 首次进入，生成澄清内容
                return await self._handle_initial_clarification(state, writer)
            elif current_status == "clarification_sent":
                # 澄清内容已发送，处理用户回应
                return await self._handle_user_response(state, writer)
            else:
                # 其他状态，返回协调器
                return await self._return_to_coordinator(state)

        except Exception as e:
            error_msg = self._handle_error(e, state)
            writer({"data": AiChatResultBO(text=error_msg)})
            return await self._return_to_coordinator(state)

    async def _handle_initial_clarification(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理初始澄清：检查问卷历史并生成澄清内容

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        # 获取问卷历史
        conversation_id = self._get_conversation_id(state)
        if not conversation_id:
            writer({"data": AiChatResultBO(text="会话信息缺失，请重新开始。")})
            self._mark_completed_and_advance(state)
            return await self._return_to_coordinator(state)

        questionnaire_history = self._collect_questionnaire_history(state)

        # if not questionnaire_history:
        #     # 没有问卷历史，跳过澄清环节
        #     writer(
        #         {
        #             "data": AiChatResultBO(
        #                 text="很好！我们已经收集了您的基本信息，现在进入下一个环节。"
        #             )
        #         }
        #     )
        #     self._mark_completed_and_advance(state)
        #     return await self._return_to_coordinator(state)

        # 生成澄清内容
        user_info = self._get_user_info(state)

        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_PLUS_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.1,
            }
        )

        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(CLARIFICATION_PROMPT),
            ]
        )
        chain = prompts | llm
        clarification_content = ""
        async for chunk in chain.astream(
            {
                "user_info": user_info,
                "questionnaire_history": questionnaire_history,
            }
        ):
            if chunk.content:
                clarification_content += chunk.content
                writer({"data": AiChatResultBO(text=chunk.content)})

        # 保存澄清内容并更新状态
        interaction_count = self._get_current_context(state).get("interaction_count", 0)
        self._update_current_context(
            state,
            {
                "clarification_content": clarification_content,
                "interaction_count": interaction_count,
            },
        )
        self._update_context_status(state, "clarification_sent")

        self._log_module_end(state, "clarification_sent")
        return await self._return_to_mentor(state)

    async def _handle_user_response(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理用户回应

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        # 获取用户输入
        user_response = self._get_user_input(state)
        if not user_response:
            writer({"data": AiChatResultBO(text="请告诉我您对上述信息的看法或补充。")})
            return await self._return_to_coordinator(state)

        # 获取澄清点
        current_context = self._get_current_context(state)
        clarification_points = current_context.get("clarification_content", "")

        # 分析用户回应
        user_info = self._get_user_info(state)
        questionnaire_history = self._collect_questionnaire_history(state)

        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_32_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(RESPONSE_ANALYSIS_PROMPT),
            ]
        )
        chain = prompts | llm
        clarification_response = ""
        async for chunk in chain.astream(
            {
                "user_info": user_info,
                "questionnaire_history": questionnaire_history,
                "user_response": user_response,
                "clarification_points": clarification_points,
            }
        ):
            if chunk.content:
                clarification_response += chunk.content
                writer({"data": AiChatResultBO(text=chunk.content)})

        # 保存用户澄清信息
        await self._save_user_clarification(
            state, clarification_points, user_response, clarification_response
        )

        # 检查是否需要进一步澄清
        interaction_count = current_context.get("interaction_count", 0) + 1
        self._update_current_context(
            state,
            {
                "clarification_content": clarification_response,
                "interaction_count": interaction_count,
            },
        )

        if (
            interaction_count >= self.max_interactions
            or "完全认同" in clarification_response
        ):
            # 澄清完成，推进到下一步
            self._mark_completed_and_advance(state)
            return await self._return_to_coordinator(state)

        self._log_module_end(state, "user_response_processed")
        return await self._return_to_mentor(state)

    async def _save_user_clarification(
        self,
        state: Dict[str, Any],
        clarification_content: str,
        user_response: str,
        analysis: str,
    ):
        """保存用户澄清信息

        Args:
            state: 当前状态
            user_response: 用户回应
            analysis: 分析结果
        """
        conversation_id = self._get_conversation_id(state)
        conversation = await AIConversationRepository.get_by_id(int(conversation_id))
        user_features = conversation.user_features or {}
        user_clarifications = user_features.get(self._module_name, {}).get(
            "user_clarifications", []
        )
        new_user_clarification = {
            "clarification_content": clarification_content,
            "response": user_response,
            "analysis": analysis,
            "timestamp": time.time(),
        }
        user_clarifications.append(new_user_clarification)

        await self.persistence_service.save_user_features(
            conversation_id,
            {"user_clarifications": user_clarifications},
            self._module_name,
        )

    async def _handle_validation_error(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        """处理验证错误

        Args:
            state: 当前状态
            writer: 流式输出写入器

        Returns:
            Command: 执行命令
        """
        writer({"data": AiChatResultBO(text="系统信息不完整，请重新开始。")})
        return await self._return_to_coordinator(state)
