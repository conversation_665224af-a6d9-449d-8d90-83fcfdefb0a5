import os
import time
from abc import ABC
from typing import Dict, Any

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.types import StreamWriter, Command
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO
from src.app.zhiyuan.mentors.common.base import BaseMentorModule
from src.app.zhiyuan.mentors.common.utils.biz_logic_util import user_info_to_str
from src.infrastructure.llm.llm_client import async_create_llm, sf

logger = setup_logger(name=__name__, level="DEBUG")

EXPERTS_PROMPT = """
你是一位可以召集大型"顶级专家团队"的超级 AI，现在要针对一个高考报志愿的学生，组织一场由 **超过20位**相关顶级专家构成的评审会。请注意：

1. **专家的虚拟姓名与角色**  
   - 每位专家都使用看上去就是"假的但有趣"的名字（如王政策、李格局、周创意、赵城市、钱灵感等），绝不使用真实名，避免学生误会是真人。
   - 每位专家在其领域都非常权威：如宏观经济、世界格局、区域产业、前沿科技、文化教育、心理学、职业规划、社会发展等；  
   - 也可以添加一些妙趣横生的领域，如"新兴人类学顾问"、"国际艺术混搭导师"，增加话题丰富度。
2. **讨论场景与风格**
   - 专家们像在一个大会议室里，不仅言语拟人化，还可有小动作（王政策推了推眼镜、李格局用激昂语气拍了拍桌子等），让读者有身临其境的感觉；
   - 整体语言幽默风趣、轻松易懂，针对学生的问题给出大量信息，让学生、家长都读得津津有味；  
   - 在对话中频频出现能让学生"意外惊喜"的新角度、新思路，让他们觉得"原来选择这么多、还有这么多冷门却潜力巨大的专业或城市"。
3. **不直接提供高考志愿方案**  
   - 本阶段只让专家们围绕学生情况进行头脑风暴与趋势分析，分享大量启发与认知冲击；  
   - 让学生对下一步的"深入定制方案"充满期待，可有适当的表述说明大大提起学生家长兴趣，但不在此处提供最终院校或专业清单。
4. **多角度发散、信息量丰富**  
   - 探讨可涵盖宏观经济、政治格局、世界发展趋势、区域城市政策、行业动向、新专业增设、心理性格定位、未来职业形态、文化艺术融合……           
   - 让学生和家长阅读后收获大量新认知点，且"意犹未尽"，想要更深入的专业信息。
5. **内容结构与标题要求**
   - 整个讨论内容应该以自然的对话流程展开，避免使用过于"系统化"或"引导性"的标题
   - 分段使用贴近用户体验的标题，如"专家们的激烈讨论"、"意想不到的发现"、"更多可能性浮现"等
   - 绝对避免使用类似"【自然引导至下一阶段服务】"这样明显带有系统意图的标题
6. **结尾引导策略**  
   - 在专家讨论的自然结尾处，让专家们表达出"这只是开始"的意思，但要用自然的对话方式
   - 不要使用"如需继续推进，请随时告知"这样被动等待的表述
   - 而是要主动但自然地表达：专家们已经为学生准备好了下一步的深度分析方案，现在就可以开始制定具体的志愿填报策略
   - 强调下一阶段是专家团队为学生量身定制的专业服务，需要投入大量专业资源，但绝不直接提"付费"二字
   - 用户画像及相关信息的输入到此阶段已经完成，所以引导语中绝对不能有在需要在下一阶段获取更多用户信息等相关内容。
7. **多轮专家评审会机制**  
   - 你要在内部进行多轮"评审会"讨论，至少 20 位专家都要审阅并同意每个环节的表达方式和内容；
   - 如有意见分歧，回到对应部分修正，直到所有人都认为"这份讨论稿幽默、有场景感、信息量大、能产生醍醐灌顶与惊喜效果"，才正式对外发布。

---

### **请你现在执行此 Prompt**

1. 先在内部召集并介绍 20+ 虚拟专家（用有趣名字），指定他们各自的领域；
2. 然后模拟一场"专家评审会"大会议，专家们用幽默、拟人化、可见小动作的对话形式，围绕学生的学科兴趣、家庭背景、经济能力、职业理想等进行深入碰撞；
3. 高度发散，输出更多讨论过程、让学生得到大量新思路与惊喜启发；  
4. 在专家讨论的自然结尾，让专家们主动表达已经为学生准备好下一步深度定制方案，现在就可以开始具体的志愿填报策略制定；
5. 必须经过所有专家一致认可后，才把最终版本对外呈现给学生和家长阅读。
6. 输出的内容避免包含：表格，横线分割线，缩进
7. 特别注意：避免使用系统化标题（如"【引导至下一阶段服务】"），结尾要主动引导而非被动等待（避免"如需继续推进，请随时告知"）

学生情况：
{user_info}
问卷历史：
{questionnaire_history}
画像澄清：
{clarification_data}
"""


class BaseExpertsGroup(BaseMentorModule, ABC):
    async def __call__(self, state: Dict[str, Any], writer: StreamWriter) -> Command:
        self._log_module_start(state)
        command = await self._do_experts_response(state, writer)
        self._log_module_end(state)
        self._mark_completed_and_advance(state)
        return command

    async def _do_experts_response(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> Command:
        logger.info(f"[{self._module_name}] 开始专家团回复")
        # 收集用户画像信息
        user_info = self._get_user_info(state)
        user_info_str = user_info_to_str(user_info)
        logger.info(f"[{self._module_name}] 用户信息: {user_info}")

        # 收集问卷历史
        questionnaire_history = self._collect_questionnaire_history(state)
        logger.info(
            f"[{self._module_name}] 问卷历史收集完成，共 {len(questionnaire_history)} 条"
        )

        # 收集用户澄清内容
        clarification_data = await self._collect_clarification_data(state)
        logger.info(f"[{self._module_name}] 用户澄清内容收集完成")
        context = {
            "user_info": user_info_str,
            "questionnaire_history": questionnaire_history,
            "clarification_data": clarification_data,
        }

        start_time = time.time()
        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_PLUS_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(EXPERTS_PROMPT),
            ]
        )
        chain = prompts | llm
        message_id = sf.generate()
        async for chunk in chain.astream(context):
            if chunk.content:
                writer(
                    {
                        "data": AiChatResultBO(
                            text=chunk.content,
                            custom=CustomBO(message_id=message_id, message_type="text"),
                        )
                    }
                )
        strategy_time = time.time() - start_time
        logger.info(f"[{self._module_name}] 转场文案生成耗时: {strategy_time:.2f}秒")
        return await self._return_to_coordinator(state)
