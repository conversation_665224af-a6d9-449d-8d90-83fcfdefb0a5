from typing import Any, List, Dict
from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")


class MessageUtils:
    """消息处理工具类"""

    @staticmethod
    def extract_user_input(messages: List[Any]) -> str:
        """提取用户最新输入

        Args:
            messages: 消息列表

        Returns:
            str: 用户输入内容
        """
        if not messages:
            return ""

        last_message = messages[-1]

        # 使用 getattr 安全获取 content，兼容 LangChain 消息对象
        user_message = getattr(last_message, "content", str(last_message))
        return user_message if isinstance(user_message, str) else ""

    @staticmethod
    def extract_message_history(
        messages: List[Any], limit: int = 10
    ) -> List[Dict[str, Any]]:
        """提取消息历史

        Args:
            messages: 消息列表
            limit: 数量限制

        Returns:
            List[Dict[str, Any]]: 格式化的消息历史
        """
        try:
            history = []
            recent_messages = messages[-limit:] if len(messages) > limit else messages

            for msg in recent_messages:
                if hasattr(msg, "content"):
                    # <PERSON><PERSON>hain 消息对象
                    history.append(
                        {
                            "content": msg.content,
                            "type": getattr(msg, "type", "unknown"),
                            "role": "user"
                            if getattr(msg, "type", "") == "human"
                            else "assistant",
                        }
                    )
                elif isinstance(msg, dict):
                    # 字典格式消息
                    history.append(
                        {
                            "content": msg.get("content", msg.get("text", "")),
                            "role": msg.get("role", "unknown"),
                            "type": msg.get("type", "text"),
                        }
                    )
                else:
                    # 其他格式
                    history.append(
                        {"content": str(msg), "role": "unknown", "type": "text"}
                    )

            return history

        except Exception as e:
            logger.error(f"提取消息历史失败: {str(e)}")
            return []

    @staticmethod
    def format_conversation_context(messages: List[Any]) -> str:
        """格式化对话上下文

        Args:
            messages: 消息列表

        Returns:
            str: 格式化的对话上下文
        """
        try:
            history = MessageUtils.extract_message_history(messages)

            formatted_parts = []
            for msg in history:
                role = msg.get("role", "unknown")
                content = msg.get("content", "")

                if role == "user":
                    formatted_parts.append(f"用户: {content}")
                elif role == "assistant":
                    formatted_parts.append(f"助手: {content}")
                else:
                    formatted_parts.append(f"{role}: {content}")

            return "\n".join(formatted_parts)

        except Exception as e:
            logger.error(f"格式化对话上下文失败: {str(e)}")
            return ""

    @staticmethod
    def detect_user_intent_keywords(user_input: str) -> List[str]:
        """检测用户意图关键词

        Args:
            user_input: 用户输入

        Returns:
            List[str]: 检测到的意图关键词
        """
        intent_keywords = {
            "report": ["报告", "分析", "结果", "推荐", "建议"],
            "question": ["问题", "疑问", "不明白", "解释", "为什么"],
            "continue": ["继续", "下一步", "开始", "进行"],
            "clarification": ["澄清", "确认", "修正", "补充", "更正"],
            "help": ["帮助", "怎么办", "如何", "指导"],
        }

        detected = []
        user_input_lower = user_input.lower()

        for intent, keywords in intent_keywords.items():
            for keyword in keywords:
                if keyword in user_input_lower:
                    detected.append(intent)
                    break

        return detected

    @staticmethod
    def clean_text_content(text: str) -> str:
        """清理文本内容

        Args:
            text: 原始文本

        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        cleaned = " ".join(text.split())

        # 移除特殊字符（保留基本标点）
        import re

        cleaned = re.sub(r"[^\w\s\u4e00-\u9fff.,!?;:()（）。，！？；：]", "", cleaned)

        return cleaned.strip()

    @staticmethod
    def truncate_text(text: str, max_length: int = 1000, suffix: str = "...") -> str:
        """截断文本

        Args:
            text: 原始文本
            max_length: 最大长度
            suffix: 截断后缀

        Returns:
            str: 截断后的文本
        """
        if not text or len(text) <= max_length:
            return text

        return text[: max_length - len(suffix)] + suffix

    @staticmethod
    def extract_structured_data(text: str, patterns: Dict[str, str]) -> Dict[str, Any]:
        """从文本中提取结构化数据

        Args:
            text: 文本内容
            patterns: 提取模式字典

        Returns:
            Dict[str, Any]: 提取的结构化数据
        """
        import re

        extracted = {}

        for key, pattern in patterns.items():
            try:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    extracted[key] = (
                        match.group(1).strip()
                        if match.groups()
                        else match.group(0).strip()
                    )
                else:
                    extracted[key] = None
            except Exception as e:
                logger.warning(f"提取 {key} 失败: {str(e)}")
                extracted[key] = None

        return extracted
