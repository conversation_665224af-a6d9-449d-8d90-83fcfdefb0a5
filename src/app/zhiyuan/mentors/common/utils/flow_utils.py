from typing import Dict, Any, List, Optional
from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")


class FlowUtils:
    """流程管理工具类"""

    @staticmethod
    def calculate_progress(current_step: int, total_steps: int) -> Dict[str, Any]:
        """计算流程进度

        Args:
            current_step: 当前步骤
            total_steps: 总步骤数

        Returns:
            Dict[str, Any]: 进度信息
        """
        try:
            if total_steps <= 0:
                return {
                    "current_step": 0,
                    "total_steps": 0,
                    "progress_percentage": 0,
                    "is_completed": True,
                }

            # 确保步骤在有效范围内
            current_step = max(0, min(current_step, total_steps))

            progress_percentage = int((current_step / total_steps) * 100)

            return {
                "current_step": current_step,
                "total_steps": total_steps,
                "progress_percentage": progress_percentage,
                "is_completed": current_step >= total_steps,
                "remaining_steps": max(0, total_steps - current_step),
            }

        except Exception as e:
            logger.error(f"计算进度失败: {str(e)}")
            return {
                "current_step": 0,
                "total_steps": 0,
                "progress_percentage": 0,
                "is_completed": False,
            }

    @staticmethod
    def get_next_module(current_step: int, module_sequence: List[str]) -> Optional[str]:
        """获取下一个模块

        Args:
            current_step: 当前步骤
            module_sequence: 模块序列

        Returns:
            Optional[str]: 下一个模块名称，None 表示流程结束
        """
        try:
            if not module_sequence:
                return None

            if current_step < 0 or current_step >= len(module_sequence):
                return None

            return module_sequence[current_step]

        except Exception as e:
            logger.error(f"获取下一个模块失败: {str(e)}")
            return None

    @staticmethod
    def get_previous_module(
        current_step: int, module_sequence: List[str]
    ) -> Optional[str]:
        """获取上一个模块

        Args:
            current_step: 当前步骤
            module_sequence: 模块序列

        Returns:
            Optional[str]: 上一个模块名称，None 表示没有上一个模块
        """
        try:
            if not module_sequence or current_step <= 0:
                return None

            previous_step = current_step - 1
            if previous_step >= len(module_sequence):
                return None

            return module_sequence[previous_step]

        except Exception as e:
            logger.error(f"获取上一个模块失败: {str(e)}")
            return None

    @staticmethod
    def get_completed_modules(
        current_step: int, module_sequence: List[str]
    ) -> List[str]:
        """获取已完成的模块列表

        Args:
            current_step: 当前步骤
            module_sequence: 模块序列

        Returns:
            List[str]: 已完成的模块列表
        """
        try:
            if not module_sequence or current_step <= 0:
                return []

            end_index = min(current_step, len(module_sequence))
            return module_sequence[:end_index]

        except Exception as e:
            logger.error(f"获取已完成模块失败: {str(e)}")
            return []

    @staticmethod
    def get_remaining_modules(
        current_step: int, module_sequence: List[str]
    ) -> List[str]:
        """获取剩余的模块列表

        Args:
            current_step: 当前步骤
            module_sequence: 模块序列

        Returns:
            List[str]: 剩余的模块列表
        """
        try:
            if not module_sequence:
                return []

            if current_step < 0:
                return module_sequence

            if current_step >= len(module_sequence):
                return []

            return module_sequence[current_step:]

        except Exception as e:
            logger.error(f"获取剩余模块失败: {str(e)}")
            return []

    @staticmethod
    def validate_step_transition(
        from_step: int, to_step: int, max_steps: int, allow_skip: bool = False
    ) -> bool:
        """验证步骤转换是否有效

        Args:
            from_step: 源步骤
            to_step: 目标步骤
            max_steps: 最大步骤数
            allow_skip: 是否允许跳过步骤

        Returns:
            bool: 转换是否有效
        """
        try:
            # 检查步骤范围
            if from_step < 0 or to_step < 0:
                return False

            if from_step > max_steps or to_step > max_steps:
                return False

            # 检查转换逻辑
            if allow_skip:
                # 允许跳过，只要是向前或保持不变
                return to_step >= from_step
            else:
                # 不允许跳过，只能前进一步或保持不变
                return to_step == from_step or to_step == from_step + 1

        except Exception as e:
            logger.error(f"验证步骤转换失败: {str(e)}")
            return False

    @staticmethod
    def create_flow_summary(
        module_sequence: List[str],
        current_step: int,
        questionnaire_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """创建流程摘要

        Args:
            module_sequence: 模块序列
            current_step: 当前步骤
            questionnaire_context: 问卷上下文

        Returns:
            Dict[str, Any]: 流程摘要
        """
        try:
            progress = FlowUtils.calculate_progress(current_step, len(module_sequence))
            completed_modules = FlowUtils.get_completed_modules(
                current_step, module_sequence
            )
            remaining_modules = FlowUtils.get_remaining_modules(
                current_step, module_sequence
            )

            # 统计问卷完成情况
            questionnaire_stats = {
                "total_questionnaires": 0,
                "completed_questionnaires": 0,
                "answered_questions": 0,
            }

            if questionnaire_context:
                for module_name, context in questionnaire_context.items():
                    if isinstance(context, dict):
                        questionnaire_stats["total_questionnaires"] += 1

                        if context.get("current_status") == "answered" or context.get(
                            "answer"
                        ):
                            questionnaire_stats["completed_questionnaires"] += 1

                            # 统计回答的问题数
                            answer = context.get("answer", [])
                            if isinstance(answer, list):
                                questionnaire_stats["answered_questions"] += len(answer)

            return {
                "progress": progress,
                "modules": {
                    "total": len(module_sequence),
                    "completed": completed_modules,
                    "remaining": remaining_modules,
                    "current": module_sequence[current_step]
                    if current_step < len(module_sequence)
                    else None,
                },
                "questionnaires": questionnaire_stats,
                "flow_status": "completed"
                if progress["is_completed"]
                else "in_progress",
            }

        except Exception as e:
            logger.error(f"创建流程摘要失败: {str(e)}")
            return {
                "progress": {
                    "current_step": 0,
                    "total_steps": 0,
                    "progress_percentage": 0,
                },
                "modules": {
                    "total": 0,
                    "completed": [],
                    "remaining": [],
                    "current": None,
                },
                "questionnaires": {
                    "total_questionnaires": 0,
                    "completed_questionnaires": 0,
                },
                "flow_status": "error",
            }

    @staticmethod
    def find_module_index(module_name: str, module_sequence: List[str]) -> int:
        """查找模块在序列中的索引

        Args:
            module_name: 模块名称
            module_sequence: 模块序列

        Returns:
            int: 模块索引，-1 表示未找到
        """
        try:
            return module_sequence.index(module_name)
        except ValueError:
            return -1

    @staticmethod
    def can_access_module(
        target_module: str,
        current_step: int,
        module_sequence: List[str],
        allow_backward: bool = False,
    ) -> bool:
        """检查是否可以访问目标模块

        Args:
            target_module: 目标模块
            current_step: 当前步骤
            module_sequence: 模块序列
            allow_backward: 是否允许向后访问

        Returns:
            bool: 是否可以访问
        """
        try:
            target_index = FlowUtils.find_module_index(target_module, module_sequence)
            if target_index == -1:
                return False

            if allow_backward:
                # 允许访问已完成的模块和当前模块
                return target_index <= current_step
            else:
                # 只允许访问当前模块
                return target_index == current_step

        except Exception as e:
            logger.error(f"检查模块访问权限失败: {str(e)}")
            return False
