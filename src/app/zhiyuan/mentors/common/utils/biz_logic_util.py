import re
import time
from datetime import datetime
import json
from typing import Optional, List

from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.models.model import UserInfoDTO
from src.exe.query.AdmissionPolicyQueryExe import AdmissionPolicyQueryExe
from src.infrastructure.db.crud.ai.MajorInfoRepository import MajorInfoRepository
from src.exe.query.school_major_cutoff_query_exe import SchoolMajorCutoffQueryExe
from src.infrastructure.db.models.ai.SchoolMajorCutoff import SchoolMajorCutoff
from src.utils.province_utils import PROVINCE_DICT

logger = setup_logger(name=__name__, level="DEBUG")


def user_info_to_str(user_info: Optional[UserInfoDTO]) -> str:
    """
    从 UserInfoDTO 提取用户信息并格式化为字符串

    Args:
        user_info: 用户信息 DTO 对象

    Returns:
        str: 格式化的用户信息字符串
    """
    if not user_info:
        return "用户信息为空"

    # 定义字段映射：字段名 -> 显示名称
    field_mappings = {
        "username": "姓名",
        "gender_key": "性别",
        "exam_no": "考生号",
        "province_key": "省份",
        "school": "学校",
        "total_score": "总分",
        "sort": "排名",
        "chinese_score": "语文分数",
        "math_score": "数学分数",
        "english_score": "英语分数",
        "physics_score": "物理分数",
        "chemistry_score": "化学分数",
        "biology_score": "生物分数",
        "politics_score": "政治分数",
        "history_score": "历史分数",
        "geography_score": "地理分数",
        "other_score": "其他分数",
    }

    # 收集有值的字段
    info_parts = []

    for field_name, display_name in field_mappings.items():
        value = getattr(user_info, field_name, None)
        if field_name == "province_key":
            value = PROVINCE_DICT.get(value, {}).get("name")

        if value is not None:
            # 处理不同类型的值
            if isinstance(value, (int, float)):
                # 数字类型，保留合适的精度
                if field_name in [
                    "total_score",
                    "chinese_score",
                    "math_score",
                    "english_score",
                    "physics_score",
                    "chemistry_score",
                    "biology_score",
                    "politics_score",
                    "history_score",
                    "geography_score",
                    "other_score",
                ]:
                    # 分数类型，保留1位小数
                    formatted_value = f"{float(value):.1f}"
                else:
                    # 其他数字（如排名），保持整数
                    formatted_value = str(int(value))
            elif isinstance(value, str) and value.strip():
                # 字符串类型，去除首尾空格
                formatted_value = value.strip()
            else:
                # 其他类型，转为字符串
                formatted_value = str(value)

            # 添加到信息列表
            info_parts.append(f"{display_name}：{formatted_value}")

    # 如果没有任何有效信息
    if not info_parts:
        return "暂无有效用户信息"

    # 拼接成最终字符串
    return "\n".join(info_parts)


async def get_admission_strategy_data(province_name: str, conversation_id: str) -> str:
    """获取填报策略数据"""
    try:
        if not province_name:
            return "暂无省份信息"

        strategy_query = f"{province_name}高考志愿填报策略 专业选择建议"
        strategy_content = ""

        async for content in AdmissionPolicyQueryExe.search_admission(
            query_text=strategy_query,
            size=2,
            conversation_id=conversation_id,
        ):
            if content:
                strategy_content += content + "\n"

        return strategy_content if strategy_content else "暂无填报策略数据"

    except Exception as e:
        logger.error(f"[{province_name}] 获取填报策略数据失败: {str(e)}")
        return "填报策略数据获取失败"


async def get_available_majors_data(
    scores: Optional[List[SchoolMajorCutoff]],
    limit: int,
    user_preferences: Optional[List[str]] = None,
) -> str:
    """获取可选专业信息，同时考虑分数和用户喜好"""
    try:
        # 第一阶段：基于分数的专业筛选
        score_based_majors = []
        if scores:
            score_names = "||".join([score_info.major_name for score_info in scores])
            start_time = time.time()
            major_fuzz_result = await MajorInfoRepository.get_by_name_fuzzy(
                major_name=score_names
            )
            generation_time = time.time() - start_time
            logger.info(f"基于用户分数的专业筛选耗时: {generation_time:.2f}秒")
            if major_fuzz_result:
                logger.info(
                    f"基于分数的专业筛选结果：{json.dumps([str(m) for m in major_fuzz_result], ensure_ascii=False)}"
                )
                score_based_majors += major_fuzz_result

        # 如果分数筛选结果太少，获取默认专业列表
        if not score_based_majors or len(score_based_majors) <= 1:
            score_based_majors = await MajorInfoRepository.get_limited_majors(100)
            logger.info(
                f"获取默认专业列表：{json.dumps([str(m) for m in score_based_majors], ensure_ascii=False)}"
            )

        # 第二阶段：基于用户喜好的专业筛选
        preference_based_majors = []
        if user_preferences and len(user_preferences) > 0:
            # 使用批量查询优化
            all_preferences = "||".join(user_preferences)
            start_time = time.time()
            pref_majors = await MajorInfoRepository.get_by_name_fuzzy(
                major_name=all_preferences
            )
            generation_time = time.time() - start_time
            logger.info(f"基于用户喜好的专业筛选耗时: {generation_time:.2f}秒")
            if pref_majors:
                logger.info(
                    f"基于用户喜好的专业筛选结果：{json.dumps([str(m) for m in pref_majors], ensure_ascii=False)}"
                )
                preference_based_majors = pref_majors

        # 合并两种筛选结果，确保喜好专业优先
        final_majors = []

        # 先添加同时满足分数和喜好的专业
        both_match_majors = [
            m for m in preference_based_majors if m in score_based_majors
        ]
        final_majors.extend(both_match_majors)

        # 再添加仅满足分数的专业
        score_only_majors = [
            m for m in score_based_majors if m not in both_match_majors
        ]
        final_majors.extend(score_only_majors)

        # 最后添加仅满足喜好但分数可能不足的专业
        pref_only_majors = [
            m for m in preference_based_majors if m not in both_match_majors
        ]
        final_majors.extend(pref_only_majors)

        # 控制返回数量
        final_majors = final_majors[:limit]

        if final_majors:
            # 为每个专业添加来源标记，帮助LLM理解
            major_arr = []
            for major in final_majors:
                major_info = {"id": major.id, "name": major.major_name}

                # 添加专业来源标记
                if major in both_match_majors:
                    major_info["match_type"] = "both"  # 同时满足分数和喜好
                elif major in score_only_majors:
                    major_info["match_type"] = "score"  # 仅满足分数
                elif major in pref_only_majors:
                    major_info["match_type"] = "preference"  # 仅满足喜好

                major_arr.append(major_info)

            logger.info(
                f"最终可选专业列表：{json.dumps([str(m) for m in major_arr], ensure_ascii=False)}"
            )
            return json.dumps(major_arr, ensure_ascii=False)
        else:
            return "无"
    except Exception as e:
        logger.error(f"获取专业信息失败: {str(e)}")
        return "无"


async def get_admission_scores_record(user_info: UserInfoDTO):
    province_key = getattr(user_info, "province_key", "") if user_info else ""
    province = PROVINCE_DICT.get(province_key, {}) if province_key else {}
    province_name = province.get("name", "") if province else ""
    user_score = user_info.total_score

    if not province_name or not user_score:
        return None, None

    # 根据用户分数范围直接查询相关录取分数数据

    current_year = datetime.now().year
    actual_year = None
    # 定义分数查询范围（用户分数 ± 50分）
    score_range_min = max(0, int(user_score) - 50)
    score_range_max = int(user_score) + 50
    scores = None
    for year in [
        current_year - 1,
        current_year - 2,
        current_year - 3,
    ]:
        try:
            # 直接根据省份、年份、分数范围查询
            scores = await SchoolMajorCutoffQueryExe.query_cutoff_scores_by_range(
                province_name=province_name,
                year=year,
                min_score=score_range_min,
                max_score=score_range_max,
                limit=200,  # 限制返回数量
            )

            if scores:
                actual_year = year
                logger.info(
                    f"获取到{year}年分数范围({score_range_min}-{score_range_max})内录取数据，共{len(scores)}条"
                )
                return actual_year, scores

        except Exception as e:
            logger.warning(f"获取{year}年数据失败: {str(e)}")

        return actual_year, scores


async def get_admission_scores_data(
    scores: Optional[List[SchoolMajorCutoff]], user_info: UserInfoDTO
) -> str:
    """获取录取分数参考"""
    try:
        province_info = PROVINCE_DICT[user_info.province_key]
        province_name = province_info["name"] if province_info else None
        user_score = user_info.total_score

        if not province_name or not user_score:
            return "暂无省份信息或分数信息"
        if not scores:
            return f"{province_name}省暂无近年分数范围录取数据"

        # 按分数排序，分为冲刺、稳妥、保底三个层次
        scores_sorted = sorted(
            scores,
            key=lambda x: int(x.lowest_score) if x.lowest_score else 0,
            reverse=True,
        )

        scores_info = (
            f"{province_name}省专业录取分数参考数据，用户分数：{user_score}）：\n"
        )

        # 分层显示：冲刺、稳妥、保底
        rush_scores = [
            s for s in scores_sorted if int(s.lowest_score) > int(user_score)
        ][:3]  # 冲刺院校
        stable_scores = [
            s for s in scores_sorted if abs(int(s.lowest_score) - int(user_score)) <= 10
        ][:4]  # 稳妥院校
        safe_scores = [
            s for s in scores_sorted if int(s.lowest_score) < int(user_score) - 10
        ][:3]  # 保底院校

        if rush_scores:
            scores_info += "冲刺院校：\n"
            for score in rush_scores:
                diff = int(score.lowest_score) - int(user_score)
                scores_info += f"  - {score.major_name}@{score.school_name}: {score.lowest_score}分 (高{diff}分)\n"

        if stable_scores:
            scores_info += "稳妥院校：\n"
            for score in stable_scores:
                diff = int(score.lowest_score) - int(user_score)
                status = (
                    f"高{diff}分"
                    if diff > 0
                    else f"低{abs(diff)}分"
                    if diff < 0
                    else "持平"
                )
                scores_info += f"  - {score.major_name}@{score.school_name}: {score.lowest_score}分 ({status})\n"

        if safe_scores:
            scores_info += "保底院校：\n"
            for score in safe_scores:
                diff = int(user_score) - int(score.lowest_score)
                scores_info += f"  - {score.major_name}@{score.school_name}: {score.lowest_score}分 (低{diff}分)\n"

        return scores_info

    except Exception as e:
        logger.error(f"获取录取分数失败: {str(e)}")
        return "录取分数数据获取失败"


def extract_user_preferences(career_aspiration: str) -> List[str]:
    """
    从用户职业倾向问卷回答中提取专业喜好

    Args:
        career_aspiration: 用户职业倾向问卷回答

    Returns:
        List[str]: 提取的专业喜好列表
    """
    if not career_aspiration or len(career_aspiration.strip()) == 0:
        return []

    # 专业领域关键词词库
    major_keywords = {
        "计算机": [
            "计算机",
            "软件",
            "编程",
            "人工智能",
            "大数据",
            "网络",
            "信息技术",
            "IT",
            "互联网",
        ],
        "医学": ["医学", "临床医学", "药学", "护理", "医疗", "康复", "健康"],
        "经济": ["经济", "金融", "会计", "财务", "投资", "贸易", "商业"],
        "教育": ["教育", "师范", "教学", "教师", "幼教"],
        "工程": ["工程", "土木", "建筑", "机械", "电气", "自动化", "材料"],
        "艺术": ["艺术", "设计", "音乐", "美术", "传媒", "影视", "动画"],
        "文学": ["文学", "汉语", "外语", "翻译", "新闻", "传播", "出版"],
        "法学": ["法学", "法律", "政治", "行政", "公共管理"],
        "理学": ["数学", "物理", "化学", "生物", "地理", "天文", "环境"],
        "农学": ["农学", "林学", "园艺", "畜牧", "水产", "食品"],
    }

    # 提取专业关键词
    preferences = []
    text = career_aspiration.lower()

    # 1. 直接匹配专业名称
    for category, keywords in major_keywords.items():
        for keyword in keywords:
            if keyword in text:
                # 检查是否有强烈喜好的表述
                strong_preference = False
                for phrase in [
                    "非常喜欢",
                    "特别喜欢",
                    "很喜欢",
                    "理想专业",
                    "梦想专业",
                    "感兴趣",
                ]:
                    if phrase in text and keyword in text.split(phrase)[1][:20]:
                        strong_preference = True
                        break

                # 如果是强烈喜好或者是主要类别，添加到偏好列表
                if strong_preference or keyword == category:
                    if keyword not in preferences:
                        preferences.append(keyword)

    # 2. 使用正则表达式提取更复杂的专业表述
    # 匹配"喜欢xxx专业"或"对xxx专业感兴趣"等模式
    patterns = [
        r"喜欢(?:的|)\s*([^，。,.]*)\s*专业",
        r"对\s*([^，。,.]*)\s*专业感兴趣",
        r"想学\s*([^，。,.]*)\s*专业",
        r"选择\s*([^，。,.]*)\s*专业",
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if match and len(match.strip()) > 1 and match.strip() not in preferences:
                preferences.append(match.strip())

    # 限制返回的偏好数量，优先保留强烈喜好的专业
    return preferences[:5] if preferences else []
