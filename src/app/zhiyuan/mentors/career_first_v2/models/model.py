from typing import Dict, Any, Optional

from langgraph.graph import MessagesState

from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.app.zhiyuan.models.model import UserInfoDTO


class CareerFirstMentorState(MessagesState):
    """状态类，继承自MessagesState并添加next属性。

    Attributes:
        next: 跟踪下一个应该执行的节点
    """

    userInfo: Optional[UserInfoDTO]
    messageId: Optional[str]
    conversationId: Optional[str]
    messages: Optional[list[dict]]
    action: Optional[str]
    guideGotoDemo: Optional[str]
    bizRuntimeBO: Optional[BizRuntimeBO]
    interrupt_feedback: Optional[bool]


async def career_first_mentor_command_update(state: Dict[str, Any]) -> Dict[str, Any]:
    """职业优先模式命令更新函数

    Args:
        state: 当前状态

    Returns:
        Dict[str, Any]: 更新后的状态

    Note:
        questionnaire_context, current_step, user_features, flow_metadata
        现在都在 bizRuntimeBO 中管理，不需要单独返回
    """
    return {
        "userInfo": state.get("userInfo"),
        "messageId": state.get("messageId"),
        "conversationId": state.get("conversationId"),
        "messages": state.get("messages"),
        "action": state.get("action"),
        "guideGotoDemo": state.get("guideGotoDemo"),
        "interrupt_feedback": state.get("interrupt_feedback"),
        "bizRuntimeBO": state.get("bizRuntimeBO"),
    }
