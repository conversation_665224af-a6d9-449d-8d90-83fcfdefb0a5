"""
Career First 流程配置

定义职业优先模式的完整流程配置，包括模块序列、模块配置和流程元数据。
"""

CAREER_FIRST_FLOW = {
    "sequence": [
        "CareerAspirationQuestionnaire",  # 1. 职业倾向问卷
        "MajorSelectionQuestionnaire",  # 2. 专业选择问卷
        "UserPersonaClarification",  # 3. 用户画像澄清
        "CareerFirstExpertsGroup",  # 4. 专家团
        "ReportGeneration",  # 5. 报告生成
    ],
    "module_configs": {
        "CareerAspirationQuestionnaire": {
            "questionnaire_id": "career_preference",
            "message_type": "bipolar",
            "auto_advance": True,
            "timeout_seconds": 300,
            "retry_count": 3,
        },
        "MajorSelectionQuestionnaire": {
            "questionnaire_id": "major_selection",
            "message_type": "likert_table",
            "auto_advance": True,
            "timeout_seconds": 600,
            "retry_count": 3,
        },
        "UserPersonaClarification": {
            "auto_advance": True,
            "max_interactions": 3,
            "timeout_seconds": 300,
        },
        "ReportGeneration": {
            "auto_advance": False,
            "support_query": True,
            "timeout_seconds": 120,
        },
    },
    "flow_metadata": {
        "name": "职业优先模式",
        "description": "基于职业倾向的志愿填报指导流程",
        "version": "2.0.0",
        "total_steps": 4,
        "estimated_duration": "15-20分钟",
        "target_users": ["高中生", "家长"],
        "features": [
            "职业倾向评估",
            "专业匹配推荐",
            "个性化报告生成",
            "交互式澄清对话",
        ],
    },
}
