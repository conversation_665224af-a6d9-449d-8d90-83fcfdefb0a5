"""
Career First 提示词配置

包含各个模块使用的 LLM 提示词模板。
"""

# 职业倾向问卷转场提示词
CAREER_ASPIRATION_TRANSITION_PROMPT = """
基于用户的职业倾向问卷回答，生成一段友好、专业的转场文案，引导用户进入下一个环节。

要求：
1. 总结用户的职业倾向特点
2. 表达对用户回答的感谢
3. 自然过渡到专业选择环节
4. 语气要友好、专业，符合高中生的理解水平

用户信息:
{user_info}

问卷内容:
{questionnaire_questions}

用户问卷回答：
{questionnaire_answers}

请生成一段150-200字的转场文案，直接输出文案内容，不需要额外的格式标记。
"""

# 专业选择问卷转场提示词
MAJOR_SELECTION_TRANSITION_PROMPT = """
基于用户的专业选择问卷回答，生成一段转场文案，引导用户进入画像澄清环节。

要求：
1. 总结用户对专业的偏好特点
2. 表达对用户配合的感谢
3. 自然过渡到画像澄清环节
4. 语气要友好、专业

用户信息:
{user_info}

问卷内容:
{questionnaire_questions}

用户问卷回答：
{questionnaire_answers}

请生成一段100-150字的转场文案，直接输出文案内容。
"""

# 专业推荐生成提示词
MAJOR_RECOMMENDATION_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，专注于根据用户信息和喜好推荐最适合的专业。

# 任务
基于以下信息，为用户推荐5-8个最适合的专业：

## 用户基础信息
{user_info}

## 就业愿景（职业倾向问卷回答）
{career_aspiration}

## 用户专业喜好
{user_preferences}

## 填报策略参考
{admission_strategy}

## 可选专业信息
{available_majors}

## 专业录取分数参考
{admission_scores}

# 要求
1. 推荐5-8个专业，确保专业多样性
2. 优先推荐同时满足分数要求和用户喜好的专业（match_type为"both"）
3. 其次推荐满足分数要求的专业（match_type为"score"）
4. 最后可以推荐1-2个仅满足用户喜好但分数略有不足的专业（match_type为"preference"），并说明需要努力提高分数
5. 每个专业要有简短的推荐理由，包括：
   - 与用户喜好的匹配度
   - 与用户分数的匹配度
   - 就业前景简述
6. 专业名称要准确，与数据库中的专业名称保持一致

# 输出格式
请以JSON格式输出推荐的专业列表：
[
  {{
    "id": 专业ID,
    "name": "专业名称",
    "reason": "推荐理由，包含喜好匹配度和分数匹配度"
  }}
]
"""
