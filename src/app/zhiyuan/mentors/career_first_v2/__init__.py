"""
职业优先模式 v2.0 - 基于通用架构重构

采用新的通用基础架构，提供更简洁、高效的实现。

主要组件：
- CareerFirstMentor: 主入口类
- CareerFirstCoordinator: 协调器
- CareerAspirationQuestionnaire: 职业倾向问卷
- MajorSelectionQuestionnaire: 专业选择问卷
- UserPersonaClarification: 用户画像澄清
- ReportGeneration: 报告生成

技术特性：
- 基于通用架构，代码复用率 80%+
- 配置驱动的流程管理
- 完整的单元测试覆盖
- 类型安全和错误处理
"""

from .career_first_mentor import CareerFirstMentor
from .coordinator import CareerFirstCoordinator
from .modules.career_aspiration_questionnaire import CareerAspirationQuestionnaire
from .modules.major_selection_questionnaire import MajorSelectionQuestionnaire
from .modules.user_persona_clarification import UserPersonaClarification
from .modules.report_generation import ReportGeneration

__all__ = [
    "CareerFirstMentor",
    "CareerFirstCoordinator",
    "CareerAspirationQuestionnaire",
    "MajorSelectionQuestionnaire",
    "UserPersonaClarification",
    "ReportGeneration",
]
