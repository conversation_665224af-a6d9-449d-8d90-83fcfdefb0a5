from typing import Dict, Any
from langgraph.types import Command

from ..common.base.base_coordinator import BaseCoordinator
from .config.flow_config import CAREER_FIRST_FLOW
from .models.model import career_first_mentor_command_update


class CareerFirstCoordinator(BaseCoordinator):
    """职业优先模式协调器 v2.0

    基于通用架构的协调器实现，零业务代码，完全配置驱动。
    """

    def __init__(self):
        """初始化协调器"""
        super().__init__(CAREER_FIRST_FLOW)

    @property
    def _module_name(self) -> str:
        """协调器名称"""
        return "CareerFirstCoordinator"

    async def _get_command_update_function(self, state: Dict[str, Any]):
        """获取命令更新函数

        Args:
            state: 当前状态

        Returns:
            命令更新函数的结果
        """
        return await career_first_mentor_command_update(state)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器（自己）

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CareerFirstCoordinator",
            update=await career_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CareerFirstMentor",
            update=await career_first_mentor_command_update(state),
        )
