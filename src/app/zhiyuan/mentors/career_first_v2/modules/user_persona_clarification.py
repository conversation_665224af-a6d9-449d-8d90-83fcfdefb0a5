from typing import Dict, Any

from langgraph.types import Command

from ..models.model import career_first_mentor_command_update
from ...common.base.base_normal_person_clarification import (
    BaseNormalUserPersonaClarification,
)


class UserPersonaClarification(BaseNormalUserPersonaClarification):
    """用户画像澄清模块 v2.0

    基于通用架构的画像澄清实现，使用统一的服务层。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "UserPersonaClarification"

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CareerFirstCoordinator",
            update=await career_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CareerFirstMentor",
            update=await career_first_mentor_command_update(state),
        )
