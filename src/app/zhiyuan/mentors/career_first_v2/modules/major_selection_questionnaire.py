from typing import Dict, Any

from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.utils.snow_flake import Snowflake
from ..config.prompts import (
    MAJOR_SELECTION_TRANSITION_PROMPT,
)
from ..models.model import (
    career_first_mentor_command_update,
)
from ...common.base.base_major_selection_questionnaire import (
    BaseMajorSelectionQuestionnaire,
)
from ...common.utils.biz_logic_util import (
    user_info_to_str,
    get_admission_scores_record,
    extract_user_preferences,
)

logger = setup_logger(name=__name__, level="DEBUG")

# 创建 Snowflake 实例
sf = Snowflake(worker_id=0, datacenter_id=0)


class MajorSelectionQuestionnaire(BaseMajorSelectionQuestionnaire):
    """专业选择问卷模块 v2.0

    基于通用架构的问卷实现，生成专业评估李克特量表。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "MajorSelectionQuestionnaire"

    @property
    def _questionnaire_id(self) -> str:
        """问卷标识符"""
        return "major_selection"

    @property
    def _transition_prompt(self) -> str:
        """转场提示词"""
        return MAJOR_SELECTION_TRANSITION_PROMPT

    async def _collect_user_data(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """收集用户数据用于专业推荐"""
        user_info = state.get("userInfo")
        if not user_info:
            logger.warning(f"[{self._module_name}] 用户信息为空")
            return {}

        user_info_str = user_info_to_str(user_info)

        # 获取职业倾向问卷答案
        conversation_id = state.get("conversationId")
        career_aspiration = ""
        if conversation_id:
            try:
                conversation = await AIConversationRepository.get_by_id(
                    int(conversation_id)
                )
                if (
                    conversation
                    and hasattr(conversation, "user_features")
                    and conversation.user_features
                ):
                    career_aspiration = conversation.user_features.get(
                        "CareerAspirationQuestionnaire_raw_answer", {}
                    ).get("text", "")
            except Exception as e:
                logger.warning(f"[{self._module_name}] 获取职业倾向数据失败: {str(e)}")

        user_preference = extract_user_preferences(career_aspiration)
        # 获取省份信息
        # province_key = getattr(user_info, "province_key", "") if user_info else ""
        # province = PROVINCE_DICT.get(province_key, {}) if province_key else {}
        # province_name = province.get("name", "") if province else ""

        # 获取填报策略数据
        admission_strategy = ""
        # if province_name and conversation_id:
        #     try:
        #         admission_strategy = await get_admission_strategy_data(
        #             province_name, str(conversation_id)
        #         )
        #     except Exception as e:
        #         logger.warning(f"[{self._module_name}] 获取填报策略失败: {str(e)}")

        actual_year, scores_info = await get_admission_scores_record(user_info)
        # 获取录取分数参考
        # admission_scores = await get_admission_scores_data(scores_info, user_info)
        # available_majors = await get_available_majors_data(
        #     scores_info, 2000, user_preference
        # )

        return {
            "user_info": user_info_str,
            "user_preference": user_preference,
            "career_aspiration": career_aspiration,
            "admission_strategy": admission_strategy,
            # "available_majors": available_majors,
            # "admission_scores": admission_scores,
        }

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CareerFirstCoordinator",
            update=await career_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CareerFirstMentor",
            update=await career_first_mentor_command_update(state),
        )
