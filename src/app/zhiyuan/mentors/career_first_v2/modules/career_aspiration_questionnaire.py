from typing import Dict, Any
from langgraph.types import Command, StreamWriter

from ...common.base.base_questionnaire import BaseQuestionnaire
from ..config.prompts import CAREER_ASPIRATION_TRANSITION_PROMPT
from ..models.model import (
    career_first_mentor_command_update,
)
from src.domain.model.ai_chat_model import AiChatResultBO
from ...common.models.questionnaire_models import (
    QuestionnaireQuestionDetail,
    QuestionnaireScaleLabel,
    QuestionnaireScale,
    QuestionnaireQuestion,
    QuestionnairePole,
)


class CareerAspirationQuestionnaire(BaseQuestionnaire):
    """职业倾向问卷模块 v2.0

    基于通用架构的问卷实现，只需要定义具体的问卷内容和提示词。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "CareerAspirationQuestionnaire"

    @property
    def _questionnaire_id(self) -> str:
        """问卷标识符"""
        return "career_preference"

    @property
    def _transition_prompt(self) -> str:
        """转场提示词"""
        return CAREER_ASPIRATION_TRANSITION_PROMPT

    async def _generate_questionnaire_response(
        self, state: Dict[str, Any], writer: StreamWriter
    ) -> AiChatResultBO:
        """生成职业倾向问卷

        Returns:
            AiChatResultBO: 问卷响应对象
        """
        # 创建问题列表
        question = QuestionnaireQuestionDetail(
            id=self._questionnaire_id,
            title="勾选你所想的就业愿景",
            description="请对于以下各维度选择一个最符合您真实感受的点...",
            scale=QuestionnaireScale(
                type="bipolar",
                displayType="bipolar",
                points=5,
                labels=[
                    QuestionnaireScaleLabel(value=-2, text="完全偏向左侧"),
                    QuestionnaireScaleLabel(value=-1, text="偏向左侧"),
                    QuestionnaireScaleLabel(value=0, text="中立"),
                    QuestionnaireScaleLabel(value=1, text="偏向右侧"),
                    QuestionnaireScaleLabel(value=2, text="完全偏向右侧"),
                ],
            ),
            questions=[
                QuestionnaireQuestion(
                    id="q_value_risk",
                    title="价值 - 风险取向",
                    poles={
                        "left": QuestionnairePole(
                            title="注重确定性/低风险",
                            examples="体制内、公务员、稳定事业单位、国企长期岗",
                        ),
                        "right": QuestionnairePole(
                            title="接受不确定性/追求高潜在收益",
                            examples="初创公司、互联网前沿团队、风险投资、加密资产领域",
                        ),
                    },
                ),
                QuestionnaireQuestion(
                    id="q_motivation_source",
                    title="动机来源",
                    poles={
                        "left": QuestionnairePole(
                            title="更看重兴趣/意义",
                            examples="科研创新、教育、公益、可持续发展项目",
                        ),
                        "right": QuestionnairePole(
                            title="更看重回报/地位",
                            examples="高薪金融、高薪技术行业、顶尖咨询、明星企业头衔",
                        ),
                    },
                ),
                QuestionnaireQuestion(
                    id="q_work_life_balance",
                    title="工作 - 生活取向",
                    poles={
                        "left": QuestionnairePole(
                            title="愿投入大量时间精力",
                            examples="投身创业、投行'996、冲击顶尖科研成果",
                        ),
                        "right": QuestionnairePole(
                            title="强调工作生活平衡",
                            examples="远程/灵活办公、充分年假、朝九晚五国企岗位",
                        ),
                    },
                ),
            ],
        )

        return self.questionnaire_service.create_bipolar_questionnaire_with_vo(question)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="CareerFirstCoordinator",
            update=await career_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="CareerFirstMentor",
            update=await career_first_mentor_command_update(state),
        )

    async def _generate_init_text(self, state: Dict[str, Any], writer: StreamWriter):
        return "你好。你提到你更关注未来能找到什么样的工作，这个思考角度非常成熟！能把职业发展作为志愿填报的核心考量，说明你不仅对自己负责，还对未来社会需求有敏锐洞察力。这种目标导向的思维模式，已经让你站在了多数同龄人的前面。请你先做几张小小的问卷，以便能为你推荐最适合你的志愿填报策略。让我们一起开始吧！"
