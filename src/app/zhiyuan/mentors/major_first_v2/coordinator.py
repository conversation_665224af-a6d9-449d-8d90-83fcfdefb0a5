from typing import Dict, Any
from langgraph.types import Command

from ..common.base.base_coordinator import BaseCoordinator
from .config.flow_config import MAJOR_FIRST_FLOW
from .models.model import major_first_mentor_command_update


class MajorFirstCoordinator(BaseCoordinator):
    """专业优先模式协调器 v2.0

    基于通用架构的协调器实现，零业务代码，完全配置驱动。
    """

    def __init__(self):
        """初始化协调器"""
        super().__init__(MAJOR_FIRST_FLOW)

    @property
    def _module_name(self) -> str:
        """协调器名称"""
        return "MajorFirstCoordinator"

    async def _get_command_update_function(self, state: Dict[str, Any]):
        """获取命令更新函数

        Args:
            state: 当前状态

        Returns:
            命令更新函数的结果
        """
        return await major_first_mentor_command_update(state)

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器（自己）

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="MajorFirstCoordinator",
            update=await major_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="MajorFirstMentor",
            update=await major_first_mentor_command_update(state),
        )
