"""
Major First 提示词配置

包含各个模块使用的 LLM 提示词模板。
"""

# 专业选择问卷转场提示词
MAJOR_SELECTION_TRANSITION_PROMPT = """
基于用户的专业选择问卷回答，生成转场文案，引导用户进入画像澄清环节。

要求：
1. 总结用户对专业的偏好特点
2. 表达对用户配合的感谢
3. 自然过渡到画像澄清环节
4. 语气要友好、专业

用户信息:
{user_info}

问卷内容:
{questionnaire_questions}

用户问卷回答：
{questionnaire_answers}

请生成一段100-150字的转场文案，直接输出文案内容。
"""

# 画像澄清引导语生成提示词
CLARIFICATION_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，专注于专业选择的用户画像澄清。

# 任务
基于用户之前的问卷回答，生成个性化的画像澄清引导语和澄清点。

# 用户信息
{user_info}

# 问卷回答历史
{questionnaire_history}

# 要求
1. 生成友好、专业的引导语，类似：
   "很好！通过前面的问卷，我对您的专业倾向有了初步了解。
   为了给您提供更精准的专业推荐，我想确认几个关键信息。
   请仔细阅读以下几点，告诉我是否准确反映了您的想法，或者您希望补充什么？"

2. 基于问卷回答，提取2-4个关键的画像澄清点，每个澄清点应该：
   - 准确反映用户在问卷中的专业偏好
   - 具有一定的解释性和深度
   - 便于用户确认或澄清
   - 聚焦于专业选择相关的因素

3. 澄清点格式示例：
   "1. 您对理工科专业表现出较强的兴趣，特别是计算机和工程类专业。
   2. 您希望选择就业前景好、薪资水平较高的专业。
   3. 您对专业的学习难度有一定考虑，倾向于选择相对容易掌握的专业。"

# 输出格式
请直接输出引导语和澄清点，不需要额外的格式标记。
"""

# 澄清回应分析提示词
RESPONSE_ANALYSIS_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，专注于分析用户的澄清回应。

# 任务
分析用户对画像澄清的回应，判断是否需要进一步澄清。

# 用户回应
{user_response}

# 澄清点
{clarification_points}

# 要求
1. 判断用户回应的类型：
   - "完全认同"：用户完全同意所有澄清点
   - "细微修正"：用户基本同意但有小的补充或修正
   - "重大澄清"：用户对某些澄清点有重要的不同意见或补充

2. 如果是"完全认同"，回复确认信息并表示进入下一环节
3. 如果是"细微修正"或"重大澄清"，生成感谢回应并记录用户的澄清信息

# 输出格式
请直接输出回应内容，语气要友好、专业。
"""

# 报告生成系统提示词
REPORT_GENERATION_PROMPT = """
# 角色
你是一个专业的高考志愿填报顾问，专注于专业推荐和分析。

# 任务
基于用户的完整信息，生成个性化的专业推荐报告。

# 用户信息
{user_info}

# 问卷回答历史
{questionnaire_history}

# 用户画像澄清
{clarification_data}

# 要求
1. 生成结构化的报告，包含：
   - 专业偏好分析
   - 重点推荐专业（3-5个）
   - 备选专业推荐（3-5个）
   - 专业选择建议
   - 学习规划建议

2. 报告要求：
   - 内容专业、准确
   - 语言通俗易懂
   - 结构清晰，便于阅读
   - 提供具体的行动建议
   - 重点关注专业本身而非职业路径

3. 专业推荐要求：
   - 每个专业包含：专业名称、推荐理由、就业方向、学习要求
   - 根据用户偏好进行个性化排序
   - 提供专业选择的决策建议

4. 格式要求：
   - 使用 Markdown 格式
   - 适当使用标题、列表、表格
   - 控制在 2000-3000 字

# 输出格式
请直接输出 Markdown 格式的报告内容。
"""

# 专业推荐生成提示词
MAJOR_RECOMMENDATION_PROMPT = """
# 角色
你是一个专业的高考志愿填报顾问，专注于基于用户专业偏好的个性化专业推荐。

# 任务
基于用户的专业偏好表达和基本信息，推荐8-12个最适合的专业供用户进一步评估。

# 用户信息
{user_info}

# 用户专业偏好
{initial_preference}

# 可选专业信息
{available_majors}

# 录取分数参考
{admission_scores}

# 填报策略建议
{admission_strategy}

# 要求
1. 专业推荐原则：
   - 优先考虑用户明确表达的专业偏好
   - 结合用户的分数情况和省份特点
   - 兼顾专业的就业前景和发展潜力
   - 提供不同层次的专业选择（冲刺、稳妥、保底）

2. 推荐数量：8-12个专业，确保有足够的选择空间

3. 专业信息要求：
   - 使用标准的专业代码（如 080901）
   - 使用准确的专业名称
   - 优先推荐用户提到的专业及其相关专业
   - 如果用户没有明确偏好，推荐热门且适合的专业

4. 推荐策略：
   - 如果用户提到具体专业：以该专业为核心，推荐相关专业
   - 如果用户提到专业类别：推荐该类别下的优质专业
   - 如果用户表示没想好：推荐就业前景好、适应面广的专业

# 输出格式
请严格按照以下 JSON 格式输出，不要包含任何其他内容：

```json
[
  {
    "id": "080901",
    "name": "计算机科学与技术",
    "category": "工学",
    "match_reason": "与您提到的计算机兴趣高度匹配",
    "score_level": "稳妥"
  },
  {
    "id": "080902",
    "name": "软件工程",
    "category": "工学",
    "match_reason": "计算机相关专业，就业前景优秀",
    "score_level": "稳妥"
  }
]
```

注意：
- 必须返回有效的 JSON 数组
- 每个专业必须包含 id、name、category、match_reason、score_level 字段
- id 使用标准专业代码
- score_level 可以是：冲刺、稳妥、保底
"""
