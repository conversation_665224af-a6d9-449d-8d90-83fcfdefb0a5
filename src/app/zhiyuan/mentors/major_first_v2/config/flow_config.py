"""
Major First 流程配置

定义专业优先模式的完整流程配置，包括模块序列、模块配置和流程元数据。
"""

MAJOR_FIRST_FLOW = {
    "sequence": [
        "InitialPreferenceQuestionnaire",  # 1. 初步意向问卷
        "MajorSelectionQuestionnaire",  # 2. 专业选择问卷
        "UserPersonaClarification",  # 3. 用户画像澄清
        "MajorFirstExpertsGroup",  # 4. 专家团
        "ReportGeneration",  # 5. 报告生成
    ],
    "module_configs": {
        "InitialPreferenceQuestionnaire": {
            "questionnaire_id": "initial_preference",
            "message_type": "text",  # 文本问答类型
            "auto_advance": True,
            "timeout_seconds": 300,
            "retry_count": 2,  # 减少重试次数
            "question_type": "text",  # 文本输入类型
        },
        "MajorSelectionQuestionnaire": {
            "questionnaire_id": "major_selection_preference",
            "message_type": "likert_table",
            "auto_advance": True,
            "timeout_seconds": 600,
            "retry_count": 3,
            "major_count": 10,  # 推荐专业数量
        },
        "UserPersonaClarification": {
            "auto_advance": True,
            "max_interactions": 3,
            "timeout_seconds": 300,
            "clarification_style": "major_focused",  # 专业导向的澄清风格
        },
        "ReportGeneration": {
            "auto_advance": False,
            "support_query": True,
            "timeout_seconds": 120,
            "report_style": "major_recommendation",  # 专业推荐报告风格
        },
    },
    "flow_metadata": {
        "name": "专业优先模式",
        "description": "基于专业兴趣优先的志愿填报指导流程",
        "version": "2.0.0",
        "total_steps": 4,
        "estimated_duration": "10-15分钟",
        "target_users": ["有明确专业倾向的高中生", "家长"],
        "features": [
            "专业意向评估",
            "专业匹配推荐",
            "个性化报告生成",
            "简化流程设计",
            "快速决策支持",
        ],
    },
}
