import time
from typing import Dict, Any
from langgraph.types import Command

from .modules.major_first_experts_group import MajorFirstExpertsGroup
from ..common.base.base_mentor import BaseMentor
from .coordinator import MajorFirstCoordinator
from .modules.initial_preference_questionnaire import InitialPreferenceQuestionnaire
from .modules.major_selection_questionnaire import MajorSelectionQuestionnaire
from .modules.user_persona_clarification import UserPersonaClarification
from .modules.report_generation import ReportGeneration
from .models.model import MajorFirstMentorState, major_first_mentor_command_update
from src.app.zhiyuan.constant import AgentTypeEnum


class MajorFirstMentor(BaseMentor):
    """专业优先模式主入口 v2.0

    基于通用架构的主入口实现，零重复代码，完全配置驱动。
    """

    @property
    def _module_name(self) -> str:
        """模式名称"""
        return "MajorFirstMentor"

    async def _create_workflow_graph(self):
        """创建工作流图

        Args:
            checkpointer: 检查点保存器

        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        # todo sean to heyang 做成单例
        # 创建模块实例
        modules = {
            "MajorFirstCoordinator": MajorFirstCoordinator(),
            "InitialPreferenceQuestionnaire": InitialPreferenceQuestionnaire(),
            "MajorSelectionQuestionnaire": MajorSelectionQuestionnaire(),
            "UserPersonaClarification": UserPersonaClarification(),
            "MajorFirstExpertsGroup": MajorFirstExpertsGroup(),
            "ReportGeneration": ReportGeneration(),
        }

        # 创建基础工作流
        workflow = self._create_base_workflow(MajorFirstMentorState, modules)

        # 设置入口点
        self._set_workflow_entry_point(workflow, "MajorFirstCoordinator")

        # 编译工作流
        return await self._compile_workflow(workflow)

    def _prepare_init_state(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """准备初始状态

        Args:
            state: 原始状态

        Returns:
            Dict[str, Any]: 处理后的初始状态
        """
        # 使用基础状态准备方法
        init_state = self._get_default_init_state(state)

        # 确保 bizRuntimeBO 结构
        self._ensure_biz_runtime_structure(init_state)

        # 添加专业优先模式特定的初始化到 bizRuntimeBO.context
        biz_runtime = init_state["bizRuntimeBO"]
        context = biz_runtime.context
        context["flow_metadata"] = {
            "mode": "major_first",
            "version": "2.0.0",
            "started_at": time.time(),
        }

        return init_state

    async def _return_to_orchestrator(self, state: Dict[str, Any]) -> Command:
        """返回到编排器

        Args:
            state: 当前状态

        Returns:
            Command: 返回编排器的命令
        """
        return Command(
            goto=AgentTypeEnum.ORCHESTRATOR.value,
            update=await major_first_mentor_command_update(state),
        )

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="MajorFirstCoordinator",
            update=await major_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口（自己）

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="MajorFirstMentor",
            update=await major_first_mentor_command_update(state),
        )
