from typing import Dict, Any

from ...common.models.state_models import BaseMentorState


class MajorFirstMentorState(BaseMentorState):
    """专业优先模式状态模型

    继承自统一的基础状态模型，添加专业优先模式特定的字段。
    """

    # 专业优先模式特定字段可以在这里添加
    # 目前使用基础状态模型已经足够
    pass


async def major_first_mentor_command_update(state: Dict[str, Any]) -> Dict[str, Any]:
    """专业优先模式命令更新函数

    Args:
        state: 当前状态

    Returns:
        Dict[str, Any]: 更新后的状态

    Note:
        questionnaire_context, current_step, user_features, flow_metadata
        现在都在 bizRuntimeBO 中管理，不需要单独返回
    """
    return {
        "userInfo": state.get("userInfo"),
        "messageId": state.get("messageId"),
        "conversationId": state.get("conversationId"),
        "messages": state.get("messages"),
        "action": state.get("action"),
        "guideGotoDemo": state.get("guideGotoDemo"),
        "interrupt_feedback": state.get("interrupt_feedback"),
        "bizRuntimeBO": state.get("bizRuntimeBO"),
    }
