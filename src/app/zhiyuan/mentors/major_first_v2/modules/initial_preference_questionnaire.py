"""
初始专业偏好问卷模块 v2.0

专业导向流程的第一个节点，询问用户是否已有心仪的专业方向。
使用简单的文本问答形式，用户回答直接在 message.text 中。
"""

import time
from typing import Dict, Any, Optional

from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from ..models.model import major_first_mentor_command_update
from ...common.base.base_text_questionnaire import BaseTextQuestionnaire

logger = setup_logger(name=__name__, level="DEBUG")


class InitialPreferenceQuestionnaire(BaseTextQuestionnaire):
    """初始专业偏好问卷模块 v2.0

    询问用户是否已有心仪的专业方向，为后续的专业导向流程提供基础信息。
    使用简单的文本问答形式，而不是复杂的结构化问卷。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "InitialPreferenceQuestionnaire"

    @property
    def _questionnaire_id(self) -> str:
        """问卷标识符"""
        return "major_first_preference"

    @property
    def _question_text(self) -> str:
        """问题文本"""
        return "你目前心里是否已经有一些比较心仪或者感兴趣的专业方向了呢？如果有的话，可以告诉我具体是哪个或哪类专业吗？"

    @property
    def _max_retries(self) -> int:
        """最大重试次数"""
        return 2  # 专业偏好问题比较开放，减少重试次数

    async def _validate_answer(
        self, answer: str, state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证用户答案

        Args:
            answer: 用户答案
            state: 当前状态

        Returns:
            Dict[str, Any]: 验证结果
        """
        if not answer or not answer.strip():
            return {"valid": False, "message": "请告诉我您的想法"}

        # 检查答案长度
        # if len(answer.strip()) < 2:
        #     return {"valid": False, "message": "请提供更详细一些的回答"}

        return {"valid": True, "message": ""}

    async def _process_answer(
        self, answer: str, state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理用户答案

        Args:
            answer: 原始答案
            state: 当前状态

        Returns:
            Dict[str, Any]: 处理后的答案数据
        """
        processed_data = {
            "raw_answer": answer,
            "processed_at": time.time(),
        }

        logger.info(f"[{self._module_name}] 处理后的答案数据: {processed_data}")
        return processed_data

    async def _get_confirmation_message(
        self, answer: str, state: Dict[str, Any]
    ) -> Optional[str]:
        """获取确认消息

        Args:
            answer: 用户答案
            state: 当前状态

        Returns:
            Optional[str]: 确认消息
        """
        return "好的，接下来我会帮您进一步明确和分析。"

    async def _get_default_answer(self, state: Dict[str, Any]) -> str:
        """获取默认答案

        Args:
            state: 当前状态

        Returns:
            str: 默认答案
        """
        return "暂时还没有明确的专业方向，希望通过测评来了解"

    async def _get_retry_message_for_empty(
        self, retry_count: int, state: Dict[str, Any]
    ) -> str:
        """获取空答案重试消息

        Args:
            retry_count: 当前重试次数
            state: 当前状态

        Returns:
            str: 重试消息
        """
        if retry_count == 1:
            return "请告诉我您目前的想法，哪怕是一个大致的方向也可以，比如'理工科'、'文科'或者'还没想好'。"
        else:
            return "如果暂时没有想法，您可以直接说'还没想好'，我会通过后续的评估帮您发现合适的专业方向。"

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="MajorFirstCoordinator",
            update=await major_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="MajorFirstMentor",
            update=await major_first_mentor_command_update(state),
        )
