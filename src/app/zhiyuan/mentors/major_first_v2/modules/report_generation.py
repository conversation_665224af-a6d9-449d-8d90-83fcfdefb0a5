from typing import Dict, Any

from langgraph.types import Command
from ..models.model import major_first_mentor_command_update
from src.utils.snow_flake import Snowflake
from lucas_common_components.logging import setup_logger

from ...common.base.base_report_generation import BaseReportGeneration

logger = setup_logger(name=__name__, level="DEBUG")
sf = Snowflake(worker_id=0, datacenter_id=0)


class ReportGeneration(BaseReportGeneration):
    """报告生成模块 v2.0

    基于通用架构的报告生成实现，支持报告生成和查询。
    """

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="MajorFirstCoordinator",
            update=await major_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="MajorFirstMentor",
            update=await major_first_mentor_command_update(state),
        )
