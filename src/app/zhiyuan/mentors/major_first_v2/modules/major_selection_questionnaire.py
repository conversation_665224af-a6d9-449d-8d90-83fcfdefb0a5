import time
from typing import Dict, Any

from langgraph.types import Command
from lucas_common_components.logging import setup_logger

from src.infrastructure.db.crud.ai.AIConversationRepository import (
    AIConversationRepository,
)
from src.utils.snow_flake import Snowflake
from ..config.prompts import (
    MAJOR_SELECTION_TRANSITION_PROMPT,
)
from ..models.model import (
    major_first_mentor_command_update,
)
from ...common.base.base_major_selection_questionnaire import (
    BaseMajorSelectionQuestionnaire,
)
from ...common.utils.biz_logic_util import (
    user_info_to_str,
    get_available_majors_data,
    get_admission_scores_data,
    get_admission_scores_record,
    extract_user_preferences,
)

logger = setup_logger(name=__name__, level="DEBUG")

# 创建 Snowflake 实例
sf = Snowflake(worker_id=0, datacenter_id=0)


class MajorSelectionQuestionnaire(BaseMajorSelectionQuestionnaire):
    """专业选择问卷模块 v2.0

    基于通用架构的问卷实现，根据用户的初始专业偏好生成个性化的专业评估李克特量表。
    """

    @property
    def _module_name(self) -> str:
        """模块名称"""
        return "MajorSelectionQuestionnaire"

    @property
    def _questionnaire_id(self) -> str:
        """问卷标识符"""
        return "major_selection_preference"

    @property
    def _transition_prompt(self) -> str:
        """转场提示词"""
        return MAJOR_SELECTION_TRANSITION_PROMPT

    async def _collect_user_data(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """收集用户数据用于专业推荐"""
        user_info = state.get("userInfo")
        if not user_info:
            logger.warning(f"[{self._module_name}] 用户信息为空")
            return {}

        user_info_str = user_info_to_str(user_info)

        # 获取初始专业偏好问卷答案（来自本模块内部）
        conversation_id = state.get("conversationId")
        initial_preference = ""

        conversation = await AIConversationRepository.get_by_id(int(conversation_id))
        if (
            conversation
            and hasattr(conversation, "user_features")
            and conversation.user_features
        ):
            # 获取初始偏好的原始答案
            initial_preference = conversation.user_features.get(
                "major_first_preference_raw_answer", ""
            )

        # 获取省份信息
        # province_key = getattr(user_info, "province_key", "") if user_info else ""
        # province = PROVINCE_DICT.get(province_key, {}) if province_key else {}
        # province_name = province.get("name", "") if province else ""

        # 获取填报策略数据
        start_time = time.time()
        admission_strategy = ""
        # if province_name and conversation_id:
        #     try:
        #         admission_strategy = await get_admission_strategy_data(
        #             province_name, str(conversation_id)
        #         )
        #     except Exception as e:
        #         logger.warning(f"[{self._module_name}] 获取填报策略失败: {str(e)}")
        # generation_time = time.time() - start_time
        # logger.info(f"[{self._module_name}] 获取填报策略耗时: {generation_time:.2f}秒")

        # 获取录取分数和专业信息
        start_time = time.time()
        actual_year, scores_info = await get_admission_scores_record(user_info)
        generation_time = time.time() - start_time
        logger.info(
            f"[{self._module_name}] 获取录取分数信息耗时: {generation_time:.2f}秒"
        )

        start_time = time.time()
        admission_scores = await get_admission_scores_data(scores_info, user_info)
        generation_time = time.time() - start_time
        logger.info(
            f"[{self._module_name}] 分析录取分数信息耗时: {generation_time:.2f}秒"
        )

        user_preference = extract_user_preferences(initial_preference)

        start_time = time.time()
        available_majors = await get_available_majors_data(
            scores_info, 2000, user_preference
        )
        generation_time = time.time() - start_time
        logger.info(
            f"[{self._module_name}] 获取专业信息列表耗时: {generation_time:.2f}秒"
        )

        return {
            "user_info": user_info_str,
            "user_preference": user_preference,
            "career_aspiration": initial_preference,
            "admission_strategy": admission_strategy,
            "available_majors": available_majors,
            "admission_scores": admission_scores,
        }

    async def _return_to_coordinator(self, state: Dict[str, Any]) -> Command:
        """返回协调器

        Args:
            state: 当前状态

        Returns:
            Command: 返回协调器的命令
        """
        return Command(
            goto="MajorFirstCoordinator",
            update=await major_first_mentor_command_update(state),
        )

    async def _return_to_mentor(self, state: Dict[str, Any]) -> Command:
        """返回主入口

        Args:
            state: 当前状态

        Returns:
            Command: 返回主入口的命令
        """
        return Command(
            goto="MajorFirstMentor",
            update=await major_first_mentor_command_update(state),
        )
