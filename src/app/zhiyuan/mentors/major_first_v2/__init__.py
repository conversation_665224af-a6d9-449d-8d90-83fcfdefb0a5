"""
专业优先模式 v2.0 - 基于通用架构重构

采用新的通用基础架构，提供更简洁、高效的实现。

主要组件：
- MajorFirstMentor: 主入口类
- MajorFirstCoordinator: 协调器
- InitialPreferenceQuestionnaire: 初步意向问卷
- MajorSelectionQuestionnaire: 专业选择问卷
- UserPersonaClarification: 用户画像澄清
- ReportGeneration: 报告生成

技术特性：
- 基于通用架构，代码复用率 80%+
- 配置驱动的流程管理
- 简化的4步流程设计
- 完整的单元测试覆盖
- 类型安全和错误处理

流程设计：
1. 初步意向问卷 - 询问用户是否有专业意向
2. 专业选择问卷 - 评估用户对具体专业的偏好
3. 用户画像澄清 - 澄清用户需求和偏好
4. 报告生成 - 生成个性化专业推荐报告
"""

from .major_first_mentor import MajorFirstMentor
from .coordinator import MajorFirstCoordinator
from .modules.initial_preference_questionnaire import InitialPreferenceQuestionnaire
from .modules.major_selection_questionnaire import MajorSelectionQuestionnaire
from .modules.user_persona_clarification import UserPersonaClarification
from .modules.report_generation import ReportGeneration

__all__ = [
    "MajorFirstMentor",
    "MajorFirstCoordinator",
    "InitialPreferenceQuestionnaire",
    "MajorSelectionQuestionnaire",
    "UserPersonaClarification",
    "ReportGeneration",
]
