import asyncio
import time
import random
from typing import Dict, Any

from lucas_common_components.logging import setup_logger

# 导入DTO

# 创建logger
logger = setup_logger(__name__)


class MajorAdmissionService:
    """专业录取分数线数据服务"""

    # 添加静态标志，用于跟踪任务状态
    _task_running = False
    _current_task_info = {
        "year": None,
        "start_time": None,
        "provinces_processed": 0,
        "total_provinces": 0,
    }

    def __init__(self, rpc_client, command_exe):
        """
        初始化专业录取分数线数据服务

        Args:
            rpc_client: RPC客户端，用于调用外部API
            command_exe: 命令执行器，用于数据库操作
        """
        self.rpc_client = rpc_client
        self.command_exe = command_exe

    @classmethod
    def is_task_running(cls) -> bool:
        """
        检查任务是否正在运行

        Returns:
            bool: 任务是否正在运行
        """
        return cls._task_running

    @classmethod
    def get_task_info(cls) -> Dict[str, Any]:
        """
        获取当前任务信息

        Returns:
            Dict[str, Any]: 当前任务信息
        """
        if not cls._task_running:
            return {"status": "idle"}

        elapsed_time = 0
        if cls._current_task_info["start_time"]:
            elapsed_time = time.time() - cls._current_task_info["start_time"]

        return {
            "status": "running",
            "year": cls._current_task_info["year"],
            "elapsed_seconds": round(elapsed_time, 2),
            "provinces_processed": cls._current_task_info["provinces_processed"],
            "total_provinces": cls._current_task_info["total_provinces"],
        }

    async def collect_all_data_by_province_no_checkpoint(
        self, year: int, batch_size: int = 100, max_workers: int = 5
    ) -> Dict[str, Any]:
        """
        按省份采集指定年份的专业录取分数线数据（无断点续传功能）

        Args:
            year: 要采集的年份
            batch_size: 每页记录数
            max_workers: 并行工作线程数 (不再使用)

        Returns:
            包含采集结果的字典
        """
        # 检查是否有任务正在运行
        if MajorAdmissionService._task_running:
            task_info = MajorAdmissionService.get_task_info()
            return {
                "success": False,
                "message": f"已有任务正在运行，无法启动新任务。当前任务：采集{task_info['year']}年数据，已处理{task_info['provinces_processed']}/{task_info['total_provinces']}个省份，运行时间{task_info['elapsed_seconds']}秒",
                "total_count": 0,
                "saved_count": 0,
                "provinces_count": 0,
                "execution_time_seconds": 0,
                "task_info": task_info,
            }

        try:
            # 设置任务状态为运行中
            MajorAdmissionService._task_running = True
            start_time = time.time()
            total_count = 0
            saved_count = 0

            # 定义省份枚举列表
            provinces = [
                "上海",
                "云南",
                "内蒙古",
                "北京",
                "台湾",
                "吉林",
                "四川",
                "天津",
                "宁夏",
                "安徽",
                "山东",
                "山西",
                "广东",
                "广西",
                "新疆",
                "江苏",
                "江西",
                "河北",
                "河南",
                "浙江",
                "海南",
                "湖北",
                "湖南",
                "甘肃",
                "福建",
                "西藏",
                "贵州",
                "辽宁",
                "重庆",
                "陕西",
                "青海",
                "香港",
                "黑龙江",
            ]

            # 更新任务信息
            MajorAdmissionService._current_task_info = {
                "year": year,
                "start_time": start_time,
                "provinces_processed": 0,
                "total_provinces": len(provinces),
            }

            logger.info(f"开始按省份采集{year}年数据，共{len(provinces)}个省份")

            # 定义单个省份处理函数
            async def process_province(province):
                max_retries = 2
                province_total = 0
                province_saved = 0
                current_page = 1
                total_pages = 1
                is_complete = True  # 默认假设能完成所有页面

                logger.info(f"开始采集省份: {province}")

                while True:
                    # TODO try catch
                    # 1. 获取当前页数据
                    page_result = None
                    for retry in range(max_retries + 1):
                        try:
                            page_result = await self.rpc_client.fetch_page_data(
                                page_index=current_page,
                                batch_size=batch_size,
                                enrollprovince=province,
                                year=year,
                            )

                            if page_result.success:
                                break

                            logger.warning(
                                f"采集{province}省份第{current_page}页数据失败: {page_result.message}，重试 {retry + 1}/{max_retries + 1}"
                            )
                            await asyncio.sleep(1 * (retry + 1))
                        except Exception as e:
                            logger.error(
                                f"处理{province}省份第{current_page}页时发生异常: {str(e)}，重试 {retry + 1}/{max_retries + 1}"
                            )
                            await asyncio.sleep(1 * (retry + 1))

                    # 2. 处理获取结果
                    # 如果获取失败，标记为不完整并退出循环
                    if not page_result or not page_result.success:
                        logger.error(
                            f"{province}省份第{current_page}页数据获取失败，停止处理"
                        )
                        is_complete = False
                        break

                    # 3. 更新总页数信息（仅在第一页）
                    if current_page == 1:
                        province_total = page_result.total_count
                        total_pages = page_result.total_pages
                        logger.info(
                            f"{province}省份共有{province_total}条数据，共{total_pages}页"
                        )

                        # 如果没有数据，直接返回成功
                        if province_total == 0:
                            return {
                                "province": province,
                                "success": True,
                                "total_count": 0,
                                "saved_count": 0,
                                "message": f"{province}省份无数据",
                            }

                    # 4. 保存当前页数据
                    if page_result.data:
                        logger.info(
                            f"获取{province}省份第{current_page}/{total_pages}页数据，本页{len(page_result.data)}条"
                        )
                        # 这是原来的逐条插入数据库：page_saved = await self.command_exe.save_batch_data(page_result.data)
                        page_saved = await self.command_exe.save_batch_data_bulk(
                            page_result.data
                        )
                        province_saved += page_saved
                        logger.info(
                            f"第{current_page}页数据保存成功，新增{page_saved}条记录"
                        )

                    # 5. 是否完成所有页面
                    if current_page >= total_pages:
                        break

                    # 6. 继续处理下一页
                    current_page += 1
                    await asyncio.sleep(0.5 + random.uniform(0.2, 0.5))

                # 检查处理结果
                processed_pages = (
                    current_page if current_page <= total_pages else total_pages
                )

                if is_complete:
                    logger.info(
                        f"成功采集{province}省份数据，总数据量: {province_total}，保存数量: {province_saved}"
                    )
                    return {
                        "province": province,
                        "success": True,
                        "total_count": province_total,
                        "saved_count": province_saved,
                        "message": f"成功采集{province}省份数据",
                    }
                else:
                    logger.warning(
                        f"{province}省份数据采集不完整，已处理{processed_pages}/{total_pages}页"
                    )
                return {
                    "province": province,
                    "success": False,
                    "total_count": province_total,
                    "saved_count": province_saved,
                    "message": f"部分采集{province}省份数据，已处理{processed_pages}/{total_pages}页",
                }

            # 改为串行处理所有省份
            provinces_results = {}
            processed_count = 0

            for province in provinces:
                # 处理单个省份
                result = await process_province(province)
                province_name = result.get("province")
                processed_count += 1

                # 更新任务信息
                MajorAdmissionService._current_task_info["provinces_processed"] = (
                    processed_count
                )

                # 更新计数
                if result.get("success", False):
                    province_total = result.get("total_count", 0)
                    province_saved = result.get("saved_count", 0)
                    total_count += province_total
                    saved_count += province_saved
                    provinces_results[province_name] = {
                        "success": True,
                        "total_count": province_total,
                        "saved_count": province_saved,
                    }
                else:
                    provinces_results[province_name] = {
                        "success": False,
                        "message": result.get("message", "未知错误"),
                    }

                # 记录进度
                logger.info(
                    f"已处理 {processed_count}/{len(provinces)} 个省份，当前: {province_name}"
                )

            end_time = time.time()
            execution_time = end_time - start_time

            # 计算成功和失败的省份
            success_provinces = [
                p
                for p in provinces_results
                if provinces_results[p].get("success", False)
            ]
            failed_provinces = [
                p
                for p in provinces_results
                if not provinces_results[p].get("success", False)
            ]

            # 最终结果
            result = {
                "success": len(failed_provinces) == 0,
                "message": f"完成按省份采集{year}年数据，成功{len(success_provinces)}个省份，失败{len(failed_provinces)}个省份",
                "total_count": total_count,
                "saved_count": saved_count,
                "provinces_count": len(provinces),
                "success_provinces_count": len(success_provinces),
                "failed_provinces_count": len(failed_provinces),
                "failed_provinces": failed_provinces,
                "provinces_results": provinces_results,
                "execution_time_seconds": execution_time,
            }

            # 记录日志
            logger.info(
                f"采集统计：总计{total_count}条数据，成功保存{saved_count}条，耗时{execution_time:.2f}秒"
            )
            logger.info(
                f"省份统计：总计{len(provinces)}个省份，成功{len(success_provinces)}个，失败{len(failed_provinces)}个"
            )
            if failed_provinces:
                logger.warning(f"失败省份列表: {', '.join(failed_provinces)}")

            return result

        except Exception as e:
            logger.exception(f"按省份采集{year}年数据时出错: {str(e)}")
            return {
                "success": False,
                "message": f"按省份采集{year}年数据时出错: {str(e)}",
                "total_count": total_count if "total_count" in locals() else 0,
                "saved_count": saved_count if "saved_count" in locals() else 0,
                "execution_time_seconds": time.time() - start_time
                if "start_time" in locals()
                else 0,
            }
        finally:
            # 无论成功还是失败，最后都要重置任务状态
            MajorAdmissionService._task_running = False
            MajorAdmissionService._current_task_info = {
                "year": None,
                "start_time": None,
                "provinces_processed": 0,
                "total_provinces": 0,
            }
