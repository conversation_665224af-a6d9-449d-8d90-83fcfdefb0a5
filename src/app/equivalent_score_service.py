from typing import Dict, Optional

from lucas_common_components.logging import setup_logger

from src.exe.command.equivalent_score_calculator import EquivalentScoreCalculator


class EquivalentScoreService:
    """等效分计算服务"""

    def __init__(self):
        """
        初始化等效分计算服务
        """
        self.logger = setup_logger(__name__)

        # 使用固定的计算器
        self.calculator = EquivalentScoreCalculator()

        # 直接使用计算器中的预测仓储
        self.prediction_repo = self.calculator.prediction_repo

    async def calculate_equivalent_scores(self, old_year: int, new_year: int) -> Dict:
        """
        计算等效分

        Args:
            old_year: 旧年份
            new_year: 新年份

        Returns:
            Dict: 计算结果
        """
        self.logger.info(f"开始计算从 {old_year} 年到 {new_year} 年的等效分")

        # 调用计算器进行计算
        result = await self.calculator.calculate_all_equivalent_scores(
            old_year, new_year
        )

        self.logger.info(
            f"等效分计算完成，处理了 {result.get('processed_count', 0)} 个学校专业"
        )
        return result

    async def get_school_major_equivalent_score(
        self, school_name: str, major_name: str, year: int
    ) -> Optional[Dict]:
        """
        获取学校专业的等效分信息

        Args:
            school_name: 学校名称
            major_name: 专业名称
            year: 预测年份

        Returns:
            Optional[Dict]: 等效分信息，如果不存在则返回None
        """
        self.logger.info(f"查询 {school_name} - {major_name} - {year} 的等效分预测结果")

        result = await self.calculator.prediction_repo.get_by_school_major(
            school_name, major_name, year
        )

        if result:
            self.logger.info(
                f"找到等效分预测结果，包含 {len(result.get('province_scores', []))} 个省份数据"
            )
        else:
            self.logger.warning("未找到等效分预测结果")

        return result
