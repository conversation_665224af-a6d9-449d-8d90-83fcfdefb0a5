"""
AI聊天服务

重构后的AI聊天服务，遵循DDD架构规范：
1. 使用Gateway接口而不是直接依赖Infrastructure层
2. 使用Cmd对象而不是直接依赖Adapter层的VO
3. 职责单一，专注于业务编排
4. 依赖注入，便于测试和扩展
"""

from typing import Optional, Dict, Any, AsyncGenerator
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command
from lucas_common_components.logging import setup_logger
from sse_starlette.sse import AsyncContentStream

from src.domain.gateway.ai_chat_gateway import AIChatGateway
from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO, ReportContentResultBO
from src.app.zhiyuan.orchestrator import get_orchestrator
from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.utils.snow_flake import Snowflake

from .contracts.chat_cmd import ChatCmd, MessageCmd, ChatProgressQueryCmd
from .contracts.constants import MessageType, ErrorMessages
from .processors.streaming_processor import StreamingChatProcessor
from .processors.progress_query_processor import ProgressQueryProcessor
from .processors.chat_state_builder import ChatStateBuilder

logger = setup_logger(name=__name__, level="DEBUG")


class AIChatService:
    """AI聊天服务
    
    负责AI聊天流程的业务编排，包括：
    - 正常聊天处理
    - 进度查询处理
    - 流程动作处理
    """

    def __init__(self, ai_chat_gateway: AIChatGateway, snowflake: Snowflake):
        """初始化AI聊天服务
        
        Args:
            ai_chat_gateway: AI聊天Gateway接口
            snowflake: 雪花算法ID生成器
        """
        self.gateway = ai_chat_gateway
        self.sf = snowflake
        self.streaming_processor = StreamingChatProcessor(ai_chat_gateway, snowflake)
        self.progress_query_processor = ProgressQueryProcessor(ai_chat_gateway)
        self.state_builder = ChatStateBuilder(ai_chat_gateway, snowflake)

    async def handle_chat(self, chat_cmd: ChatCmd, uniq_user_id: str) -> AsyncContentStream:
        """处理正常聊天请求
        
        Args:
            chat_cmd: 聊天命令对象
            uniq_user_id: 唯一用户ID
            
        Returns:
            AsyncContentStream: 流式响应
        """
        if not chat_cmd.conversation_id:
            raise ValueError(ErrorMessages.CONVERSATION_ID_REQUIRED)

        # 获取会话动作
        action = await self._get_conversation_action(chat_cmd.conversation_id)
        
        async for result in self._handle_normal_chat(chat_cmd, uniq_user_id, action):
            yield result

    async def handle_chat_progress_query(
        self, 
        progress_cmd: ChatProgressQueryCmd, 
        uniq_user_id: str
    ) -> AsyncContentStream:
        """处理聊天进度查询
        
        Args:
            progress_cmd: 进度查询命令对象
            uniq_user_id: 唯一用户ID
            
        Returns:
            AsyncContentStream: 流式响应
        """
        if not progress_cmd.conversation_id:
            raise ValueError(ErrorMessages.CONVERSATION_ID_REQUIRED)
            
        async for result in self.progress_query_processor.handle_progress_query(progress_cmd, uniq_user_id):
            yield result

    async def _get_conversation_action(self, conversation_id: str) -> Optional[str]:
        """获取会话动作类型
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Optional[str]: 会话动作类型
        """
        ai_conversation = await self.gateway.get_conversation_by_id(int(conversation_id))
        if ai_conversation and hasattr(ai_conversation, 'context'):
            return ai_conversation.context.get("conversation_action")
        return None

    async def _handle_normal_chat(
        self, 
        chat_cmd: ChatCmd, 
        uniq_user_id: str, 
        action: Optional[str]
    ) -> AsyncGenerator[str, None]:
        """处理正常聊天流程
        
        Args:
            chat_cmd: 聊天命令对象
            uniq_user_id: 唯一用户ID
            action: 会话动作类型
            
        Yields:
            str: 流式响应数据
        """
        conversation_id = int(chat_cmd.conversation_id)
        
        try:
            # 更新会话时间戳
            await self.gateway.update_conversation_timestamp(conversation_id)
            
            # 处理中断反馈
            if chat_cmd.interrupt_feedback:
                await self._handle_interrupt_feedback(conversation_id)
                return

            # 保存用户消息
            await self._save_user_messages(chat_cmd)

            # 构建初始状态
            init_state = await self.state_builder.build_init_state(chat_cmd, uniq_user_id, action)

            # 获取orchestrator并处理流式响应
            orchestrator = await get_orchestrator()
            config = RunnableConfig(configurable={"thread_id": chat_cmd.conversation_id})
            
            # 创建流式数据生成器
            stream_generator = orchestrator.astream(
                init_state,
                config=config,
                stream_mode=["custom", "values"],
                subgraphs=True,
            )

            # 使用流式处理器处理响应
            async for result in self.streaming_processor.process_stream_chunks(
                stream_generator, conversation_id
            ):
                yield result

        except Exception as e:
            logger.error(f"{ErrorMessages.INITIALIZATION_ERROR}: {str(e)}", exc_info=True)
            # 返回错误消息
            error_result = AiChatResultBO(text=f"系统错误: {str(e)}")
            yield error_result.model_dump_json(exclude_none=True)



    async def _handle_interrupt_feedback(self, conversation_id: int) -> None:
        """处理中断反馈
        
        Args:
            conversation_id: 会话ID
        """
        orchestrator = await get_orchestrator()
        config = RunnableConfig(configurable={"thread_id": str(conversation_id)})
        
        async for chunk in orchestrator.astream(
            Command(resume="This is my feedback!"),
            config=config,
            stream_mode=["custom", "values"],
            subgraphs=True,
        ):
            if chunk:
                logger.info(f"interrupt_feedback_chunk: {chunk}")

    async def _save_user_messages(self, chat_cmd: ChatCmd) -> None:
        """保存用户消息到数据库
        
        Args:
            chat_cmd: 聊天命令对象
        """
        conversation_id = int(chat_cmd.conversation_id)
        
        # 转换为保存格式
        message_data_list = []
        for message_cmd in chat_cmd.messages:
            if message_cmd.text:
                message_data = {
                    "id": self.sf.generate(),
                    "conversation_id": conversation_id,
                    "role": message_cmd.role or "user",
                    "message_type": MessageType.TEXT,
                    "message_data": {"text": message_cmd.text},
                }
                message_data_list.append(message_data)
        
        if message_data_list:
            await self.gateway.save_user_message(conversation_id, message_data_list)

 