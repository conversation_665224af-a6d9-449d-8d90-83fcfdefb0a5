"""
消息处理器抽象基类

定义所有消息处理器的统一接口。
每个处理器负责管理特定类型消息的状态和持久化逻辑。
"""

from abc import ABC, abstractmethod
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO
from src.domain.gateway.ai_chat_gateway import AIChatGateway
from src.utils.snow_flake import Snowflake

logger = setup_logger(name=__name__, level="DEBUG")


class BaseMessageHandler(ABC):
    """消息处理器抽象基类
    
    定义所有消息处理器的统一接口。
    每个处理器负责管理特定类型消息的状态和持久化逻辑。
    """

    def __init__(
        self, 
        initial_chunk: AiChatResultBO, 
        conversation_id: int,
        gateway: AIChatGateway,
        snowflake: Snowflake
    ):
        self.conversation_id = conversation_id
        self.gateway = gateway
        self.message_id = self._get_or_generate_message_id(initial_chunk, snowflake)
        
        # 确保custom对象存在并设置message_id
        if initial_chunk.custom is None:
            initial_chunk.custom = CustomBO(
                message_type=self.get_message_type(), 
                message_id=str(self.message_id)
            )
        else:
            initial_chunk.custom.message_id = str(self.message_id)
        
        # 处理初始数据块
        self._process_initial_chunk(initial_chunk)

    def _get_or_generate_message_id(self, chunk: AiChatResultBO, snowflake: Snowflake) -> int:
        """获取或生成消息ID"""
        if chunk.custom and chunk.custom.message_id:
            try:
                return int(chunk.custom.message_id)
            except (ValueError, TypeError):
                pass
        return snowflake.generate()

    @abstractmethod
    def get_message_type(self) -> str:
        """返回此处理器对应的消息类型"""
        pass

    @abstractmethod
    def _process_initial_chunk(self, chunk: AiChatResultBO) -> None:
        """处理初始数据块（同步部分）"""
        pass

    async def initialize_async(self, initial_chunk: AiChatResultBO) -> None:
        """异步初始化方法，用于处理需要异步操作的初始化逻辑"""
        # 子类可以重写此方法来执行异步初始化
        pass

    @abstractmethod
    async def accumulate(self, chunk: AiChatResultBO) -> None:
        """累积新的数据块"""
        pass

    @abstractmethod
    async def save(self) -> None:
        """保存消息到持久化存储"""
        pass 