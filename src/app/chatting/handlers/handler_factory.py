"""
消息处理器工厂

工厂方法：根据消息类型创建对应的处理器实例。
"""

from src.domain.model.ai_chat_model import AiChatResultBO
from src.domain.gateway.ai_chat_gateway import AIChatGateway
from ..contracts.constants import MessageType
from src.utils.snow_flake import Snowflake
from .base_handler import BaseMessageHandler
from .text_handler import TextMessageHandler
from .custom_handler import CustomMessageHandler


async def create_message_handler(
    chunk: AiChatResultBO,
    conversation_id: int,
    gateway: AIChatGateway,
    snowflake: Snowflake
) -> BaseMessageHandler:
    """工厂方法：根据消息类型创建对应的处理器
    
    Args:
        chunk: AI聊天结果BO对象
        conversation_id: 会话ID
        gateway: AI聊天Gateway接口
        snowflake: 雪花算法ID生成器
        
    Returns:
        BaseMessageHandler: 对应的消息处理器实例
    """
    message_type = chunk.custom.message_type if chunk.custom else MessageType.TEXT
    
    if message_type == MessageType.TEXT:
        handler = TextMessageHandler(chunk, conversation_id, gateway, snowflake)
    else:
        handler = CustomMessageHandler(chunk, conversation_id, gateway, snowflake)
    
    # 执行异步初始化
    await handler.initialize_async(chunk)
    return handler 