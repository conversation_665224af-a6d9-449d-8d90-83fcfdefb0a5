"""
自定义消息处理器

采用即时更新策略，每次收到数据块都立即保存。
"""

from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO
from src.domain.gateway.ai_chat_gateway import AIChatGateway
from ..contracts.constants import MessageType
from src.utils.snow_flake import Snowflake
from .base_handler import BaseMessageHandler

logger = setup_logger(name=__name__, level="DEBUG")


class CustomMessageHandler(BaseMessageHandler):
    """自定义消息处理器
    
    采用即时更新策略，每次收到数据块都立即保存。
    """

    def __init__(
        self, 
        initial_chunk: AiChatResultBO, 
        conversation_id: int,
        gateway: AIChatGateway,
        snowflake: Snowflake
    ):
        # 先初始化消息类型，再调用父类构造函数
        self._current_message_type = initial_chunk.custom.message_type if initial_chunk.custom else MessageType.TEXT
        super().__init__(initial_chunk, conversation_id, gateway, snowflake)

    def get_message_type(self) -> str:
        # 自定义消息的类型由数据块本身决定
        return self._current_message_type

    def _process_initial_chunk(self, chunk: AiChatResultBO) -> None:
        """处理初始自定义消息块（同步部分）"""
        # _current_message_type 已在 __init__ 中设置

    async def initialize_async(self, initial_chunk: AiChatResultBO) -> None:
        """异步初始化：保存初始自定义消息块"""
        await self.accumulate(initial_chunk)

    async def accumulate(self, chunk: AiChatResultBO) -> None:
        """立即保存自定义消息"""
        # 确保custom对象存在并设置message_id
        if chunk.custom is None:
            chunk.custom = CustomBO(
                message_type=self._current_message_type, 
                message_id=str(self.message_id)
            )
        else:
            chunk.custom.message_id = str(self.message_id)
            
        await self.gateway.save_ai_custom_message(
            self.conversation_id, self.message_id, chunk.custom
        )
        logger.info(f"Saved custom message: message_id={self.message_id}, type={chunk.custom.message_type}")

    async def save(self) -> None:
        """自定义消息已在accumulate中保存，此方法为空操作"""
        pass 