"""
文本消息处理器

采用缓冲策略，累积所有文本块，在save()时一次性保存。
"""

from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO
from src.domain.gateway.ai_chat_gateway import AIChatGateway
from ..contracts.constants import MessageType
from src.utils.snow_flake import Snowflake
from .base_handler import BaseMessageHandler

logger = setup_logger(name=__name__, level="DEBUG")


class TextMessageHandler(BaseMessageHandler):
    """文本消息处理器
    
    采用缓冲策略，累积所有文本块，在save()时一次性保存。
    """

    def __init__(
        self, 
        initial_chunk: AiChatResultBO, 
        conversation_id: int,
        gateway: AIChatGateway,
        snowflake: Snowflake
    ):
        self.text_buffer = ""
        super().__init__(initial_chunk, conversation_id, gateway, snowflake)

    def get_message_type(self) -> str:
        return MessageType.TEXT

    def _process_initial_chunk(self, chunk: AiChatResultBO) -> None:
        """处理初始文本块"""
        self.text_buffer = chunk.text or ""

    async def accumulate(self, chunk: AiChatResultBO) -> None:
        """累积文本内容"""
        # 确保custom对象存在并设置message_id
        if chunk.custom is None:
            chunk.custom = CustomBO(
                message_type=MessageType.TEXT, 
                message_id=str(self.message_id)
            )
        else:
            chunk.custom.message_id = str(self.message_id)
            
        self.text_buffer += chunk.text or ""

    async def save(self) -> None:
        """保存累积的文本消息"""
        if self.text_buffer:
            await self.gateway.save_ai_text_message(
                self.text_buffer, self.conversation_id, self.message_id
            )
            logger.info(f"Saved text message: message_id={self.message_id}, length={len(self.text_buffer)}") 