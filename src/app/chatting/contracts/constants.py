"""
聊天服务常量定义

定义聊天服务中使用的常量，避免硬编码字符串。
"""

from enum import Enum


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"
    REPORT = "report"
    QUESTIONNAIRE = "questionnaire"
    BIPOLAR = "bipolar"
    CUSTOM = "custom"


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    AI = "ai"


class ActionType(str, Enum):
    """动作类型枚举"""
    QUERY_PROGRESS = "query_progress"
    REPORT = "report"
    NORMAL_CHAT = "normal_chat"


class ChunkMode(str, Enum):
    """流式数据块模式枚举"""
    CUSTOM = "custom"
    VALUES = "values"


class ErrorMessages:
    """错误消息常量"""
    CONVERSATION_ID_REQUIRED = "conversation_id cannot be None"
    REPORT_TASK_NOT_FOUND = "report_task_not_found"
    INITIALIZATION_ERROR = "Initialization error in chat_handler"


class DefaultValues:
    """默认值常量"""
    PROGRESS_QUERY_INTERVAL = 5
    INITIAL_CREDITS = 100
    MESSAGE_HISTORY_LIMIT = 10 