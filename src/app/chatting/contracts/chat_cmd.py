"""
聊天命令对象

定义聊天相关的命令对象，用于在App层处理业务逻辑。
按照DDD规范，App层应该使用Cmd对象而不是直接依赖Adapter层的VO对象。
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class MessageCmd(BaseModel):
    """消息命令对象"""
    text: Optional[str] = Field(None, description="消息文本内容")
    role: str = Field(..., description="消息角色：user/assistant/system")
    params: Optional[Dict[str, Any]] = Field(None, description="消息参数")


class ChatCmd(BaseModel):
    """聊天命令对象"""
    conversation_id: str = Field(..., description="会话ID")
    messages: List[MessageCmd] = Field(default_factory=list, description="消息列表")
    interrupt_feedback: Optional[bool] = Field(False, description="是否中断反馈")


class ChatProgressQueryCmd(BaseModel):
    """聊天进度查询命令对象"""
    conversation_id: str = Field(..., description="会话ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    action: Optional[str] = Field(None, description="操作类型")


class ChatProcessActionCmd(BaseModel):
    """聊天流程动作命令对象"""
    conversation_id: str = Field(..., description="会话ID")
    messages: List[MessageCmd] = Field(default_factory=list, description="消息列表")
    action: Optional[str] = Field(None, description="动作类型") 