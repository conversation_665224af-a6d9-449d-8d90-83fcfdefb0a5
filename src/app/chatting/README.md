# 聊天服务模块

基于DDD架构规范重构的AI聊天服务模块，提供清晰的职责分离和良好的可维护性。

## 架构设计

### 模块结构

```
src/app/chatting/
├── __init__.py                 # 模块初始化
├── ai_chat_service.py         # 核心聊天服务（App层）
├── chat_service_factory.py    # 服务工厂
├── README.md                  # 本文档
│
├── contracts/                 # 数据契约
│   ├── __init__.py
│   ├── chat_cmd.py           # 命令对象定义
│   └── constants.py          # 常量定义
│
├── processors/               # 无状态处理器
│   ├── __init__.py
│   ├── chat_state_builder.py    # 聊天状态构建器
│   ├── progress_query_processor.py # 进度查询处理器
│   └── streaming_processor.py   # 流式处理器
│
├── handlers/                 # 有状态消息处理器
│   ├── __init__.py
│   ├── base_handler.py       # 抽象基类
│   ├── text_handler.py       # 文本消息处理器
│   ├── custom_handler.py     # 自定义消息处理器
│   └── handler_factory.py    # 处理器工厂
│
└── utils/                    # 工具类
    ├── __init__.py
    └── message_converter.py  # 消息转换器
```

### 依赖关系图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Adapter层     │───▶│     App层        │───▶│   Domain层      │
│  (原有Router)   │    │  AIChatService   │    │ AIChatGateway   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       ▲
         │                        ▼                       │
         │              ┌──────────────────┐    ┌─────────────────┐
         │              │   Processors/    │    │     Exe层       │
         │              │ StreamingProc... │    │AIChatGatewayImpl│
         │              └──────────────────┘    └─────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Utils/        │    │   Handlers/      │    │Infrastructure层 │
│MessageConverter │    │ TextHandler...   │    │   (Repository)  │
│   (转换工具)    │    │ (有状态处理器)   │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 核心组件

### 1. AIChatService (App层)

负责业务编排的核心服务类：

- **职责**：聊天流程编排、业务逻辑协调
- **依赖**：通过Gateway接口访问数据，避免直接依赖Infrastructure层
- **特点**：职责单一、易于测试、支持依赖注入

### 2. AIChatGateway (Domain层接口)

定义数据访问契约的Gateway接口：

- **职责**：定义聊天相关的数据访问接口
- **位置**：`src/domain/gateway/ai_chat_gateway.py`
- **特点**：遵循依赖倒置原则，由Domain层定义接口

### 3. AIChatGatewayImpl (Exe层实现)

Gateway接口的具体实现：

- **职责**：实现具体的数据访问逻辑
- **位置**：`src/exe/gateway_impl/ai_chat_gateway_impl.py`
- **特点**：可以依赖Infrastructure层，处理具体的技术实现

### 4. StreamingChatProcessor

专门处理流式响应的处理器：

- **职责**：管理流式数据状态转换、消息持久化
- **特点**：使用状态机模式、职责单一、易于扩展

### 5. MessageConverter

消息格式转换器：

- **职责**：VO↔Cmd转换、数据库消息↔LangChain消息转换
- **特点**：纯函数式设计、无状态、易于测试

## 使用方式

### 直接使用新架构

```python
from src.app.chatting.chat_service_factory import get_ai_chat_service
from src.app.chatting.chat_cmd import ChatCmd, MessageCmd

# 创建服务实例
service = get_ai_chat_service()

# 构建命令对象
chat_cmd = ChatCmd(
    conversation_id="123",
    messages=[MessageCmd(text="Hello", role="user")],
    interrupt_feedback=False
)

# 调用服务
async for result in service.handle_chat(chat_cmd, "user_id"):
    print(result)
```

## 架构优势

### 1. 符合DDD规范

- ✅ App层使用Cmd对象，不直接依赖Adapter层VO
- ✅ 使用Gateway模式，Domain层定义接口，Exe层实现
- ✅ 依赖方向正确：Adapter→App→Domain←Exe←Infrastructure

### 2. 职责清晰

- **AIChatService**：专注业务编排
- **StreamingChatProcessor**：专门处理流式响应
- **MessageConverter**：负责格式转换
- **AIChatGatewayImpl**：负责数据访问

### 3. 易于测试

- 所有组件都支持依赖注入
- 核心逻辑与技术实现解耦
- 可以轻松进行单元测试和集成测试

### 4. 向后兼容

- 提供遗留适配器，保持原有接口可用
- 渐进式迁移，降低风险

### 5. 易于扩展

- 新增消息类型只需扩展StreamingChatProcessor
- 新增数据源只需实现Gateway接口
- 状态机模式便于处理复杂的消息流转

## 常见问题

### Q: 为什么不直接修改原有文件？

A: 为了保持向后兼容性和降低迁移风险。新架构在独立模块中实现，可以渐进式迁移。

### Q: 如何添加新的消息类型？

A: 在`constants.py`中添加新的消息类型枚举，然后在`StreamingChatProcessor`中添加相应的处理逻辑。

### Q: 如何扩展Gateway接口？

A: 在`AIChatGateway`接口中添加新方法，然后在`AIChatGatewayImpl`中实现具体逻辑。

### Q: 性能如何？

A: 新架构通过以下方式保证性能：

- 单例模式避免重复创建服务实例
- 流式处理器优化状态管理
- Gateway实现保持原有的数据访问性能 