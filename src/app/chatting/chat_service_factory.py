"""
聊天服务工厂

负责创建聊天服务实例并处理依赖注入。
遵循DDD架构规范，通过工厂模式管理服务依赖。
"""
import threading

from src.exe.gateway_impl.ai_chat_gateway_impl import AIChatGatewayImpl
from src.utils.snow_flake import Snowflake
from .ai_chat_service import AIChatService


class ChatServiceFactory:
    """聊天服务工厂类"""

    @staticmethod
    def create_ai_chat_service() -> AIChatService:
        """创建AI聊天服务实例
        
        Returns:
            AIChatService: 配置好依赖的AI聊天服务实例
        """
        # 创建依赖实例
        ai_chat_gateway = AIChatGatewayImpl()
        snowflake = Snowflake(worker_id=0, datacenter_id=0)
        
        # 创建并返回服务实例
        return AIChatService(ai_chat_gateway, snowflake)


# 全局服务实例缓存和锁
_ai_chat_service_instance = None
_lock = threading.Lock()


def get_ai_chat_service() -> AIChatService:
    """获取AI聊天服务实例（线程安全的单例模式）
    
    Returns:
        AIChatService: AI聊天服务实例
    """
    global _ai_chat_service_instance
    # 使用双重检查锁定模式确保线程安全和高性能
    if _ai_chat_service_instance is None:
        with _lock:
            if _ai_chat_service_instance is None:
                _ai_chat_service_instance = ChatServiceFactory.create_ai_chat_service()
    return _ai_chat_service_instance 