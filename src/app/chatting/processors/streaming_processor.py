"""
流式聊天处理器

负责处理AI聊天流式响应的状态管理和消息持久化。
使用策略模式将不同类型消息的处理逻辑分离到专门的处理器中。
"""

from typing import Optional, Any, AsyncGenerator
from lucas_common_components.logging import setup_logger

from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO
from src.domain.gateway.ai_chat_gateway import AIChatGateway
from ..contracts.constants import MessageType
from ..handlers.base_handler import BaseMessageHandler
from ..handlers.handler_factory import create_message_handler
from src.utils.snow_flake import Snowflake

logger = setup_logger(name=__name__, level="DEBUG")


class StreamingChatProcessor:
    """流式聊天处理器
    
    作为调度器，根据消息类型将处理委托给相应的消息处理器。
    使用策略模式，使每种消息类型的处理逻辑保持独立。
    """

    def __init__(self, gateway: AIChatGateway, snowflake: Snowflake):
        self.gateway = gateway
        self.snowflake = snowflake

    async def process_stream_chunks(
        self, 
        chunk_stream: AsyncGenerator[Any, None], 
        conversation_id: int
    ) -> AsyncGenerator[str, None]:
        """处理流式数据块
        
        Args:
            chunk_stream: 流式数据块生成器
            conversation_id: 会话ID
            
        Yields:
            str: 处理后的JSON字符串
        """
        active_handler: Optional[BaseMessageHandler] = None
        try:
            async for chunk in chunk_stream:
                if not chunk:
                    continue

                # 解析chunk数据
                mode, data = self._parse_chunk(chunk)
                
                if mode == "custom" and isinstance(data, dict) and "data" in data:
                    chat_result_bo = data["data"]
                    if not isinstance(chat_result_bo, AiChatResultBO):
                        continue

                    # 处理chat_result_bo
                    result_json, active_handler = await self._process_chat_result(
                        chat_result_bo, conversation_id, active_handler
                    )
                    if result_json:
                        yield result_json
        finally:
            # 确保最后一个处理器的消息被保存
            if active_handler:
                await active_handler.save()

    def _parse_chunk(self, chunk: Any) -> tuple[Optional[str], Any]:
        """解析chunk数据，提取mode和data"""
        mode = None
        data = chunk
        
        if isinstance(chunk, tuple):
            if len(chunk) == 2:
                mode, data = chunk
            elif len(chunk) == 3:
                _, mode, data = chunk
                
        return mode, data

    async def _process_chat_result(
        self, 
        chat_result_bo: AiChatResultBO, 
        conversation_id: int,
        active_handler: Optional[BaseMessageHandler]
    ) -> tuple[Optional[str], Optional[BaseMessageHandler]]:
        """处理单个聊天结果对象
        
        Args:
            chat_result_bo: 聊天结果BO对象
            conversation_id: 会话ID
            active_handler: 当前活跃的处理器
            
        Returns:
            tuple[Optional[str], Optional[BaseMessageHandler]]: 处理后的JSON字符串和更新后的处理器
        """
        # 确保custom对象存在
        if chat_result_bo.custom is None:
            chat_result_bo.custom = CustomBO(message_type=MessageType.TEXT, message_id="")

        current_message_type = self._get_message_type(chat_result_bo)

        # 检查是否需要切换处理器
        if self._should_switch_handler(current_message_type, active_handler):
            # 保存当前处理器的消息
            if active_handler:
                await active_handler.save()
            
            # 创建新的处理器
            active_handler = await create_message_handler(
                chat_result_bo, conversation_id, self.gateway, self.snowflake
            )
        else:
            # 使用当前处理器累积数据
            if active_handler:
                await active_handler.accumulate(chat_result_bo)

        logger.info(f"chat_result_bo:{chat_result_bo.model_dump_json(exclude_none=True)}")
        return chat_result_bo.model_dump_json(exclude_none=True), active_handler

    def _should_switch_handler(self, current_message_type: str, active_handler: Optional[BaseMessageHandler]) -> bool:
        """判断是否需要切换处理器
        
        Args:
            current_message_type: 当前消息类型
            active_handler: 当前活跃的处理器
            
        Returns:
            bool: 是否需要切换处理器
        """
        # 如果没有活跃的处理器，需要创建
        if active_handler is None:
            return True
        
        # 如果消息类型发生变化，需要切换
        return current_message_type != active_handler.get_message_type()

    def _get_message_type(self, ai_vo: AiChatResultBO) -> str:
        """获取消息类型"""
        if not ai_vo.custom:
            return MessageType.TEXT
        return ai_vo.custom.message_type or MessageType.TEXT