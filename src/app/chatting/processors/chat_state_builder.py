"""
聊天状态构建器

专门负责构建聊天初始状态和BizRuntimeBO相关逻辑。
从AIChatService中拆分出来，让状态构建逻辑更清晰。
"""

from typing import Optional, Dict, Any
from lucas_common_components.logging import setup_logger

from src.domain.gateway.ai_chat_gateway import AIChatGateway
from src.app.zhiyuan.models.OrchestratorModel import BizRuntimeBO
from src.utils.snow_flake import Snowflake
from ..contracts.chat_cmd import ChatCmd
from ..contracts.constants import DefaultValues
from ..utils.message_converter import MessageConverter

logger = setup_logger(name=__name__, level="DEBUG")


class ChatStateBuilder:
    """聊天状态构建器
    
    负责构建聊天初始状态、管理BizRuntimeBO等操作。
    """

    def __init__(self, gateway: AIChatGateway, snowflake: Snowflake):
        """初始化聊天状态构建器
        
        Args:
            gateway: AI聊天Gateway接口
            snowflake: 雪花算法ID生成器
        """
        self.gateway = gateway
        self.snowflake = snowflake

    async def build_init_state(
        self, 
        chat_cmd: ChatCmd, 
        uniq_user_id: str, 
        action: Optional[str]
    ) -> Dict[str, Any]:
        """构建聊天初始状态
        
        Args:
            chat_cmd: 聊天命令对象
            uniq_user_id: 唯一用户ID
            action: 会话动作类型
            
        Returns:
            Dict[str, Any]: 初始状态字典
        """
        conversation_id = chat_cmd.conversation_id
        
        # 获取检查点数据
        checkpoint_data = await self.gateway.get_checkpoint_data(conversation_id)
        
        # 获取历史消息
        db_messages = await self.gateway.get_messages_by_conversation_id(
            int(conversation_id), DefaultValues.MESSAGE_HISTORY_LIMIT
        )
        
        # 转换为LangChain消息格式
        langchain_messages = MessageConverter.db_messages_to_langchain(db_messages)
        
        # 获取用户信息
        user_info = await self.gateway.get_user_profile(uniq_user_id)
        if user_info:
            user_info.id = int(uniq_user_id)
        
        # 构建BizRuntimeBO
        biz_runtime_bo = self._build_biz_runtime_bo(checkpoint_data, chat_cmd)
        
        # 提取消息参数
        message_params = MessageConverter.extract_message_params(chat_cmd.messages)
        
        # 更新BizRuntimeBO参数
        self._update_biz_runtime_params(biz_runtime_bo, message_params)

        logger.info(f"构建初始状态完成: conversation_id={conversation_id}")
        
        return {
            "userInfo": user_info,
            "messageId": str(self.snowflake.generate()),
            "conversationId": conversation_id,
            "messages": langchain_messages,
            "isInit": True,
            "action": action or (checkpoint_data.get("action") if checkpoint_data else None),
            "nextAgents": [],
            "bizRuntimeBO": biz_runtime_bo,
            "interrupt_feedback": chat_cmd.interrupt_feedback or False,
            "handleSummary": False,
        }

    def _build_biz_runtime_bo(
        self, 
        checkpoint_data: Optional[Dict[str, Any]], 
        chat_cmd: ChatCmd
    ) -> BizRuntimeBO:
        """构建BizRuntimeBO对象
        
        Args:
            checkpoint_data: 检查点数据
            chat_cmd: 聊天命令对象
            
        Returns:
            BizRuntimeBO: 业务运行时对象
        """
        if checkpoint_data:
            biz_runtime_dict = checkpoint_data.get("bizRuntimeBO")
            if isinstance(biz_runtime_dict, dict):
                biz_runtime_bo = BizRuntimeBO(**biz_runtime_dict)
            else:
                biz_runtime_bo = biz_runtime_dict if biz_runtime_dict else BizRuntimeBO()
        else:
            biz_runtime_bo = BizRuntimeBO()
            
        # 确保params存在
        if not biz_runtime_bo.params:
            biz_runtime_bo.params = {}
            
        return biz_runtime_bo

    def _update_biz_runtime_params(
        self, 
        biz_runtime_bo: BizRuntimeBO, 
        message_params: Dict[str, Any]
    ) -> None:
        """更新BizRuntimeBO参数
        
        Args:
            biz_runtime_bo: 业务运行时对象
            message_params: 消息参数
        """
        # 更新问卷相关参数
        if message_params.get("question"):
            biz_runtime_bo.params.update({"questionnaire_question": message_params["question"]})
            
        # 更新答案参数
        answer = message_params.get("answer")
        biz_runtime_bo.params.update({"answer": answer})
        
        logger.debug(f"更新BizRuntimeBO参数: {biz_runtime_bo.params}") 