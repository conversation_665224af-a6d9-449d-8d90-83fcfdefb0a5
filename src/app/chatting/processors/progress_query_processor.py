"""
进度查询处理器

专门负责处理聊天中的进度查询相关逻辑。
从AIChatService中拆分出来，保持职责单一。
"""

from typing import Optional, AsyncGenerator, Any
from lucas_common_components.logging import setup_logger

from src.domain.gateway.ai_chat_gateway import AIChatGateway
from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO, MessageStatusBO, ReportContentResultBO
from ..contracts.chat_cmd import ChatProgressQueryCmd
from ..contracts.constants import MessageType, ActionType, ErrorMessages, DefaultValues

logger = setup_logger(name=__name__, level="DEBUG")


class ProgressQueryProcessor:
    """进度查询处理器
    
    负责处理聊天进度查询的完整流程。
    """

    def __init__(self, gateway: AIChatGateway):
        """初始化进度查询处理器
        
        Args:
            gateway: AI聊天Gateway接口
        """
        self.gateway = gateway

    async def handle_progress_query(
        self, 
        progress_cmd: ChatProgressQueryCmd, 
        uniq_user_id: str
    ) -> AsyncGenerator[str, None]:
        """处理进度查询
        
        Args:
            progress_cmd: 进度查询命令对象
            uniq_user_id: 唯一用户ID
            
        Yields:
            str: 查询结果JSON
        """
        if progress_cmd.action == ActionType.QUERY_PROGRESS and progress_cmd.task_id:
            report_task = await self.gateway.get_report_task_by_id(progress_cmd.task_id)
            
            if not report_task:
                error_result = self._build_error_result(progress_cmd.task_id)
                yield error_result.model_dump_json(exclude_none=True)
                return

            # 构建进度查询结果
            message_id = self._extract_message_id_from_task(report_task)
            result = self._build_progress_result(report_task, message_id)
            
            logger.info(f"progress_result: {result.model_dump_json(exclude_none=True)}")
            yield result.model_dump_json(exclude_none=True)

    def _build_error_result(self, task_id: str) -> AiChatResultBO:
        """构建错误结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            AiChatResultBO: 错误结果对象
        """
        return AiChatResultBO(
            text=ErrorMessages.REPORT_TASK_NOT_FOUND,
            custom=CustomBO(
                message_id="",
                message_type=MessageType.REPORT,
                message_data=ReportContentResultBO(
                    success=False, 
                    task_id=task_id, 
                    task_name=""
                ),
            ),
        )

    def _extract_message_id_from_task(self, report_task: Any) -> str:
        """从报告任务中提取消息ID
        
        Args:
            report_task: 报告任务对象
            
        Returns:
            str: 消息ID
        """
        if hasattr(report_task, 'extra_data') and report_task.extra_data:
            return report_task.extra_data.get("message_id", "")
        return ""

    def _build_progress_result(self, report_task: Any, message_id: str) -> AiChatResultBO:
        """构建进度查询结果
        
        Args:
            report_task: 报告任务对象
            message_id: 消息ID
            
        Returns:
            AiChatResultBO: 聊天结果对象
        """
        return AiChatResultBO(
            custom=CustomBO(
                message_id=message_id,
                message_type=MessageType.REPORT,
                message_status=MessageStatusBO(
                    user_input={
                        "send_button_disable": not report_task.is_completed
                    },
                    complete=report_task.is_completed,
                    progress=report_task.progress,
                    next_interval=None if report_task.is_completed else DefaultValues.PROGRESS_QUERY_INTERVAL,
                ),
                message_data=ReportContentResultBO(
                    task_id=report_task.task_id if hasattr(report_task, 'task_id') else "",
                    task_name=report_task.task_name if hasattr(report_task, 'task_name') else "",
                    content=report_task.report_content if hasattr(report_task, 'report_content') else None,
                    success=True,
                ),
            ),
        ) 