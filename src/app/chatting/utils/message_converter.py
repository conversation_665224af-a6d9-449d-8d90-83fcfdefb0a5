"""
消息转换器

负责各种消息格式之间的转换，包括：
- BO对象到LangChain消息的转换
- 其他消息格式转换
"""

from typing import List, Dict, Any
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, BaseMessage

from ..contracts.chat_cmd import MessageCmd
from ..contracts.constants import MessageRole


class MessageConverter:
    """消息转换器类"""

    @staticmethod
    def to_langchain_message(message_cmd: MessageCmd) -> BaseMessage:
        """将MessageCmd转换为LangChain消息类型
        
        Args:
            message_cmd: 消息命令对象
            
        Returns:
            BaseMessage: LangChain消息对象
        """
        if message_cmd.role == MessageRole.USER:
            return HumanMessage(content=message_cmd.text or "")
        elif message_cmd.role == MessageRole.ASSISTANT:
            return AIMessage(content=message_cmd.text or "")
        elif message_cmd.role == MessageRole.SYSTEM:
            return SystemMessage(content=message_cmd.text or "")
        else:
            # 默认作为用户消息处理
            return HumanMessage(content=message_cmd.text or "")

    @staticmethod
    def db_messages_to_langchain(db_messages: List[Any]) -> List[BaseMessage]:
        """将数据库消息转换为LangChain消息列表
        
        Args:
            db_messages: 数据库消息列表
            
        Returns:
            List[BaseMessage]: LangChain消息列表
        """
        langchain_messages = []
        
        # 确保消息按时间正序排列
        sorted_messages = sorted(db_messages, key=lambda x: x.created_at)
        
        for msg in sorted_messages:
            text = ""
            if isinstance(msg.message_data, dict):
                text = msg.message_data.get("text", "")
            
            if text:
                message_cmd = MessageCmd(text=text, role=msg.role, params=None)
                langchain_message = MessageConverter.to_langchain_message(message_cmd)
                langchain_messages.append(langchain_message)
                
        return langchain_messages

    @staticmethod
    def extract_message_params(messages: List[MessageCmd]) -> Dict[str, Any]:
        """从消息列表中提取参数
        
        Args:
            messages: 消息命令列表
            
        Returns:
            Dict[str, Any]: 提取的参数
        """
        if not messages:
            return {}
            
        first_message = messages[0]
        if not first_message.params:
            return {}
            
        return {
            "action": first_message.params.get("action", ""),
            "task_id": first_message.params.get("task_id", ""),
            "interrupt_feedback": first_message.params.get("interrupt_feedback", ""),
            "question": first_message.params.get("question", {}),
            "answer": first_message.params.get("answer", {})
        } 