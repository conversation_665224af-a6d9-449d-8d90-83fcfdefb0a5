"""
SAE JOB 服务接口
"""

import json
import os
from typing import Dict, Any, Optional
from lucas_common_components.logging import setup_logger
from src.config.config_model import AIChatAppConfig
from alibabacloud_sae20190506.client import Client as sae20190506Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_sae20190506 import models as sae_20190506_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_credentials.models import Config

logger = setup_logger(name=__name__, level="DEBUG")


class SAEJobClient:
    """SAE JOB 服务客户端"""

    _client: Optional[sae20190506Client] = None
    _initialized: bool = False

    @classmethod
    def _create_client(cls) -> sae20190506Client:
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        try:
            sae_job_config = AIChatAppConfig.get_instance().saeJob
            credential_config = Config(
                type="access_key",
                access_key_id=sae_job_config.accessKeyId,
                access_key_secret=sae_job_config.accessKeySecret,
            )
            cred = CredentialClient(credential_config)

            open_api_config = open_api_models.Config(credential=cred.cloud_credential)
            open_api_config.endpoint = sae_job_config.endpoint

            client = sae20190506Client(open_api_config)
            logger.info("SAE JOB 客户端创建成功")
            return client

        except Exception as e:
            logger.error(f"SAE JOB 客户端创建失败: {e}")
            raise

    @classmethod
    def get_client(cls) -> sae20190506Client:
        """
        获取SAE JOB客户端实例（单例模式）
        @return: Client
        """
        if not cls._initialized or cls._client is None:
            cls._client = cls._create_client()
            cls._initialized = True
            logger.info("SAE JOB 客户端初始化完成")

        return cls._client

    @classmethod
    def reset_client(cls):
        """重置客户端（用于配置更新或错误恢复）"""
        cls._client = None
        cls._initialized = False
        logger.info("SAE JOB 客户端已重置")

    @classmethod
    async def exec_job(cls, app_id: str, envs: str) -> Dict[str, Any]:
        """
        执行 SAE JOB 任务

        Args:
            app_id: SAE 应用ID
            envs: 环境变量

        Returns:
            Dict[str, Any]: 执行结果

        Raises:
            Exception: 执行失败时抛出异常
        """
        try:
            logger.info(f"开始执行 SAE JOB: app_id={app_id}, envs={envs}")

            # 获取客户端
            client = cls.get_client()

            # 构建请求
            exec_job_request = sae_20190506_models.ExecJobRequest(
                app_id=app_id,
                envs=envs,
            )

            # 设置运行时选项
            runtime = util_models.RuntimeOptions()
            headers = {}

            # 执行请求
            response = await client.exec_job_with_options_async(
                exec_job_request, headers, runtime
            )

            # 处理响应
            result = {
                "success": True,
                "request_id": response.headers.get("x-acs-request-id"),
                "response_body": response.body.to_map() if response.body else {},
                "status_code": response.status_code,
            }

            logger.info(f"SAE JOB 执行成功: {result}")
            return result

        except Exception as error:
            logger.error(f"SAE JOB 执行失败: {error}")

            # 提取错误信息
            error_info = {
                "success": False,
                "error_message": str(error),
                "error_code": getattr(error, "code", None),
                "request_id": None,
                "recommend": None,
            }

            # 尝试获取详细错误信息
            try:
                if hasattr(error, "message"):
                    error_info["error_message"] = error.message

                if hasattr(error, "data") and error.data:
                    error_info["recommend"] = error.data.get("Recommend")
                    error_info["request_id"] = error.data.get("RequestId")

            except Exception as parse_error:
                logger.warning(f"解析错误信息失败: {parse_error}")

            logger.error(f"SAE JOB 错误详情: {error_info}")

            # 重新抛出异常，让调用方处理
            raise Exception(f"SAE JOB 执行失败: {error_info['error_message']}")

    @classmethod
    async def exec_report_job(
        cls,
        app_id: str,
        task_id: str,
        task_key: str,
        conversation_id: int,
        user_id: int,
        message_id: int,
        trace_id: str,
    ) -> Dict[str, Any]:
        """
        执行报告生成任务

        Args:
            app_id: SAE 应用ID
            task_id: 任务ID
            task_key: 任务类型键
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            logger.info(f"开始执行报告生成任务: task_id={task_id}, task_key={task_key}")

            # 构建命令参数
            envs = [
                {"name": "APP_MODE", "value": "job"},
                {"name": "JOB_TASK", "value": "new_reporting_task"},
                {"name": "NACOS_SERVER_ADDR", "value": os.getenv("NACOS_SERVER_ADDR")},
                {
                    "name": "JOB_PARAMS",
                    "value": f"message_id:{message_id},conversation_id:{conversation_id},user_id:{user_id},task_id:{task_id},task_key:{task_key},trace_id:{trace_id}}}",
                },
            ]

            # 执行任务
            result = await cls.exec_job(app_id=app_id, envs=json.dumps(envs))
            logger.info(f"报告生成任务提交成功: {task_id}")
            return result

        except Exception as e:
            logger.error(f"报告生成任务提交失败: {e}")
            raise


# 便捷函数
async def create_report_task(
    task_id: str,
    task_key: str,
    conversation_id: int,
    user_id: int,
    message_id: int,
    trace_id: str,
) -> Dict[str, Any]:
    """
    创建报告生成任务的便捷函数

    Args:
        task_id: 任务ID
        task_key: 任务类型键
        conversation_id: 会话ID
        user_id: 用户ID

    Returns:
        Dict[str, Any]: 执行结果
    """
    try:
        # 从配置中获取 SAE 应用ID

        # app_id = "12f90eba-977c-473c-ab25-79b7a50b6a06"
        app_id = AIChatAppConfig.get_instance().saeJob.jobIdConfig["report_task"]
        if not app_id:
            raise ValueError("SAE 应用ID未配置")

        # 执行报告生成任务
        result = await SAEJobClient.exec_report_job(
            app_id=app_id,
            task_id=task_id,
            task_key=task_key,
            conversation_id=conversation_id,
            user_id=user_id,
            message_id=message_id,
            trace_id=trace_id,
        )

        return result

    except Exception as e:
        logger.error(f"创建报告任务失败: {e}")
        raise


async def submit_sae_job(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    提交 SAE JOB 任务（兼容性函数）

    Args:
        task_data: 任务数据，包含 task_id, task_key, conversation_id, user_id

    Returns:
        Dict[str, Any]: 执行结果
    """
    return await create_report_task(
        task_id=task_data["task_id"],
        task_key=task_data["task_key"],
        conversation_id=task_data["conversation_id"],
        user_id=task_data["user_id"],
        message_id=task_data["message_id"],
        trace_id=task_data["trace_id"],
    )
