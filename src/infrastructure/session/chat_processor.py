import asyncio
from typing import Any

from lucas_common_components.logging import setup_logger
from sse_starlette.sse import AsyncContentStream

from src.adapter.vo.ai_chat_model import ChatInputVO
from src.domain.model.ai_chat_model import AiChatResultBO
from src.infrastructure.session import <PERSON>Manager, SessionManager, Connection, ConnectionStatus

logger = setup_logger(name=__name__, level="DEBUG")


class ChatProcessor:
    _instance = None

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = ChatProcessor()
        return cls._instance

    def __init__(self):
        # 全局管理器实例
        self.connection_manager = ConnectionManager()
        self.session_manager = SessionManager(self.connection_manager)

    async def shutdown(self):
        """优雅下线函数

        在应用关闭时调用，确保所有资源被正确清理。
        """
        logger.info("Starting graceful shutdown of chat service...")

        try:
            # 1. 等待当前处理中的任务完成（设置超时）
            logger.info("Waiting for active tasks to complete...")
            await self._wait_for_active_tasks(timeout=30.0)

            # 2. 取消剩余任务
            logger.info("Cancelling remaining tasks...")
            await self._cancel_remaining_tasks()

            # 3. 关闭会话管理器
            logger.info("Shutting down session manager...")
            await self.session_manager.shutdown()

            # 4. 关闭所有连接
            logger.info("Closing all connections...")
            await self.connection_manager.close_all_connections()

            logger.info("Graceful shutdown completed successfully")

        except Exception as e:
            logger.error(f"Error during graceful shutdown: {e}")
            raise

    async def _wait_for_active_tasks(self, timeout: float):
        """等待活跃任务完成

        Args:
            timeout: 超时时间（秒）
        """
        import time
        start_time = time.time()

        while True:
            stats = await self.session_manager.get_manager_stats()
            active_conversations = stats['active_conversations']

            if active_conversations == 0:
                logger.info("All active tasks completed")
                break

            elapsed = time.time() - start_time
            if elapsed >= timeout:
                logger.warning(f"Timeout waiting for tasks, {active_conversations} conversations still active")
                break

            logger.info(
                f"Waiting for {active_conversations} active conversations to complete... ({elapsed:.1f}s/{timeout}s)")
            await asyncio.sleep(1.0)

    async def _cancel_remaining_tasks(self):
        """取消剩余任务"""
        stats = await self.session_manager.get_manager_stats()
        if stats['active_conversations'] > 0:
            logger.info(f"Cancelling {stats['active_conversations']} remaining tasks...")

            # 获取所有活跃会话ID
            active_conversations = list(self.session_manager.active_sessions.keys())

            # 取消所有会话任务
            for conversation_id in active_conversations:
                await self.session_manager.cancel_conversation_tasks(conversation_id)

    async def get_service_health(self) -> dict:
        """获取服务健康状态

        Returns:
            dict: 服务健康状态信息
        """
        try:
            connection_stats = await self.connection_manager.get_connection_stats()
            session_stats = await self.session_manager.get_manager_stats()

            return {
                "status": "healthy",
                "connections": connection_stats,
                "sessions": session_stats,
                "timestamp": asyncio.get_event_loop().time()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": asyncio.get_event_loop().time()
            }

    async def chat_handler(self, request: ChatInputVO, uniqUserId: str, chat_session_processor) -> AsyncContentStream:
        """新的聊天处理器 - 使用 Acceptor-Processor 模式"""
        # 检查conversation_id是否为空
        if not request.conversation_id:
            raise ValueError("conversation_id cannot be None")

        # 创建连接
        connection = await self.connection_manager.add_connection(request.conversation_id, uniqUserId)

        try:
            # 提交会话任务
            await self.session_manager.submit_session_task(
                request, uniqUserId, connection.connection_id, chat_session_processor
            )

            # 从连接的响应队列中读取结果
            async for result in self._read_connection_responses(connection):
                yield result

        except Exception as e:
            logger.error(f"Error in chat_handler: {e}")
            error_result = AiChatResultBO(text=f"处理出错: {str(e)}")
            yield error_result.model_dump_json(exclude_none=True)
        finally:
            # 清理连接
            await self.connection_manager.remove_connection(connection.connection_id)

    async def _read_connection_responses(self, connection: Connection) -> AsyncContentStream:
        """从连接读取响应"""
        try:
            while connection.status == ConnectionStatus.ACTIVE:
                try:
                    # 等待响应，设置超时
                    result = await asyncio.wait_for(
                        connection.response_queue.get(),
                        timeout=30.0
                    )

                    if result is None:  # 结束信号
                        logger.info(f"Received end signal for connection {connection.connection_id}")
                        break

                    # 检查是否是任务完成信号
                    if isinstance(result, dict) and result.get("task_completed"):
                        logger.info(f"Task completed for connection {connection.connection_id}: {result}")
                        # 可以选择是否将完成信号发送给客户端
                        yield result
                        continue

                    # 检查是否是错误信号
                    if isinstance(result, dict) and result.get("error"):
                        logger.error(f"Task error for connection {connection.connection_id}: {result}")
                        yield result
                        continue

                    yield result

                except asyncio.TimeoutError:
                    # 发送心跳或检查连接状态
                    logger.debug(f"Timeout waiting for response on connection {connection.connection_id}")
                    continue
                except Exception as e:
                    logger.error(f"Error reading from connection {connection.connection_id}: {e}")
                    break

        except Exception as e:
            logger.error(f"Error in _read_connection_responses: {e}")
        finally:
            # 确保连接被标记为关闭
            connection.status = ConnectionStatus.CLOSED
            logger.info(f"Connection {connection.connection_id} marked as closed")
