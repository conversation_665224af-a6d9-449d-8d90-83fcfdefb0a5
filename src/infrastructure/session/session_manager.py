"""
会话管理器 - Processor 模式实现

负责管理会话任务的处理，类似于 Tomcat 的 Processor 组件。
提供任务队列、串行处理、状态跟踪等功能。
"""

import asyncio
import time
import uuid
from typing import Dict, Optional, Callable, Any, Awaitable
from dataclasses import dataclass, field
from enum import Enum
from lucas_common_components.logging import setup_logger

from .connection_manager import ConnectionManager

logger = setup_logger(name=__name__, level="DEBUG")


class SessionStatus(Enum):
    """会话状态枚举"""
    IDLE = "idle"
    PROCESSING = "processing"
    WAITING = "waiting"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class SessionTask:
    """会话任务对象
    
    表示一个会话处理任务，包含任务的基本信息和状态。
    """
    task_id: str
    conversation_id: str
    user_id: str
    request: Any  # 请求对象，类型由具体业务决定
    connection_id: str
    status: SessionStatus = SessionStatus.WAITING
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error: Optional[str] = None
    
    def start(self):
        """标记任务开始"""
        self.status = SessionStatus.PROCESSING
        self.started_at = time.time()
    
    def complete(self):
        """标记任务完成"""
        self.status = SessionStatus.COMPLETED
        self.completed_at = time.time()
    
    def fail(self, error: str):
        """标记任务失败"""
        self.status = SessionStatus.ERROR
        self.error = error
        self.completed_at = time.time()
    
    def get_duration(self) -> Optional[float]:
        """获取任务执行时长
        
        Returns:
            Optional[float]: 执行时长（秒），如果任务未完成则返回None
        """
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


# 会话处理器类型定义 - 返回异步生成器
from typing import AsyncGenerator
SessionProcessor = Callable[[Any, str, str], AsyncGenerator[Any, None]]


class SessionManager:
    """会话管理器 - 类似 Tomcat 的 Processor
    
    负责管理会话任务的处理，确保同一会话的任务串行执行，
    避免重复处理和资源冲突。
    """

    def __init__(self, connection_manager: ConnectionManager, max_queue_size: int = 100):
        """初始化会话管理器
        
        Args:
            connection_manager: 连接管理器实例
            max_queue_size: 每个会话的最大队列大小
        """
        self.connection_manager = connection_manager
        self.max_queue_size = max_queue_size
        
        # 会话状态管理
        self.active_sessions: Dict[str, SessionTask] = {}  # conversation_id -> current task
        self.task_queue: Dict[str, asyncio.Queue] = {}  # conversation_id -> task queue
        self.processing_tasks: Dict[str, asyncio.Task] = {}  # conversation_id -> processing task
        
        # 统计信息
        self.total_tasks_processed = 0
        self.total_processing_time = 0.0
        
        self._lock = asyncio.Lock()

    async def submit_session_task(self, request: Any, user_id: str, connection_id: str, 
                                processor: SessionProcessor) -> str:
        """提交会话任务
        
        Args:
            request: 请求对象（需要有 conversation_id 属性）
            user_id: 用户ID
            connection_id: 连接ID
            processor: 会话处理器函数
            
        Returns:
            str: 任务ID
            
        Raises:
            ValueError: 当请求无效或队列已满时
        """
        # 获取会话ID
        conversation_id = getattr(request, 'conversation_id', None)
        if not conversation_id:
            raise ValueError("Request must have conversation_id attribute")
        
        task_id = str(uuid.uuid4())
        
        session_task = SessionTask(
            task_id=task_id,
            conversation_id=conversation_id,
            user_id=user_id,
            request=request,
            connection_id=connection_id
        )
        
        async with self._lock:
            # 确保会话有任务队列
            if conversation_id not in self.task_queue:
                self.task_queue[conversation_id] = asyncio.Queue(maxsize=self.max_queue_size)
            
            # 检查队列是否已满
            if self.task_queue[conversation_id].full():
                raise ValueError(f"Task queue for conversation {conversation_id} is full")
            
            # 将任务加入队列
            await self.task_queue[conversation_id].put(session_task)
            
            # 如果该会话没有正在处理的任务，启动处理器
            if conversation_id not in self.processing_tasks:
                self.processing_tasks[conversation_id] = asyncio.create_task(
                    self._process_conversation_tasks(conversation_id, processor)
                )
        
        logger.info(f"Submitted session task {task_id} for conversation {conversation_id}")
        return task_id

    async def _process_conversation_tasks(self, conversation_id: str, processor: SessionProcessor):
        """处理会话任务队列
        
        Args:
            conversation_id: 会话ID
            processor: 会话处理器函数
        """
        logger.info(f"Started processing tasks for conversation {conversation_id}")
        
        try:
            while True:
                # 获取下一个任务
                task_queue = self.task_queue.get(conversation_id)
                if not task_queue:
                    break
                
                try:
                    # 等待任务，超时后检查是否还有连接
                    session_task = await asyncio.wait_for(task_queue.get(), timeout=30.0)
                except asyncio.TimeoutError:
                    # 检查是否还有活跃连接
                    connections = await self.connection_manager.get_conversation_connections(conversation_id)
                    if not connections:
                        logger.info(f"No active connections for conversation {conversation_id}, stopping processor")
                        break
                    continue
                
                # 处理任务
                await self._process_single_task(session_task, processor)
                
        except Exception as e:
            logger.error(f"Error in conversation processor for {conversation_id}: {e}")
        finally:
            # 清理处理器
            await self._cleanup_conversation_processor(conversation_id)

    async def _process_single_task(self, session_task: SessionTask, processor: SessionProcessor):
        """处理单个会话任务
        
        Args:
            session_task: 会话任务
            processor: 会话处理器函数
        """
        conversation_id = session_task.conversation_id
        
        try:
            # 更新任务状态
            session_task.start()
            self.active_sessions[conversation_id] = session_task
            
            logger.info(f"Processing task {session_task.task_id} for conversation {conversation_id}")
            
            # 处理会话逻辑
            async for result in processor(session_task.request, session_task.user_id, conversation_id):
                # 广播结果到所有连接
                await self.connection_manager.broadcast_to_conversation(conversation_id, result)
            
            # 任务完成
            session_task.complete()
            
            # 更新统计信息
            duration = session_task.get_duration()
            if duration:
                self.total_tasks_processed += 1
                self.total_processing_time += duration
            
            logger.info(f"Completed task {session_task.task_id} for conversation {conversation_id}")
            
        except Exception as e:
            # 任务出错
            error_msg = str(e)
            session_task.fail(error_msg)
            
            logger.error(f"Error processing task {session_task.task_id}: {e}")
            
            # 发送错误消息
            error_result = {
                "error": True,
                "message": f"处理出错: {error_msg}",
                "task_id": session_task.task_id
            }
            await self.connection_manager.broadcast_to_conversation(conversation_id, error_result)
        finally:
            # 清理活跃会话
            if conversation_id in self.active_sessions:
                del self.active_sessions[conversation_id]

    async def _cleanup_conversation_processor(self, conversation_id: str):
        """清理会话处理器
        
        Args:
            conversation_id: 会话ID
        """
        async with self._lock:
            if conversation_id in self.processing_tasks:
                del self.processing_tasks[conversation_id]
            if conversation_id in self.task_queue:
                del self.task_queue[conversation_id]
            if conversation_id in self.active_sessions:
                del self.active_sessions[conversation_id]
        
        logger.info(f"Cleaned up processor for conversation {conversation_id}")

    async def get_session_status(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取会话状态
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Optional[Dict[str, Any]]: 会话状态信息，如果会话不存在则返回None
        """
        active_task = self.active_sessions.get(conversation_id)
        if not active_task:
            return None
        
        return {
            "task_id": active_task.task_id,
            "status": active_task.status.value,
            "created_at": active_task.created_at,
            "started_at": active_task.started_at,
            "duration": active_task.get_duration(),
            "error": active_task.error
        }

    async def get_queue_status(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取队列状态
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Optional[Dict[str, Any]]: 队列状态信息，如果队列不存在则返回None
        """
        task_queue = self.task_queue.get(conversation_id)
        if not task_queue:
            return None
        
        return {
            "queue_size": task_queue.qsize(),
            "max_size": self.max_queue_size,
            "is_processing": conversation_id in self.processing_tasks
        }

    async def get_manager_stats(self) -> Dict[str, Any]:
        """获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        async with self._lock:
            active_conversations = len(self.active_sessions)
            total_queues = len(self.task_queue)
            total_queue_size = sum(q.qsize() for q in self.task_queue.values())
            
            avg_processing_time = (
                self.total_processing_time / self.total_tasks_processed 
                if self.total_tasks_processed > 0 else 0.0
            )
            
            return {
                "active_conversations": active_conversations,
                "total_queues": total_queues,
                "total_queue_size": total_queue_size,
                "total_tasks_processed": self.total_tasks_processed,
                "average_processing_time": avg_processing_time,
                "max_queue_size": self.max_queue_size
            }

    async def cancel_conversation_tasks(self, conversation_id: str):
        """取消会话的所有任务
        
        Args:
            conversation_id: 会话ID
        """
        async with self._lock:
            # 取消处理任务
            if conversation_id in self.processing_tasks:
                processing_task = self.processing_tasks[conversation_id]
                processing_task.cancel()
                try:
                    await processing_task
                except asyncio.CancelledError:
                    pass
            
            # 清理队列
            if conversation_id in self.task_queue:
                task_queue = self.task_queue[conversation_id]
                while not task_queue.empty():
                    try:
                        task = task_queue.get_nowait()
                        task.fail("Task cancelled")
                    except asyncio.QueueEmpty:
                        break
        
        await self._cleanup_conversation_processor(conversation_id)
        logger.info(f"Cancelled all tasks for conversation {conversation_id}")

    async def shutdown(self):
        """关闭会话管理器"""
        logger.info("Shutting down session manager...")
        
        # 取消所有处理任务
        conversation_ids = list(self.processing_tasks.keys())
        for conversation_id in conversation_ids:
            await self.cancel_conversation_tasks(conversation_id)
        
        # 清理所有状态
        async with self._lock:
            self.active_sessions.clear()
            self.task_queue.clear()
            self.processing_tasks.clear()
        
        logger.info("Session manager shutdown complete")
