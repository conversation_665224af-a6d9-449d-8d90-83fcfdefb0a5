"""Redis 客户端 - 提供 Redis 连接和 pub/sub 功能

负责管理 Redis 连接和提供 pub/sub 功能，用于在分布式环境中广播消息。
"""

import asyncio
import json
from typing import Any, Dict, Optional, Callable, Awaitable

import redis.asyncio as redis
from lucas_common_components.logging import setup_logger

from src.config.config_model import AIChatAppConfig, RedisConfig

logger = setup_logger(name=__name__, level="DEBUG")


class RedisClient:
    """Redis 客户端 - 单例模式
    
    提供 Redis 连接和 pub/sub 功能，用于在分布式环境中广播消息。
    使用单例模式确保应用中只有一个 Redis 连接池。
    """
    
    _instance: Optional['RedisClient'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        """初始化 Redis 客户端"""
        self._redis: Optional[redis.Redis] = None
        self._pubsub: Optional[redis.client.PubSub] = None
        self._initialized = False
        self._subscribers: Dict[str, Dict[str, Callable[[str, Any], Awaitable[None]]]] = {}
        self._listener_thread = None
        self._stop_listener = False  # 停止标志，用于通知监听线程退出
        self._listener_started = False  # 标记监听器是否已启动
    
    @classmethod
    async def get_instance(cls) -> 'RedisClient':
        """获取 RedisClient 单例实例
        
        Returns:
            RedisClient: Redis 客户端实例
        """
        async with cls._lock:
            if cls._instance is None:
                cls._instance = RedisClient()
                await cls._instance.initialize()
            return cls._instance
    
    async def initialize(self):
        """初始化 Redis 连接
        
        从配置中获取 Redis 连接信息并建立连接。
        """
        if self._initialized:
            return
        
        try:
            # 获取 Redis 配置
            config = AIChatAppConfig.get_instance().redis
            if not config:
                logger.error("Redis 配置不存在，无法初始化 Redis 客户端")
                return
            
            # 创建 Redis 连接
            self._redis = await self._create_redis_connection(config)
            self._pubsub = self._redis.pubsub()
            
            self._initialized = True
            logger.info("Redis 客户端初始化成功")
        except Exception as e:
            logger.error(f"Redis 客户端初始化失败: {e}")
            raise
    
    async def _create_redis_connection(self, config: RedisConfig) -> redis.Redis:
        """创建 Redis 连接
        
        Args:
            config: Redis 配置
            
        Returns:
            redis.Redis: Redis 连接对象
        """
        connection_params = {
            "host": config.host,
            "port": config.port,
            "db": config.db or 0,
            "decode_responses": True,  # 自动解码响应
        }
        
        # 添加可选的认证信息
        if config.username:
            connection_params["username"] = config.username
        if config.password:
            connection_params["password"] = config.password
        
        # 添加连接池配置
        if config.max_connections:
            connection_params["max_connections"] = config.max_connections
        
        return redis.Redis(**connection_params)
    
    async def publish(self, channel: str, message: Any):
        """发布消息到指定频道
        
        Args:
            channel: 频道名称
            message: 要发布的消息，将被转换为 JSON 字符串
        """
        if not self._initialized or not self._redis:
            await self.initialize()
        
        try:
            # 将消息转换为 JSON 字符串
            message_str = json.dumps(message) if not isinstance(message, str) else message
            await self._redis.publish(channel, message_str)
            logger.debug(f"消息已发布到频道 {channel}")
        except Exception as e:
            logger.error(f"发布消息到频道 {channel} 失败: {e}")
            raise
    
    async def subscribe(self, channel: str, callback: Callable[[str, Any], Awaitable[None]], subscriber_id: str):
        """订阅频道
        
        Args:
            channel: 频道名称
            callback: 回调函数，接收频道名和消息作为参数
            subscriber_id: 订阅者 ID，用于后续取消订阅
        """
        if not self._initialized or not self._redis or not self._pubsub:
            await self.initialize()
        
        try:
            # 如果是第一个订阅者，则订阅该频道
            if channel not in self._subscribers or not self._subscribers[channel]:
                await self._pubsub.subscribe(channel)
                logger.info(f"已订阅频道 {channel}")
                
                # 如果监听线程尚未启动，则启动它
                if not self._listener_started:
                    self._start_listener_thread()
            
            # 保存回调函数
            if channel not in self._subscribers:
                self._subscribers[channel] = {}
            self._subscribers[channel][subscriber_id] = callback
            logger.debug(f"订阅者 {subscriber_id} 已订阅频道 {channel}")
        except Exception as e:
            logger.error(f"订阅频道 {channel} 失败: {e}")
            raise
    
    async def unsubscribe(self, channel: str, subscriber_id: str):
        """取消订阅
        
        Args:
            channel: 频道名称
            subscriber_id: 订阅者 ID
        """
        if not self._initialized or not self._redis or not self._pubsub:
            return
        
        try:
            # 移除订阅者
            if channel in self._subscribers and subscriber_id in self._subscribers[channel]:
                del self._subscribers[channel][subscriber_id]
                logger.debug(f"订阅者 {subscriber_id} 已取消订阅频道 {channel}")
            
            # 如果没有订阅者了，则取消订阅该频道
            if channel in self._subscribers and not self._subscribers[channel]:
                await self._pubsub.unsubscribe(channel)
                del self._subscribers[channel]
                logger.info(f"已取消订阅频道 {channel}")
        except Exception as e:
            logger.error(f"取消订阅频道 {channel} 失败: {e}")
    
    def _start_listener_thread(self):
        """启动监听线程
        
        在单独的线程中运行消息监听器，避免阻塞主线程。
        只有在第一次订阅时才会启动。
        """
        if self._listener_started or self._listener_thread is not None:
            return
        
        import threading
        
        # 在新线程中创建新的事件循环
        def run_listener_in_thread():
            # 为线程创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # 在新的事件循环中运行监听任务
                # 创建一个任务来运行消息监听器
                listener_task = loop.create_task(self._message_listener())
                
                # 定期检查停止标志
                while not self._stop_listener and not listener_task.done():
                    loop.run_until_complete(asyncio.sleep(0.5))
                
                # 如果任务还在运行但收到了停止信号，取消任务
                if not listener_task.done():
                    listener_task.cancel()
                    try:
                        loop.run_until_complete(listener_task)
                    except asyncio.CancelledError:
                        logger.info("监听任务已取消")
            except Exception as e:
                logger.error(f"监听线程异常: {e}")
            finally:
                loop.close()
        
        self._listener_thread = threading.Thread(target=run_listener_in_thread)
        self._listener_thread.daemon = True  # 设置为守护线程，随主线程退出
        self._listener_thread.start()
        self._listener_started = True
        logger.info("消息监听线程已启动")
    
    async def _message_listener(self):
        """消息监听器
        
        监听所有订阅的频道，并调用相应的回调函数。
        在单独的线程中运行，通过检查停止标志来决定是否退出。
        """
        if not self._pubsub:
            return
        
        try:
            # 使用 aiter 和 anext 手动迭代，以便能够检查停止标志
            pubsub_aiter = self._pubsub.listen().__aiter__()
            while not self._stop_listener:
                try:
                    # 设置超时，以便定期检查停止标志
                    message = await asyncio.wait_for(pubsub_aiter.__anext__(), timeout=1.0)
                    
                    if message["type"] == "message":
                        channel = message["channel"]
                        data = message["data"]
                        
                        # 尝试解析 JSON
                        try:
                            if isinstance(data, str):
                                data = json.loads(data)
                        except json.JSONDecodeError:
                            # 如果不是有效的 JSON，保持原样
                            pass
                        
                        # 调用所有订阅者的回调函数
                        if channel in self._subscribers:
                            for subscriber_id, callback in self._subscribers[channel].items():
                                try:
                                    await callback(channel, data)
                                except Exception as e:
                                    logger.error(f"调用订阅者 {subscriber_id} 的回调函数失败: {e}")
                except asyncio.TimeoutError:
                    # 超时，继续循环并检查停止标志
                    continue
                except StopAsyncIteration:
                    # 迭代结束
                    break
        except asyncio.CancelledError:
            logger.info("消息监听器已取消")
        except Exception as e:
            logger.error(f"消息监听器异常: {e}")
        
        logger.info("消息监听器已退出")
    
    async def close(self):
        """关闭 Redis 连接"""
        if not self._initialized:
            return
        
        try:
            # 设置停止标志，让监听线程自行退出
            self._stop_listener = True
            
            # 关闭 PubSub
            if self._pubsub:
                await self._pubsub.close()
            
            # 关闭 Redis 连接
            if self._redis:
                await self._redis.close()
            
            # 等待监听线程结束（设置超时，避免无限等待）
            if hasattr(self, '_listener_thread') and self._listener_thread and self._listener_thread.is_alive():
                import threading
                if threading.current_thread() != self._listener_thread:  # 避免自己等待自己
                    self._listener_thread.join(timeout=2.0)  # 最多等待2秒
            
            self._initialized = False
            self._listener_started = False
            self._listener_thread = None
            logger.info("Redis 客户端已关闭")
        except Exception as e:
            logger.error(f"关闭 Redis 客户端失败: {e}")
    
    def __del__(self):
        """析构函数，确保资源被释放"""
        # 设置停止标志，通知监听线程退出
        if hasattr(self, '_stop_listener'):
            self._stop_listener = True
        
        # 重置监听线程状态
        if hasattr(self, '_listener_started'):
            self._listener_started = False