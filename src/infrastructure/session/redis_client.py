"""
Redis 客户端

提供 Redis 连接和 pub/sub 功能，用于在分布式环境中广播消息。
使用单例模式确保应用中只有一个 Redis 连接池。
"""

import asyncio
import json
import redis.asyncio as redis
from typing import Dict, Optional, Callable, Any, Awaitable
from lucas_common_components.logging import setup_logger
from src.config.config_model import AIChatAppConfig, RedisConfig

logger = setup_logger(name=__name__, level="DEBUG")


class RedisClient:
    """Redis 客户端 - 单例模式
    
    提供 Redis 连接和 pub/sub 功能，使用单一事件循环处理所有操作。
    """
    
    _instance: Optional['RedisClient'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        """初始化 Redis 客户端"""
        self._redis: Optional[redis.Redis] = None
        self._pubsub: Optional[redis.client.PubSub] = None
        self._initialized = False
        self._subscribers: Dict[str, Dict[str, Callable[[str, Any], Awaitable[None]]]] = {}
        self._listener_task: Optional[asyncio.Task] = None
        self._stop_listener = False
    
    @classmethod
    async def get_instance(cls) -> 'RedisClient':
        """获取 RedisClient 单例实例
        
        Returns:
            RedisClient: Redis 客户端实例
        """
        async with cls._lock:
            if cls._instance is None:
                cls._instance = RedisClient()
                await cls._instance.initialize()
            return cls._instance
    
    async def initialize(self):
        """初始化 Redis 连接"""
        if self._initialized:
            return
        
        try:
            # 获取 Redis 配置
            config = AIChatAppConfig.get_instance().redis
            if not config:
                logger.error("Redis 配置不存在，无法初始化 Redis 客户端")
                return
            
            # 创建 Redis 连接
            self._redis = await self._create_redis_connection(config)
            self._pubsub = self._redis.pubsub()
            
            self._initialized = True
            logger.info("Redis 客户端初始化成功")
        except Exception as e:
            logger.error(f"Redis 客户端初始化失败: {e}")
            raise
    
    async def _create_redis_connection(self, config: RedisConfig) -> redis.Redis:
        """创建 Redis 连接"""
        connection_params = {
            "host": config.host,
            "port": config.port,
            "db": config.db or 0,
            "decode_responses": True,
        }
        
        if config.username:
            connection_params["username"] = config.username
        if config.password:
            connection_params["password"] = config.password
        if config.max_connections:
            connection_params["max_connections"] = config.max_connections
        
        return redis.Redis(**connection_params)
    
    async def publish(self, channel: str, message: Any):
        """发布消息到指定频道"""
        if not self._initialized or not self._redis:
            await self.initialize()
        
        try:
            message_str = json.dumps(message) if not isinstance(message, str) else message
            await self._redis.publish(channel, message_str)
            logger.debug(f"消息已发布到频道 {channel}")
        except Exception as e:
            logger.error(f"发布消息到频道 {channel} 失败: {e}")
            raise
    
    async def subscribe(self, channel: str, callback: Callable[[str, Any], Awaitable[None]], subscriber_id: str):
        """订阅频道"""
        if not self._initialized or not self._redis or not self._pubsub:
            await self.initialize()
        
        try:
            # 如果是第一个订阅者，则订阅该频道
            if channel not in self._subscribers or not self._subscribers[channel]:
                await self._pubsub.subscribe(channel)
                logger.info(f"已订阅频道 {channel}")
                
                # 如果监听器尚未启动，则启动它
                if not self._listener_task or self._listener_task.done():
                    await self._start_listener()
            
            # 保存回调函数
            if channel not in self._subscribers:
                self._subscribers[channel] = {}
            self._subscribers[channel][subscriber_id] = callback
            logger.debug(f"订阅者 {subscriber_id} 已订阅频道 {channel}")
        except Exception as e:
            logger.error(f"订阅频道 {channel} 失败: {e}")
            raise
    
    async def unsubscribe(self, channel: str, subscriber_id: str):
        """取消订阅"""
        if not self._initialized or not self._redis or not self._pubsub:
            return
        
        try:
            # 移除订阅者
            if channel in self._subscribers and subscriber_id in self._subscribers[channel]:
                del self._subscribers[channel][subscriber_id]
                logger.debug(f"订阅者 {subscriber_id} 已取消订阅频道 {channel}")
            
            # 如果没有订阅者了，则取消订阅该频道
            if channel in self._subscribers and not self._subscribers[channel]:
                await self._pubsub.unsubscribe(channel)
                del self._subscribers[channel]
                logger.info(f"已取消订阅频道 {channel}")
        except Exception as e:
            logger.error(f"取消订阅频道 {channel} 失败: {e}")
    
    async def _start_listener(self):
        """启动消息监听器"""
        if self._listener_task and not self._listener_task.done():
            return
        
        self._stop_listener = False
        self._listener_task = asyncio.create_task(self._message_listener())
        logger.info("Redis 消息监听器已启动")
    
    async def _message_listener(self):
        """消息监听器 - 在同一事件循环中运行"""
        if not self._pubsub:
            return
        
        try:
            async for message in self._pubsub.listen():
                if self._stop_listener:
                    break
                
                if message["type"] == "message":
                    channel = message["channel"]
                    data = message["data"]
                    
                    # 尝试解析 JSON
                    try:
                        if isinstance(data, str):
                            data = json.loads(data)
                    except json.JSONDecodeError:
                        pass
                    
                    # 调用所有订阅者的回调函数
                    if channel in self._subscribers:
                        for subscriber_id, callback in self._subscribers[channel].items():
                            try:
                                # 在同一事件循环中执行回调
                                await callback(channel, data)
                                logger.debug(f"成功调用订阅者 {subscriber_id} 的回调函数")
                            except Exception as e:
                                logger.error(f"调用订阅者 {subscriber_id} 的回调函数失败: {e}")
        except asyncio.CancelledError:
            logger.info("Redis 消息监听器已取消")
        except Exception as e:
            logger.error(f"Redis 消息监听器异常: {e}")
        finally:
            logger.info("Redis 消息监听器已退出")
    
    async def close(self):
        """关闭 Redis 连接"""
        if not self._initialized:
            return
        
        try:
            # 停止监听器
            self._stop_listener = True
            if self._listener_task and not self._listener_task.done():
                self._listener_task.cancel()
                try:
                    await self._listener_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭 PubSub
            if self._pubsub:
                await self._pubsub.close()
            
            # 关闭 Redis 连接
            if self._redis:
                await self._redis.close()
            
            self._initialized = False
            logger.info("Redis 客户端已关闭")
        except Exception as e:
            logger.error(f"关闭 Redis 客户端失败: {e}")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, '_stop_listener'):
            self._stop_listener = True
