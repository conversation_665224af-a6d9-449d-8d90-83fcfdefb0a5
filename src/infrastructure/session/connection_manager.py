"""
连接管理器 - Acceptor 模式实现

负责管理 WebSocket/SSE 连接的生命周期
提供连接创建、销毁、广播等功能。
"""

import asyncio
import uuid
from typing import Dict, Optional, Set, Any
from .models import Connection, ConnectionStatus
from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")


class ConnectionManager:
    """连接管理器 - 类似 Tomcat 的 Acceptor
    
    负责管理所有客户端连接的生命周期，提供连接的创建、销毁、
    广播等功能。支持同一会话的多个连接。
    """

    def __init__(self, max_connections_per_conversation: int = 1):
        """初始化连接管理器
        
        Args:
            max_connections_per_conversation: 每个会话的最大连接数
        """
        self.connections: Dict[str, Connection] = {}
        self.conversation_connections: Dict[str, Set[str]] = {}  # conversation_id -> connection_ids
        self.max_connections_per_conversation = max_connections_per_conversation
        self._lock = asyncio.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()

    def _start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """定期清理过期连接"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self._cleanup_expired_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")

    async def _cleanup_expired_connections(self):
        """清理过期连接"""
        expired_connections = []

        async with self._lock:
            for connection_id, connection in self.connections.items():
                if connection.is_expired():
                    expired_connections.append(connection_id)

        for connection_id in expired_connections:
            await self.remove_connection(connection_id)
            logger.info(f"Cleaned up expired connection: {connection_id}")

    async def add_connection(self, conversation_id: str, user_id: str) -> Connection:
        """添加新连接
        
        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            
        Returns:
            Connection: 创建的连接对象
            
        Raises:
            ValueError: 当会话连接数超过限制时
        """
        connection_id = str(uuid.uuid4())

        async with self._lock:
            # 检查会话连接数限制
            if conversation_id in self.conversation_connections:
                if len(self.conversation_connections[conversation_id]) >= self.max_connections_per_conversation:
                    raise ValueError(f"Too many connections for conversation {conversation_id}")

            # 创建连接对象
            connection = Connection(
                connection_id=connection_id,
                conversation_id=conversation_id,
                user_id=user_id
            )

            # 添加到管理器
            self.connections[connection_id] = connection
            if conversation_id not in self.conversation_connections:
                self.conversation_connections[conversation_id] = set()
            self.conversation_connections[conversation_id].add(connection_id)

        logger.info(f"Added connection {connection_id} for conversation {conversation_id}")
        return connection

    async def remove_connection(self, connection_id: str):
        """移除连接
        
        Args:
            connection_id: 连接ID
        """
        async with self._lock:
            if connection_id in self.connections:
                connection = self.connections[connection_id]
                connection.status = ConnectionStatus.CLOSED
                connection.close_event.set()

                # 从会话连接映射中移除
                conversation_id = connection.conversation_id
                if conversation_id in self.conversation_connections:
                    self.conversation_connections[conversation_id].discard(connection_id)
                    if not self.conversation_connections[conversation_id]:
                        del self.conversation_connections[conversation_id]

                del self.connections[connection_id]
                logger.info(f"Removed connection {connection_id}")

    async def get_connection(self, connection_id: str) -> Optional[Connection]:
        """获取连接
        
        Args:
            connection_id: 连接ID
            
        Returns:
            Optional[Connection]: 连接对象，如果不存在则返回None
        """
        return self.connections.get(connection_id)

    async def get_conversation_connections(self, conversation_id: str) -> Set[str]:
        """获取会话的所有连接ID
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Set[str]: 连接ID集合
        """
        return self.conversation_connections.get(conversation_id, set()).copy()

    async def broadcast_to_conversation(self, conversation_id: str, message: Any):
        """向会话的所有连接广播消息
        
        Args:
            conversation_id: 会话ID
            message: 要广播的消息
        """
        connection_ids = await self.get_conversation_connections(conversation_id)
        if not connection_ids:
            logger.debug(f"No active connections for conversation {conversation_id}")
            return

        broadcast_tasks = []
        for connection_id in connection_ids:
            connection = await self.get_connection(connection_id)
            if connection and connection.status == ConnectionStatus.ACTIVE:
                task = self._send_to_connection(connection, message)
                broadcast_tasks.append(task)

        if broadcast_tasks:
            # 并发发送消息
            results = await asyncio.gather(*broadcast_tasks, return_exceptions=True)

            # 处理发送失败的连接
            failed_connections = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    connection_id = list(connection_ids)[i]
                    failed_connections.append(connection_id)
                    logger.error(f"Failed to send message to connection {connection_id}: {result}")

            # 移除失败的连接
            for connection_id in failed_connections:
                await self.remove_connection(connection_id)

    async def _send_to_connection(self, connection: Connection, message: Any):
        """向单个连接发送消息
        
        Args:
            connection: 连接对象
            message: 消息内容
        """
        try:
            connection.update_activity()
            await connection.response_queue.put(message)
        except Exception as e:
            logger.error(f"Failed to send message to connection {connection.connection_id}: {e}")
            raise

    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        async with self._lock:
            total_connections = len(self.connections)
            active_connections = sum(1 for conn in self.connections.values()
                                     if conn.status == ConnectionStatus.ACTIVE)
            total_conversations = len(self.conversation_connections)

            # 按会话统计连接数
            connections_per_conversation = {
                conv_id: len(conn_ids)
                for conv_id, conn_ids in self.conversation_connections.items()
            }

            return {
                "total_connections": total_connections,
                "active_connections": active_connections,
                "total_conversations": total_conversations,
                "connections_per_conversation": connections_per_conversation,
                "max_connections_per_conversation": self.max_connections_per_conversation
            }

    async def close_all_connections(self):
        """关闭所有连接"""
        connection_ids = list(self.connections.keys())
        for connection_id in connection_ids:
            await self.remove_connection(connection_id)

        # 取消清理任务
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        logger.info("Closed all connections")

    def __del__(self):
        """析构函数，确保清理任务被取消"""
        if hasattr(self, '_cleanup_task') and self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
