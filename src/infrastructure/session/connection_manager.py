"""连接管理器 - Acceptor 模式实现

负责管理 WebSocket/SSE 连接的生命周期
提供连接创建、销毁、广播等功能。
支持通过消息广播接口在分布式环境中广播消息。
"""

import asyncio
import uuid
from typing import Dict, Optional, Set, Any
from .models import Connection, ConnectionStatus
from .message_broadcaster import MessageBroadcaster, MessageBroadcasterFactory
from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")


class ConnectionManager:
    """连接管理器 - 类似 Tomcat 的 Acceptor
    
    负责管理所有客户端连接的生命周期，提供连接的创建、销毁、
    广播等功能。支持同一会话的多个连接。
    支持通过消息广播接口在分布式环境中广播消息。
    """

    def __init__(self, max_connections_per_conversation: int = 1):
        """初始化连接管理器
        
        Args:
            max_connections_per_conversation: 每个会话的最大连接数
            use_distributed: 是否使用分布式广播（如 Redis）
        """
        self.connections: Dict[str, Connection] = {}
        self.conversation_connections: Dict[str, Set[str]] = {}  # conversation_id -> connection_ids
        self.max_connections_per_conversation = max_connections_per_conversation
        self._lock = asyncio.Lock()  # 用于保护共享资源的锁
        self._cleanup_task: Optional[asyncio.Task] = None
        self._broadcaster: Optional[MessageBroadcaster] = None
        self._broadcaster_init_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()

        # 异步初始化消息广播器
        self._broadcaster_init_task = asyncio.create_task(self._init_broadcaster())

    async def _init_broadcaster(self):
        """初始化消息广播器"""

        self._broadcaster = await MessageBroadcasterFactory.create()
        logger.info("消息广播器初始化成功")

        # 订阅消息频道 - 不在这里等待，避免任务等待自身
        # 创建一个新的任务来订阅频道
        asyncio.create_task(self._subscribe_to_conversation_message_channel())

    async def _handle_broadcast_message(self, channel: str, message: Any):
        """处理从广播器接收到的消息
        
        Args:
            channel: 频道名称
            message: 消息内容
        """
        try:
            # 检查是消息频道
            if channel == "conversation_message":
                # 从消息中获取会话ID
                if isinstance(message, dict) and "conversation_id" in message:
                    conversation_id = message["conversation_id"]
                    msg_content = message.get("content")

                    # 检查该会话是否在本地管理的连接中
                    if conversation_id in self.conversation_connections:
                        # 在本地广播消息
                        await self._local_broadcast_to_conversation(conversation_id, msg_content)
                else:
                    logger.warning(f"收到的消息格式不正确: {message}")
                    return
        except Exception as e:
            logger.error(f"处理广播消息失败: {e}")

    async def _local_broadcast_to_conversation(self, conversation_id: str, message: Any):
        """向本地的会话连接广播消息
        
        Args:
            conversation_id: 会话ID
            message: 要广播的消息
        """
        try:
            # 获取会话的所有连接ID
            connection_ids = await self.get_conversation_connections(conversation_id)

            # 向每个连接发送消息
            for connection_id in connection_ids:
                connection = await self.get_connection(connection_id)
                if connection and connection.status == ConnectionStatus.ACTIVE:
                    try:
                        await self._send_to_connection(connection, message)
                    except Exception as e:
                        logger.error(f"向连接 {connection_id} 发送消息失败: {e}")
        except Exception as e:
            logger.error(f"本地广播消息到会话 {conversation_id} 失败: {e}")

    async def ensure_broadcaster_ready(self):
        """确保消息广播器已初始化
        
        Returns:
            bool: 广播器是否就绪
        """
        # 不等待初始化任务，只检查广播器是否已就绪
        # 如果初始化任务仍在进行中，则返回False
        if self._broadcaster_init_task and not self._broadcaster_init_task.done():
            return False
            
        return self._broadcaster is not None

    def _start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """定期清理过期连接"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self._cleanup_expired_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")

    async def _cleanup_expired_connections(self):
        """清理过期连接"""
        expired_connections = []

        async with self._lock:
            for connection_id, connection in self.connections.items():
                if connection.is_expired():
                    expired_connections.append(connection_id)

        for connection_id in expired_connections:
            await self.remove_connection(connection_id)
            logger.info(f"Cleaned up expired connection: {connection_id}")

    async def add_connection(self, conversation_id: str, user_id: str) -> Connection:
        """添加新连接
        
        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            
        Returns:
            Connection: 创建的连接对象
            
        Raises:
            ValueError: 当会话连接数超过限制时
        """
        connection_id = str(uuid.uuid4())

        async with self._lock:
            # 检查会话连接数限制
            if conversation_id in self.conversation_connections:
                if len(self.conversation_connections[conversation_id]) >= self.max_connections_per_conversation:
                    raise ValueError(f"Too many connections for conversation {conversation_id}")

            # 创建连接对象
            connection = Connection(
                connection_id=connection_id,
                conversation_id=conversation_id,
                user_id=user_id
            )

            # 添加到管理器
            self.connections[connection_id] = connection
            if conversation_id not in self.conversation_connections:
                self.conversation_connections[conversation_id] = set()
            self.conversation_connections[conversation_id].add(connection_id)

        logger.info(f"Added connection {connection_id} for conversation {conversation_id}")
        return connection

    async def _subscribe_to_conversation_message_channel(self):
        """订阅会话消息频道
        """
        # 检查广播器是否就绪
        broadcaster_ready = await self.ensure_broadcaster_ready()
        if not broadcaster_ready:
            logger.warning("广播器未就绪，无法订阅消息频道")
            return

        try:
            # 订阅消息频道
            channel = "conversation_message"
            success = await self._broadcaster.subscribe(
                channel,
                self._handle_broadcast_message,
                f"local_instance_{id(self)}"
            )
            if success:
                logger.info(f"已订阅会话消息频道 {channel}")
            else:
                logger.warning(f"订阅会话消息频道 {channel} 失败")
        except Exception as e:
            logger.error(f"订阅会话消息频道失败: {e}")
            # 订阅失败不影响正常功能，只是无法接收其他实例的消息

    async def remove_connection(self, connection_id: str):
        """移除连接
        
        Args:
            connection_id: 连接ID
        """
        conversation_id = None
        is_last_connection = False

        async with self._lock:
            if connection_id in self.connections:
                connection = self.connections[connection_id]
                connection.status = ConnectionStatus.CLOSED
                connection.close_event.set()

                # 从会话连接映射中移除
                conversation_id = connection.conversation_id
                if conversation_id in self.conversation_connections:
                    self.conversation_connections[conversation_id].discard(connection_id)
                    if not self.conversation_connections[conversation_id]:
                        del self.conversation_connections[conversation_id]
                        is_last_connection = True

                del self.connections[connection_id]
                logger.info(f"Removed connection {connection_id}")

    async def get_connection(self, connection_id: str) -> Optional[Connection]:
        """获取连接
        
        Args:
            connection_id: 连接ID
            
        Returns:
            Optional[Connection]: 连接对象，如果不存在则返回None
        """
        return self.connections.get(connection_id)

    async def get_conversation_connections(self, conversation_id: str) -> Set[str]:
        """获取会话的所有连接ID
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Set[str]: 连接ID集合
        """
        return self.conversation_connections.get(conversation_id, set()).copy()

    async def broadcast_to_conversation(self, conversation_id: str, message: Any):
        """向会话的所有连接广播消息
        
        在分布式环境中，使用消息广播器广播消息到所有服务实例。
        在单机环境中，直接广播到本地连接。
        
        Args:
            conversation_id: 会话ID
            message: 要广播的消息
        """
        # 检查广播器是否就绪
        broadcaster_ready = await self.ensure_broadcaster_ready()

        # 标记是否需要本地广播
        need_local_broadcast = True

        if broadcaster_ready:
            try:
                # 使用广播器发布消息到频道
                channel = "conversation_message"
                # 构造包含会话ID的消息
                wrapped_message = {
                    "conversation_id": conversation_id,
                    "content": message
                }
                success = await self._broadcaster.publish(channel, wrapped_message)
                
                # 如果发布成功，则不需要本地广播（因为本地也会收到广播消息）
                if success:
                    need_local_broadcast = False
                else:
                    logger.warning(f"通过广播器发布消息到频道 {channel} 失败，将使用本地广播")
            except Exception as e:
                logger.error(f"通过广播器发布消息失败: {e}，将使用本地广播")
        
        # 如果广播器未就绪或发布失败，使用本地广播
        if need_local_broadcast:
            # 在单机环境中直接广播到本地连接
            await self._local_broadcast_to_conversation(conversation_id, message)

    async def _send_to_connection(self, connection: Connection, message: Any):
        """向单个连接发送消息
        
        Args:
            connection: 连接对象
            message: 消息内容
        """
        try:
            connection.update_activity()
            await connection.response_queue.put(message)
        except Exception as e:
            logger.error(f"Failed to send message to connection {connection.connection_id}: {e}")
            raise

    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        async with self._lock:
            total_connections = len(self.connections)
            active_connections = sum(1 for conn in self.connections.values()
                                     if conn.status == ConnectionStatus.ACTIVE)
            total_conversations = len(self.conversation_connections)

            # 按会话统计连接数
            connections_per_conversation = {
                conv_id: len(conn_ids)
                for conv_id, conn_ids in self.conversation_connections.items()
            }

            return {
                "total_connections": total_connections,
                "active_connections": active_connections,
                "total_conversations": total_conversations,
                "connections_per_conversation": connections_per_conversation,
                "max_connections_per_conversation": self.max_connections_per_conversation
            }

    async def close_all_connections(self):
        """关闭所有连接"""
        connection_ids = list(self.connections.keys())
        for connection_id in connection_ids:
            await self.remove_connection(connection_id)

        # 取消清理任务
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # 关闭广播器
        if self._broadcaster:
            try:
                await self._broadcaster.close()
                logger.info("消息广播器已关闭")
            except Exception as e:
                logger.error(f"关闭消息广播器失败: {e}")

        logger.info("Closed all connections")

    def __del__(self):
        """析构函数，确保清理任务被取消"""
        if hasattr(self, '_cleanup_task') and self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()

        # 尝试关闭广播器
        if hasattr(self, '_broadcaster') and self._broadcaster:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self._broadcaster.close())
            except Exception:
                pass
