import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional


class ConnectionStatus(Enum):
    """连接状态枚举"""
    ACTIVE = "active"
    CLOSED = "closed"
    WAITING = "waiting"


@dataclass
class Connection:
    """连接对象

    表示一个客户端连接，包含连接的基本信息和通信队列。
    """
    connection_id: str
    conversation_id: str
    user_id: str
    status: ConnectionStatus = ConnectionStatus.ACTIVE
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    response_queue: asyncio.Queue = field(default_factory=asyncio.Queue)
    close_event: asyncio.Event = field(default_factory=asyncio.Event)

    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = time.time()

    def is_expired(self, timeout_seconds: float = 300.0) -> bool:
        """检查连接是否过期

        Args:
            timeout_seconds: 超时时间（秒）

        Returns:
            bool: 是否过期
        """
        return time.time() - self.last_activity > timeout_seconds


class SessionStatus(Enum):
    """会话状态枚举"""
    IDLE = "idle"
    PROCESSING = "processing"
    WAITING = "waiting"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class SessionTask:
    """会话任务对象

    表示一个会话处理任务，包含任务的基本信息和状态。
    """
    task_id: str
    conversation_id: str
    user_id: str
    request: Any  # 请求对象，类型由具体业务决定
    connection_id: str
    status: SessionStatus = SessionStatus.WAITING
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error: Optional[str] = None

    def start(self):
        """标记任务开始"""
        self.status = SessionStatus.PROCESSING
        self.started_at = time.time()

    def complete(self):
        """标记任务完成"""
        self.status = SessionStatus.COMPLETED
        self.completed_at = time.time()

    def fail(self, error: str):
        """标记任务失败"""
        self.status = SessionStatus.ERROR
        self.error = error
        self.completed_at = time.time()

    def get_duration(self) -> Optional[float]:
        """获取任务执行时长

        Returns:
            Optional[float]: 执行时长（秒），如果任务未完成则返回None
        """
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
