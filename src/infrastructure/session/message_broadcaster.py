"""消息广播接口及实现

定义消息广播接口及其实现，用于在分布式环境中广播消息。
提供本地广播和基于 Redis 的分布式广播实现。(后续可基于此接口进行更可靠的MQ实现)
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Callable, Awaitable

from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")


class MessageBroadcaster(ABC):
    """消息广播接口
    
    定义消息广播的基本接口，包括订阅、取消订阅、发布消息等功能。
    """

    @abstractmethod
    async def subscribe(self, channel: str, callback: Callable[[str, Any], Awaitable[None]],
                        subscriber_id: str) -> bool:
        """订阅频道
        
        Args:
            channel: 频道名称
            callback: 回调函数，接收频道名和消息作为参数
            subscriber_id: 订阅者 ID，用于后续取消订阅
            
        Returns:
            bool: 订阅是否成功
        """
        pass

    @abstractmethod
    async def unsubscribe(self, channel: str, subscriber_id: str) -> bool:
        """取消订阅
        
        Args:
            channel: 频道名称
            subscriber_id: 订阅者 ID
            
        Returns:
            bool: 取消订阅是否成功
        """
        pass

    @abstractmethod
    async def publish(self, channel: str, message: Any) -> bool:
        """发布消息
        
        Args:
            channel: 频道名称
            message: 要发布的消息
            
        Returns:
            bool: 发布是否成功
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """关闭广播器，释放资源"""
        pass


class RedisMessageBroadcaster(MessageBroadcaster):
    """基于 Redis 的分布式消息广播实现
    
    使用 Redis pub/sub 机制实现分布式消息广播。
    """

    def __init__(self):
        """初始化 Redis 消息广播器"""
        self._redis_client = None
        self._initialized = False
        self._lock = asyncio.Lock()

    async def initialize(self) -> bool:
        """初始化 Redis 客户端
        
        Returns:
            bool: 初始化是否成功
        """
        if self._initialized:
            return True

        try:
            from .redis_client import RedisClient
            self._redis_client = await RedisClient.get_instance()
            self._initialized = True
            logger.info("Redis 消息广播器初始化成功")
            return True
        except Exception as e:
            logger.error(f"Redis 消息广播器初始化失败: {e}")
            return False

    async def _ensure_initialized(self) -> bool:
        """确保 Redis 客户端已初始化
        
        Returns:
            bool: 是否已初始化
        """
        if not self._initialized:
            return await self.initialize()
        return self._initialized

    async def subscribe(self, channel: str, callback: Callable[[str, Any], Awaitable[None]],
                        subscriber_id: str) -> bool:
        """订阅频道
        
        Args:
            channel: 频道名称
            callback: 回调函数，接收频道名和消息作为参数
            subscriber_id: 订阅者 ID，用于后续取消订阅
            
        Returns:
            bool: 订阅是否成功
        """
        if not await self._ensure_initialized():
            return False

        try:
            await self._redis_client.subscribe(channel, callback, subscriber_id)
            logger.debug(f"Redis 订阅者 {subscriber_id} 已订阅频道 {channel}")
            return True
        except Exception as e:
            logger.error(f"Redis 订阅频道 {channel} 失败: {e}")
            return False

    async def unsubscribe(self, channel: str, subscriber_id: str) -> bool:
        """取消订阅
        
        Args:
            channel: 频道名称
            subscriber_id: 订阅者 ID
            
        Returns:
            bool: 取消订阅是否成功
        """
        if not await self._ensure_initialized():
            return False

        try:
            await self._redis_client.unsubscribe(channel, subscriber_id)
            logger.debug(f"Redis 订阅者 {subscriber_id} 已取消订阅频道 {channel}")
            return True
        except Exception as e:
            logger.error(f"Redis 取消订阅频道 {channel} 失败: {e}")
            return False

    async def publish(self, channel: str, message: Any) -> bool:
        """发布消息到频道

        Args:
            channel: 频道名称
            message: 消息内容
            
        Returns:
            bool: 发布是否成功
        """
        if not await self._ensure_initialized():
            return False

        try:
            await self._redis_client.publish(channel, message)
            logger.debug(f"Redis 消息已发布到频道 {channel}")
            return True
        except Exception as e:
            logger.error(f"Redis 发布消息到频道 {channel} 失败: {e}")
            return False

    async def close(self) -> None:
        """关闭广播器，释放资源"""
        if self._initialized and self._redis_client:
            try:
                await self._redis_client.close()
                self._initialized = False
                logger.info("Redis 消息广播器已关闭")
            except Exception as e:
                logger.error(f"关闭 Redis 消息广播器失败: {e}")


class MessageBroadcasterFactory:
    """消息广播器工厂
    
    用于创建不同类型的消息广播器实例。
    """

    @staticmethod
    async def create() -> MessageBroadcaster:
        """创建消息广播器

        Returns:
            MessageBroadcaster: 消息广播器实例
        """

        broadcaster = RedisMessageBroadcaster()
        success = await broadcaster.initialize()
        
        # 即使初始化失败，也返回广播器实例
        # 后续操作会在各方法中检查初始化状态
        if not success:
            logger.warning("Redis 消息广播器初始化失败，但仍返回实例。后续操作将在本地执行。")
            
        return broadcaster
