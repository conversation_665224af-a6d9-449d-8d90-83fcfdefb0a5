from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class PageResultDTO:
    """分页结果DTO"""

    success: bool
    message: str
    data: List[Dict[str, Any]]
    total_count: int
    total_pages: int
    page_index: int
    batch_size: int

    @classmethod
    def from_api_response(
        cls, response: Dict[str, Any], page_index: int, batch_size: int
    ) -> "PageResultDTO":
        """
        从API响应创建DTO对象

        Args:
            response: API响应字典
            page_index: 页码
            batch_size: 每页记录数

        Returns:
            PageResultDTO对象
        """
        return cls(
            success=response.get("success", False),
            message=response.get("message", ""),
            data=response.get("data", []),
            total_count=response.get("total_count", 0),
            total_pages=response.get("total_pages", 0),
            page_index=page_index,
            batch_size=batch_size,
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典，保持与原有代码兼容

        Returns:
            字典表示
        """
        return {
            "success": self.success,
            "message": self.message,
            "data": self.data,
            "total_count": self.total_count,
            "total_pages": self.total_pages,
        }

    def is_empty(self) -> bool:
        """
        检查结果是否为空

        Returns:
            是否为空
        """
        return not self.success or len(self.data) == 0
