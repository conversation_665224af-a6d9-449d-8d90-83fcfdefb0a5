import asyncio
import aiohttp
import os
import random
from typing import Dict, Any, Optional, Callable, Awaitable, TypeVar

from lucas_common_components.logging import setup_logger

from src.infrastructure.dto.gugudata_dto import PageResultDTO

# 创建logger
logger = setup_logger(__name__)

# 定义一个泛型类型变量，用于表示异步函数的返回类型
T = TypeVar("T")


class GuguDataClient:
    """专业录取分数线数据RPC客户端"""

    def __init__(self):
        """初始化RPC客户端"""
        self.api_base_url = "https://api.gugudata.com/metadata/ceemajorline"
        self.api_key = os.getenv("GUGUDATA_APPKEY", "VRJWAYXT9AZCRUTYT6HX3HNU3H4SJU87")
        self.request_interval = 0.3  # 请求间隔时间（秒）
        self.retry_times = 3  # 重试次数
        self.retry_interval = 1  # 重试间隔（秒）

    async def fetch_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        从API获取数据

        Args:
            params: 请求参数字典

        Returns:
            API返回的响应数据
        """
        # 确保请求参数中包含API Key
        request_params = {"appkey": self.api_key, **params}

        retry_count = 0
        while retry_count < self.retry_times:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        self.api_base_url, params=request_params
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            # 检查API返回状态码
                            if result.get("DataStatus", {}).get("StatusCode") == 100:
                                return result
                            else:
                                error_code = result.get("DataStatus", {}).get(
                                    "StatusCode"
                                )
                                error_msg = result.get("DataStatus", {}).get(
                                    "StatusDescription"
                                )
                                logger.error(f"API返回错误: {error_code} - {error_msg}")

                                # 如果是请求频率限制，增加重试间隔
                                if error_code in [429, 502]:
                                    await asyncio.sleep(self.retry_interval * 2)
                                    retry_count += 1
                                    continue

                                return result
                        else:
                            logger.error(f"HTTP请求失败，状态码: {response.status}")
                            retry_count += 1
                            await asyncio.sleep(self.retry_interval)
            except Exception as e:
                logger.error(f"请求异常: {str(e)}")
                retry_count += 1
                await asyncio.sleep(self.retry_interval)

        logger.error("请求失败，已达到最大重试次数")
        return {
            "DataStatus": {
                "StatusCode": -1,
                "StatusDescription": "请求失败，已达到最大重试次数",
            }
        }

    async def retry_with_backoff(
        self,
        func: Callable[..., Awaitable[T]],
        *args,
        max_retries=3,
        base_delay=1,
        max_delay=60,
        **kwargs,
    ) -> T:
        """
        使用指数退避策略进行重试的通用工具方法

        Args:
            func: 要执行的异步函数
            *args: 传递给函数的位置参数
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            **kwargs: 传递给函数的关键字参数

        Returns:
            协程的执行结果

        Raises:
            Exception: 当重试全部失败时抛出
        """
        retry_count = 0
        last_exception = None

        while retry_count <= max_retries:
            try:
                # 调用传入的函数并等待结果
                return await func(*args, **kwargs)
            except Exception as e:
                retry_count += 1
                last_exception = e

                if retry_count > max_retries:
                    logger.error(f"达到最大重试次数({max_retries})，放弃重试")
                    break

                # 计算延迟时间，使用指数退避策略，加入随机扰动
                delay = min(
                    base_delay * (2 ** (retry_count - 1)) + random.uniform(0, 1),
                    max_delay,
                )
                logger.warning(
                    f"操作失败，将在{delay:.2f}秒后进行第{retry_count}次重试。错误: {str(e)}"
                )
                await asyncio.sleep(delay)

        # 重试失败后，抛出最后一个异常
        raise (
            last_exception
            if last_exception
            else Exception("重试失败，没有捕获到具体异常")
        )

    async def fetch_page_data(
        self,
        page_index: int = 1,
        batch_size: int = 50,
        enrollprovince: Optional[str] = None,
        schoolname: Optional[str] = None,
        majorname: Optional[str] = None,
        year: Optional[int] = None,
    ) -> PageResultDTO:
        """
        获取单页专业录取分数线数据

        Args:
            page_index: 页码
            batch_size: 每页记录数
            enrollprovince: 招生省份
            schoolname: 学校名称
            majorname: 专业名称
            year: 年份

        Returns:
            分页结果DTO对象
        """
        try:
            logger.info(
                f"获取页面数据，页码: {page_index}, 省份: {enrollprovince}, 年份: {year}, 批次大小: {batch_size}"
            )

            params = {"pagesize": batch_size, "pageindex": page_index}

            # 添加可选参数
            if enrollprovince:
                params["enrollprovince"] = enrollprovince
            if schoolname:
                params["schoolname"] = schoolname
            if majorname:
                params["majorname"] = majorname
            if year:
                params["year"] = year

            # 使用retry_with_backoff进行重试调用
            result = await self.retry_with_backoff(
                self.fetch_data, params, max_retries=3, base_delay=1, max_delay=30
            )

            # 处理结果
            if result.get("DataStatus", {}).get("StatusCode") != 100:
                error_msg = result.get("DataStatus", {}).get(
                    "StatusDescription", "请求失败"
                )
                logger.error(f"获取第{page_index}页数据失败: {error_msg}")

                # 创建失败的分页结果DTO
                return PageResultDTO(
                    success=False,
                    message=error_msg,
                    data=[],
                    total_count=0,
                    total_pages=0,
                    page_index=page_index,
                    batch_size=batch_size,
                )

            # 计算总页数
            total_count = result.get("DataStatus", {}).get("DataTotalCount", 0)
            total_pages = (
                (total_count + batch_size - 1) // batch_size if batch_size > 0 else 0
            )
            page_data = result.get("Data", [])

            logger.info(
                f"成功获取第{page_index}/{total_pages}页数据，本页{len(page_data)}条记录，总记录数{total_count}"
            )

            # TODO data 使用对象dto
            # 创建成功的分页结果DTO
            return PageResultDTO(
                success=True,
                message="获取数据成功",
                data=page_data,
                total_count=total_count,
                total_pages=total_pages,
                page_index=page_index,
                batch_size=batch_size,
            )
        except Exception as e:
            logger.exception(f"获取页面数据时发生异常: {str(e)}")

            # 创建异常情况下的分页结果DTO
            return PageResultDTO(
                success=False,
                message=f"获取页面数据时发生异常: {str(e)}",
                data=[],
                total_count=0,
                total_pages=0,
                page_index=page_index,
                batch_size=batch_size,
            )
