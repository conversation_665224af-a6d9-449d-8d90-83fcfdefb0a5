from typing import Dict

import httpx
from lucas_common_components.logging import setup_logger
from lucas_common_components.trace import trace_context

from src.config.config_model import AIChatAppConfig

logger = setup_logger(name=__name__, level="DEBUG")


class HeaderInjectionTransport(httpx.AsyncHTTPTransport):
    """自定义传输类，用于注入请求头"""

    def __init__(self, get_headers_func):
        super().__init__()
        self.get_headers_func = get_headers_func

    async def handle_async_request(self, request):
        # 在发送请求前注入headers
        request.headers.update(self.get_headers_func())
        return await super().handle_async_request(request)


def get_common_headers() -> Dict[str, str]:
    """获取通用请求头"""
    return {"rest-service-traceId": trace_context.get_trace_id()}


class ClientManager:
    """客户端管理器"""

    _instance = None
    _clients: Dict[str, httpx.AsyncClient] = {}
    _services: Dict[str, str] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            # 从环境变量获取当前环境，默认为 dev
            # 直接使用相对路径
            cls._services = AIChatAppConfig.get_instance().model_dump()
        return cls._instance

    def get_client(self, service_name: str) -> httpx.AsyncClient:
        """获取或创建客户端"""
        if service_name not in self._clients:
            rest_config = AIChatAppConfig.get_instance().rest
            base_url = getattr(rest_config, service_name)

            # 创建客户端，使用自定义传输类
            client = httpx.AsyncClient(
                base_url=base_url,
                transport=HeaderInjectionTransport(get_common_headers),
            )

            self._clients[service_name] = client
        return self._clients[service_name]

    async def close_all(self):
        """关闭所有客户端"""
        for client in self._clients.values():
            await client.aclose()
        self._clients.clear()
