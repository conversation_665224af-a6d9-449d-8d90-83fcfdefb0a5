import asyncio
from typing import Optional

from lucas_common_components.logging import setup_logger

from src.app.zhiyuan.models.model import (
    UserInfoDTO,
    GetProfileResponse,
    ConsumeCreditResponse,
    ConsumeCreditResultDTO,
)
from src.infrastructure.rpc.http_client import ClientManager

logger = setup_logger(__name__)


class AccountClient:
    def __init__(self):
        self.client = ClientManager().get_client("account")

    async def get_profile(self, uniq_user_id: str) -> Optional[UserInfoDTO]:
        """
        获取用户个人信息

        Args:
            uniq_user_id: str - 用户唯一标识

        Returns:
            Optional[UserInfoDTO] - 用户信息，如果请求失败则返回None

        Raises:
            Exception: 当所有重试都失败时抛出异常
        """
        max_retries = 3
        base_delay = 1.0  # 初始延迟1秒

        for attempt in range(max_retries):
            try:
                response = await self.client.post(
                    "/rest/account/getProfile",
                    timeout=2.0 + attempt,  # 随重试次数增加超时时间
                    json={"id": uniq_user_id},
                )

                # 打印原始响应数据以便调试
                logger.info(f"Raw response data: {response.json()}")

                result = GetProfileResponse.model_validate(response.json())
                if not result.success:
                    logger.warning(
                        f"获取用户信息失败 (业务逻辑错误) for user {uniq_user_id} on attempt {attempt + 1}"
                    )
                    raise ValueError("Business error reported by API")
                return result.data

            except Exception as e:
                current_delay = base_delay * (2**attempt)  # 指数退避

                if attempt == max_retries - 1:  # 最后一次重试
                    logger.error(
                        f"获取用户信息失败(重试{attempt + 1}/{max_retries}) for user {uniq_user_id}: {str(e)}",
                        exc_info=True,
                    )
                    raise e

                logger.warning(
                    f"获取用户信息失败(重试{attempt + 1}/{max_retries}) for user {uniq_user_id}, {current_delay}秒后重试: {str(e)}"
                )
                await asyncio.sleep(current_delay)
                return None
        return None

    async def consume_credit(
        self, user_id: str, consume_related_id: str
    ) -> Optional[ConsumeCreditResultDTO]:
        """
        消费用户积分

        Args:
            user_id: int - 用户ID
            consume_related_id: str - 报告类型，对应消耗的积分在配置中心

        Returns:
            Optional[ConsumeCreditResultDTO] - 消费结果，如果请求失败则返回None

        Raises:
            Exception: 当所有重试都失败时抛出异常
        """
        max_retries = 3
        base_delay = 1.0  # 初始延迟1秒

        for attempt in range(max_retries):
            try:
                response = await self.client.post(
                    "/rest/account/consumeCredit",
                    timeout=2.0 + attempt,  # 随重试次数增加超时时间
                    json={"userId": user_id, "reportId": consume_related_id},
                )

                # 打印原始响应数据以便调试
                logger.info(f"Raw response data: {response.json()}")

                result = ConsumeCreditResponse.model_validate(response.json())
                if not result.success:
                    logger.warning(
                        f"消费积分失败 (业务逻辑错误) for user {user_id}, related {consume_related_id} on attempt {attempt + 1}: {result.errorMessage}"
                    )
                    # 返回结果，让调用方根据状态处理
                    raise ValueError("Business error reported by API")
                return result.data

            except Exception as e:
                current_delay = base_delay * (2**attempt)  # 指数退避

                if attempt == max_retries - 1:  # 最后一次重试
                    logger.error(
                        f"消费积分失败(重试{attempt + 1}/{max_retries}) for user {user_id}, related {consume_related_id}: {str(e)}",
                        exc_info=True,
                    )
                    raise e

                logger.warning(
                    f"消费积分失败(重试{attempt + 1}/{max_retries}) for user {user_id}, related {consume_related_id}, {current_delay}秒后重试: {str(e)}"
                )
                await asyncio.sleep(current_delay)
        return None
