import os
from typing import List
from openai import OpenAI


class EmbeddingClient:
    _client = OpenAI(
        api_key=os.getenv("ALIBABA_API_KEY", "sk-df50aa96d0ec4d9a83b79570419fc9ba"),
        base_url=os.getenv(
            "ALIBABA_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"
        ),
    )
    _model = "text-embedding-v3"
    _dimensions = 768

    @staticmethod
    def get_embedding(text: str) -> List[float]:
        try:
            completion = EmbeddingClient._client.embeddings.create(
                model=EmbeddingClient._model,
                input=text,
                dimensions=EmbeddingClient._dimensions,
                encoding_format="float",
            )

            # Extract the embedding from the response
            embedding = completion.data[0].embedding
            return embedding

        except Exception as e:
            print(f"Error generating embedding: {str(e)}")
            raise

    @staticmethod
    def get_embedding_128(text: str) -> List[float]:
        """
        生成128维的向量嵌入，用于向量搜索服务

        Args:
            text: 输入文本

        Returns:
            List[float]: 128维的向量嵌入
        """
        try:
            completion = EmbeddingClient._client.embeddings.create(
                model=EmbeddingClient._model,
                input=text,
                dimensions=128,
                encoding_format="float",
            )

            # Extract the embedding from the response
            embedding = completion.data[0].embedding
            return embedding

        except Exception as e:
            print(f"Error generating 128-dimension embedding: {str(e)}")
            raise
