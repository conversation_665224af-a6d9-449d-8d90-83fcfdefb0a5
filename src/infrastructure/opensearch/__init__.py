"""
阿里云OpenSearch客户端模块

提供分层的OpenSearch客户端架构：
- basic_client: 底层协议客户端，处理HTTP请求、认证、重试等
- llm_client: 高层业务客户端，提供面向业务的搜索接口
- config: 共享配置类
"""

# 导出配置类
from .config import Config

# 导出底层客户端
from .basic_client import AliyunOpenSearchBasicClient

# 导出高层客户端
from .llm_client import AliyunOpenSearchLLMClient

__all__ = [
    # 配置
    'Config',
    # 底层客户端
    'AliyunOpenSearchBasicClient',
    # 高层客户端
    'AliyunOpenSearchLLMClient',
]
