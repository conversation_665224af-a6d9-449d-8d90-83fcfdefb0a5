# -*- coding: utf-8 -*-

from Tea.model import TeaModel


class Config(TeaModel):
    """
    阿里云 OpenSearch 配置类
    用于配置认证和连接相关参数
    """

    def __init__(
        self,
        endpoint: str = None,
        protocol: str = None,
        type: str = None,
        security_token: str = None,
        access_key_id: str = None,
        access_key_secret: str = None,
        user_agent: str = "",
    ):
        self.endpoint = endpoint
        self.protocol = protocol
        self.type = type
        self.security_token = security_token
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.user_agent = user_agent 