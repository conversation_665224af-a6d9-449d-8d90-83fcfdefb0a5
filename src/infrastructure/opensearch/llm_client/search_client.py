# -*- coding: utf-8 -*-

from typing import Dict, Any, AsyncGenerator

from lucas_common_components.logging import setup_logger

from ..config import Config
from ..basic_client import AliyunOpenSearchBasicClient
from .stream_processor import StreamProcessor

logger = setup_logger(__name__)


class AliyunOpenSearchLLMClient:
    """
    阿里云 OpenSearch LLM 版高层业务客户端
    
    职责：
    - 提供面向业务的搜索接口
    - 构建符合 OpenSearch LLM 版 API 规范的请求体
    - 解析和处理业务响应数据
    - 实现增量流式输出等业务特性
    """

    def __init__(self, config: Config):
        """
        初始化 LLM 客户端
        
        Args:
            config: 阿里云 OpenSearch 配置对象
        """
        self._low_level_client = AliyunOpenSearchBasicClient(config)
        self._stream_processor = StreamProcessor()

    def _prepare_llm_body(self, body: Dict, use_llm: bool) -> Dict:
        """
        准备包含LLM配置的请求体
        
        Args:
            body: 原始请求体
            use_llm: 是否使用大模型
            
        Returns:
            配置好的请求体
        """
        # 设置是否使用大模型的参数
        if "options" not in body:
            body["options"] = {}
        if "chat" not in body["options"]:
            body["options"]["chat"] = {}
        body["options"]["chat"]["disable"] = not use_llm
        return body

    def _prepare_stream_body(self, body: Dict, use_llm: bool) -> Dict:
        """
        准备包含流式配置的请求体
        
        Args:
            body: 原始请求体
            use_llm: 是否使用大模型
            
        Returns:
            配置好的请求体
        """
        body = self._prepare_llm_body(body, use_llm)
        body["options"]["chat"]["stream"] = True  # 开启流式
        return body

    async def searchDoc(
        self, app_name: str, body: Dict, query_params: dict = {}, use_llm: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        执行知识搜索
        
        Args:
            app_name: OpenSearch 应用名称
            body: 请求体数据
            query_params: 查询参数
            use_llm: 是否使用大模型
            
        Yields:
            搜索结果数据
        """
        try:
            # 准备请求体
            prepared_body = self._prepare_llm_body(body, use_llm)

            # 构建请求路径
            pathname = f"/v3/openapi/apps/{app_name}/actions/knowledge-search"

            # 调用底层客户端执行请求
            async for response in self._low_level_client.execute_request(
                method="POST",
                pathname=pathname,
                query=query_params,
                body=prepared_body,
                stream=False  # 知识搜索通常不是流式的
            ):
                # 处理搜索结果
                if isinstance(response, dict) and "body" in response:
                    yield response["body"].get("result", response["body"])
                else:
                    yield response
                    
        except Exception as e:
            logger.error(f"Error in searchDoc: {e}", exc_info=True)
            yield {"error": str(e)}

    async def multi_search(
        self, app_name: str, body: Dict, query_params: dict = {}, use_llm: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        执行多路搜索
        
        Args:
            app_name: OpenSearch 应用名称
            body: 请求体数据
            query_params: 查询参数
            use_llm: 是否使用大模型
            
        Yields:
            搜索结果数据
        """
        try:
            # 准备请求体
            prepared_body = self._prepare_llm_body(body, use_llm)

            # 构建请求路径
            pathname = f"/v3/openapi/apps/{app_name}/actions/multi-search"

            # 调用底层客户端执行请求
            async for response in self._low_level_client.execute_request(
                method="POST",
                pathname=pathname,
                query=query_params,
                body=prepared_body,
                stream=False
            ):
                # 处理搜索结果
                if isinstance(response, dict) and "body" in response:
                    yield response["body"].get("result", response["body"])
                else:
                    yield response
                    
        except Exception as e:
            logger.error(f"Error in multi_search: {e}", exc_info=True)
            yield {"error": str(e)}

    async def stream_multi_search(
        self,
        app_name: str,
        body: Dict,
        query_params: dict = {},
        use_llm: bool = True,
    ) -> AsyncGenerator[str, None]:
        """
        流式多路搜索，返回原始流式数据
        
        Args:
            app_name: OpenSearch 应用名称
            body: 请求体数据
            query_params: 查询参数
            use_llm: 是否使用大模型
            
        Yields:
            原始流式响应行
        """
        try:
            # 准备流式请求体
            prepared_body = self._prepare_stream_body(body, use_llm)

            # 构建请求路径
            pathname = f"/v3/openapi/apps/{app_name}/actions/multi-search"

            # 调用底层客户端执行流式请求
            response_generator = self._low_level_client.execute_request(
                method="POST",
                pathname=pathname,
                query=query_params,
                body=prepared_body,
                stream=True
            )

            # 使用流式处理器处理响应
            async for line in self._stream_processor.process_stream_response(response_generator):
                yield line
                    
        except Exception as e:
            logger.error(f"Error in stream_multi_search: {e}", exc_info=True)
            yield f"data:{{'error': '{str(e)}'}}"

    async def stream_incremental_search(
        self,
        app_name: str,
        body: Dict,
        query_params: dict = {},
        use_llm: bool = True,
    ) -> AsyncGenerator[str, None]:
        """
        流式增量搜索，实现打字机效果
        返回纯文本增量内容，直接用于前端显示
        
        Args:
            app_name: OpenSearch 应用名称
            body: 请求体数据
            query_params: 查询参数
            use_llm: 是否使用大模型
            
        Yields:
            增量文本内容
        """
        try:
            # 获取原始流式数据
            stream_generator = self.stream_multi_search(
                app_name, body, query_params, use_llm
            )

            # 使用流式处理器处理增量输出
            async for incremental_content in self._stream_processor.process_incremental_stream(stream_generator):
                yield incremental_content
                        
        except Exception as e:
            logger.error(f"Error in stream_incremental_search: {e}", exc_info=True)
            # 出错时不返回任何内容，保持输出干净 