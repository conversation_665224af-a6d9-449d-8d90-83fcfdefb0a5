# -*- coding: utf-8 -*-

import json
from typing import Dict, Any, AsyncGenerator

from lucas_common_components.logging import setup_logger

logger = setup_logger(__name__)


class StreamProcessor:
    """
    流式处理器
    
    职责：
    - 处理流式响应数据
    - 实现增量输出（打字机效果）
    - 解析和格式化流式JSON数据
    """

    @staticmethod
    async def process_stream_response(
        response_generator: AsyncGenerator[Dict[str, Any], None]
    ) -> AsyncGenerator[str, None]:
        """
        处理流式响应，转换为原始流式数据格式
        
        Args:
            response_generator: 底层客户端的响应生成器
            
        Yields:
            原始流式响应行
        """
        try:
            async for response in response_generator:
                # 处理新的响应格式
                if isinstance(response, dict) and "body" in response:
                    body = response["body"]
                    
                    # 如果body是字符串（流式响应），按行处理
                    if isinstance(body, str):
                        lines = body.strip().split('\n')
                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue
                            
                            # 如果不是以data:开头，添加前缀
                            if not line.startswith("data:"):
                                line = f"data:{line}"
                            
                            yield line
                    else:
                        # 如果body是字典（非流式响应），转换为data:格式
                        yield f"data:{json.dumps(body, ensure_ascii=False)}"
                else:
                    # 兼容旧格式
                    yield f"data:{json.dumps(response, ensure_ascii=False)}"
                    
        except StopAsyncIteration:
            # 正常的流式结束，不记录错误
            pass
        except Exception as e:
            # 只有真正的错误才记录日志
            if str(e) != "None" and "UnretryableException" not in str(type(e)):
                logger.error(f"Error in process_stream_response: {e}", exc_info=True)
                yield f"data:{json.dumps({'error': str(e)}, ensure_ascii=False)}"

    @staticmethod
    async def process_incremental_stream(
        stream_generator: AsyncGenerator[str, None]
    ) -> AsyncGenerator[str, None]:
        """
        处理增量流式输出，实现打字机效果
        
        Args:
            stream_generator: 原始流式数据生成器
            
        Yields:
            增量文本内容
        """
        last_answer = ""

        try:
            async for line in stream_generator:
                # 处理结束标记
                if line == "data:[done]":
                    yield line
                    continue

                # 解析JSON数据
                if line.startswith("data:"):
                    try:
                        json_str = line[5:]  # 去掉"data:"前缀
                        data = json.loads(json_str)
                        
                        # 检查是否有错误
                        if "error" in data:
                            # 只有非空错误才记录日志
                            if data['error'] and str(data['error']) != "None":
                                logger.error(f"Stream response error: {data['error']}")
                            continue
                        
                        # 提取当前答案
                        if (StreamProcessor._extract_answer_from_data(data)):
                            current_answer = StreamProcessor._extract_answer_from_data(data)

                            # 计算新增内容
                            if current_answer.startswith(last_answer):
                                incremental_content = current_answer[len(last_answer):]
                                if incremental_content:  # 只在有新内容时才返回
                                    yield incremental_content

                            # 更新上次答案
                            last_answer = current_answer
                            
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing stream response JSON: {e}")
                        # JSON解析错误时不返回任何内容，避免干扰输出
                    except KeyError as e:
                        logger.error(f"Missing expected field in stream response: {e}")
                        # 字段缺失时不返回任何内容
                    except Exception as e:
                        logger.error(f"Unexpected error in stream processing: {e}")
                        # 其他错误时不返回任何内容
                        
        except Exception as e:
            logger.error(f"Error in process_incremental_stream: {e}", exc_info=True)
            # 出错时不返回任何内容，保持输出干净

    @staticmethod
    def _extract_answer_from_data(data: Dict[str, Any]) -> str:
        """
        从响应数据中提取答案文本
        
        Args:
            data: 响应数据字典
            
        Returns:
            提取的答案文本，如果没有找到则返回空字符串
        """
        try:
            if ("result" in data and 
                "data" in data["result"] and 
                len(data["result"]["data"]) > 0 and
                "answer" in data["result"]["data"][0]):
                
                return data["result"]["data"][0]["answer"]
        except (KeyError, IndexError, TypeError):
            pass
        
        return "" 