# -*- coding: utf-8 -*-

from typing import Dict, Any
from Tea.request import TeaRequest
from Tea.core import <PERSON><PERSON><PERSON>
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_opensearch_util.opensearch_util import OpensearchUtil
from alibabacloud_credentials.client import Client as CredentialClient


class RequestBuilder:
    """
    请求构建器
    
    职责：
    - 构建HTTP请求头
    - 构建TeaRequest对象
    - 处理请求签名
    """

    def __init__(self, credential: CredentialClient, endpoint: str, protocol: str, user_agent: str):
        """
        初始化请求构建器
        
        Args:
            credential: 认证客户端
            endpoint: 服务端点
            protocol: 协议类型
            user_agent: 用户代理字符串
        """
        self._credential = credential
        self._endpoint = endpoint
        self._protocol = protocol
        self._user_agent = user_agent

    def build_request_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """
        构建请求头
        
        Args:
            headers: 额外的请求头
            
        Returns:
            完整的请求头字典
        """
        base_headers = {
            "user-agent": UtilClient.get_user_agent(self._user_agent),
            "Content-Type": "application/json",
            "Date": OpensearchUtil.get_date(),
            "host": UtilClient.default_string(
                self._endpoint, "opensearch-cn-hangzhou.aliyuncs.com"
            ),
            "X-Opensearch-Nonce": UtilClient.get_nonce(),
        }
        return TeaCore.merge(base_headers, headers)

    def build_tea_request(
        self,
        method: str,
        pathname: str,
        query: Dict[str, Any],
        headers: Dict[str, str],
        body: Any,
    ) -> TeaRequest:
        """
        构建 TeaRequest 对象
        
        Args:
            method: HTTP方法
            pathname: 请求路径
            query: 查询参数
            headers: 请求头
            body: 请求体
            
        Returns:
            构建好的 TeaRequest 对象
        """
        # 获取认证信息（使用新的API）
        credential = self._credential.get_credential()
        access_key_id = credential.access_key_id
        access_key_secret = credential.access_key_secret
        security_token = credential.security_token
        
        # 创建请求对象
        request = TeaRequest()
        request.protocol = UtilClient.default_string(self._protocol, "HTTP")
        request.method = method
        request.pathname = pathname
        request.headers = self.build_request_headers(headers)
        
        # 设置查询参数
        if not UtilClient.is_unset(query):
            request.query = UtilClient.stringify_map_value(query)
        
        # 设置请求体和Content-MD5
        if not UtilClient.is_unset(body):
            req_body = UtilClient.to_jsonstring(body)
            request.headers["Content-MD5"] = OpensearchUtil.get_content_md5(req_body)
            request.body = req_body
        
        # 设置安全令牌
        if not UtilClient.is_unset(security_token):
            request.headers["X-Opensearch-Security-Token"] = security_token
        
        # 生成签名
        request.headers["Authorization"] = OpensearchUtil.get_signature(
            request, access_key_id, access_key_secret
        )
        
        return request 