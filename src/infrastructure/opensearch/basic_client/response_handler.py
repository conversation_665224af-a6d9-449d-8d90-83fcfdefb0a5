# -*- coding: utf-8 -*-

from typing import Dict, Any, AsyncGenerator
from Tea.exceptions import TeaException
from alibabacloud_tea_util.client import Client as UtilClient


class ResponseHandler:
    """
    响应处理器
    
    职责：
    - 处理HTTP响应数据
    - 处理流式响应数据
    - 错误状态码检查和异常抛出
    """

    @staticmethod
    def handle_response(response) -> Dict[str, Any]:
        """
        处理响应数据
        
        Args:
            response: HTTP响应对象
            
        Returns:
            解析后的响应数据
            
        Raises:
            TeaException: 当响应状态码表示错误时
        """
        obj_str = UtilClient.read_as_string(response.body)
        
        # 检查错误状态码
        if UtilClient.is_4xx(response.status_code) or UtilClient.is_5xx(response.status_code):
            raise TeaException({
                "message": response.status_message,
                "data": obj_str,
                "code": response.status_code,
            })
        
        # 解析JSON响应
        obj = UtilClient.parse_json(obj_str)
        res = UtilClient.assert_as_map(obj)
        return {"body": res, "headers": response.headers}

    @staticmethod
    def handle_stream_response(response) -> Dict[str, Any]:
        """
        处理流式响应数据
        
        Args:
            response: HTTP响应对象
            
        Returns:
            解析后的响应数据
            
        Raises:
            TeaException: 当响应状态码表示错误时
        """
        obj_str = UtilClient.read_as_string(response.body)
        
        # 检查错误状态码
        if UtilClient.is_4xx(response.status_code) or UtilClient.is_5xx(response.status_code):
            raise TeaException({
                "message": response.status_message,
                "data": obj_str,
                "code": response.status_code,
            })
        
        # 直接返回原始响应体，让上层处理流式解析
        # 这样可以保持完整的流式数据
        return {"body": obj_str, "headers": response.headers} 