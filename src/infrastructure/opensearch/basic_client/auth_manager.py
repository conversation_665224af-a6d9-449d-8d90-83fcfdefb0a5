# -*- coding: utf-8 -*-

from Tea.exceptions import TeaException
from alibabacloud_credentials import models as credential_models
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_util.client import Client as UtilClient

from ..config import Config


class AuthManager:
    """
    认证管理器
    
    职责：
    - 管理认证凭证
    - 提供认证信息访问接口
    """

    def __init__(self, config: Config):
        """
        初始化认证管理器
        
        Args:
            config: 配置对象
        """
        if UtilClient.is_unset(config):
            raise TeaException(
                {"name": "ParameterMissing", "message": "'config' can not be unset"}
            )
        
        # 设置默认认证类型
        if UtilClient.empty(config.type):
            config.type = "access_key"
        
        # 初始化认证客户端
        credential_config = credential_models.Config(
            access_key_id=config.access_key_id,
            type=config.type,
            access_key_secret=config.access_key_secret,
            security_token=config.security_token,
        )
        self._credential = CredentialClient(credential_config)

    @property
    def credential(self) -> CredentialClient:
        """获取认证客户端实例"""
        return self._credential

    def get_access_key_id(self) -> str:
        """获取访问密钥ID"""
        return self._credential.get_access_key_id()

    def get_access_key_secret(self) -> str:
        """获取访问密钥Secret"""
        return self._credential.get_access_key_secret()

    def get_security_token(self) -> str:
        """获取安全令牌"""
        return self._credential.get_security_token() 