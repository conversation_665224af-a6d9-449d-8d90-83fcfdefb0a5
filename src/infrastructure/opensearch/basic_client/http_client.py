# -*- coding: utf-8 -*-

import time
from typing import Dict, Any, AsyncGenerator, Optional
from Tea.core import TeaCore
from Tea.exceptions import TeaException, UnretryableException
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from ..config import Config
from .auth_manager import AuthManager
from .request_builder import RequestBuilder
from .response_handler import ResponseHandler
from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")

class AliyunOpenSearchBasicClient:
    """
    阿里云 OpenSearch 底层协议客户端
    
    职责：
    - 整合认证、请求构建、响应处理等组件
    - 执行HTTP请求（支持同步和异步）
    - 处理重试和错误恢复
    - 支持流式响应
    """

    def __init__(self, config: Config):
        """
        初始化客户端
        
        Args:
            config: 配置对象，包含认证信息和连接参数
        """
        if UtilClient.is_unset(config):
            raise TeaException(
                {"name": "ParameterMissing", "message": "'config' can not be unset"}
            )
        
        # 保存配置信息
        self._endpoint = config.endpoint
        self._protocol = config.protocol
        self._user_agent = config.user_agent
        
        # 初始化各个组件
        self._auth_manager = AuthManager(config)
        self._request_builder = RequestBuilder(
            self._auth_manager.credential,
            config.endpoint,
            config.protocol,
            config.user_agent
        )
        self._response_handler = ResponseHandler()
        
        # 默认运行时配置
        self._default_runtime = util_models.RuntimeOptions(
            connect_timeout=10000,
            read_timeout=90000,
            autoretry=True,
            ignore_ssl=False,
            max_idle_conns=50,
            max_attempts=3,
        )

    def _build_runtime_config(self, runtime: util_models.RuntimeOptions) -> Dict[str, Any]:
        """
        构建运行时配置字典
        
        Args:
            runtime: 运行时选项
            
        Returns:
            运行时配置字典
        """
        runtime.validate()
        return {
            "timeouted": "retry",
            "readTimeout": runtime.read_timeout,
            "connectTimeout": runtime.connect_timeout,
            "httpProxy": runtime.http_proxy,
            "httpsProxy": runtime.https_proxy,
            "noProxy": runtime.no_proxy,
            "maxIdleConns": runtime.max_idle_conns,
            "retry": {
                "retryable": runtime.autoretry,
                "maxAttempts": UtilClient.default_number(runtime.max_attempts, 3),
            },
            "backoff": {
                "policy": UtilClient.default_string(runtime.backoff_policy, "no"),
                "period": UtilClient.default_number(runtime.backoff_period, 1),
            },
            "ignoreSSL": runtime.ignore_ssl,
        }

    async def execute_request(
        self,
        method: str,
        pathname: str,
        query: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        body: Optional[Any] = None,
        stream: bool = False,
        runtime: Optional[util_models.RuntimeOptions] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        执行HTTP请求（统一的异步接口）
        
        Args:
            method: HTTP方法 (GET, POST, etc.)
            pathname: 请求路径
            query: 查询参数字典
            headers: 额外的请求头
            body: 请求体数据
            stream: 是否为流式请求
            runtime: 运行时配置选项
            
        Yields:
            响应数据字典
            
        Raises:
            TeaException: 请求失败时
            UnretryableException: 重试耗尽时
        """
        # 使用默认参数
        if query is None:
            query = {}
        if headers is None:
            headers = {}
        if runtime is None:
            runtime = self._default_runtime
        
        # 构建运行时配置
        _runtime = self._build_runtime_config(runtime)
        
        # 重试逻辑
        _last_request = None
        _last_exception = None
        _now = time.time()
        _retry_times = 0
        
        while TeaCore.allow_retry(_runtime.get("retry"), _retry_times, _now):
            if _retry_times > 0:
                _backoff_time = TeaCore.get_backoff_time(
                    _runtime.get("backoff"), _retry_times
                )
                if _backoff_time > 0:
                    TeaCore.sleep(_backoff_time)
            
            _retry_times += 1
            
            try:
                # 构建请求
                request = self._request_builder.build_tea_request(method, pathname, query, headers, body)
                _last_request = request
                
                # 执行请求
                if stream:
                    # 流式请求处理
                    response = await TeaCore.async_do_action(request, _runtime)
                    try:
                        result = self._response_handler.handle_stream_response(response)
                        yield result
                    except TeaException as e:
                        logger.error(f"Stream response handling error: {e}", exc_info=True)
                        if TeaCore.is_retryable(e):
                            _last_exception = e
                            continue
                        raise e
                else:
                    # 常规请求处理
                    response = await TeaCore.async_do_action(request, _runtime)
                    result = self._response_handler.handle_response(response)
                    yield result
                    return  # 非流式请求只返回一次结果
                    
            except TeaException as e:
                if TeaCore.is_retryable(e):
                    _last_exception = e
                    continue
                raise e
        
        # 重试耗尽，抛出异常
        raise UnretryableException(_last_request, _last_exception)

    async def execute_sync_request(
        self,
        method: str,
        pathname: str,
        query: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        body: Optional[Any] = None,
        runtime: Optional[util_models.RuntimeOptions] = None,
    ) -> Dict[str, Any]:
        """
        执行同步HTTP请求（兼容性接口）
        
        Args:
            method: HTTP方法
            pathname: 请求路径
            query: 查询参数字典
            headers: 额外的请求头
            body: 请求体数据
            runtime: 运行时配置选项
            
        Returns:
            响应数据字典
            
        Raises:
            TeaException: 请求失败时
            UnretryableException: 重试耗尽时
        """
        # 使用默认参数
        if query is None:
            query = {}
        if headers is None:
            headers = {}
        if runtime is None:
            runtime = self._default_runtime
        
        # 构建运行时配置
        _runtime = self._build_runtime_config(runtime)
        
        # 重试逻辑
        _last_request = None
        _last_exception = None
        _now = time.time()
        _retry_times = 0
        
        while TeaCore.allow_retry(_runtime.get("retry"), _retry_times, _now):
            if _retry_times > 0:
                _backoff_time = TeaCore.get_backoff_time(
                    _runtime.get("backoff"), _retry_times
                )
                if _backoff_time > 0:
                    TeaCore.sleep(_backoff_time)
            
            _retry_times += 1
            
            try:
                # 构建请求
                request = self._request_builder.build_tea_request(method, pathname, query, headers, body)
                _last_request = request
                
                # 执行同步请求
                response = await TeaCore.async_do_action(request, _runtime)
                return self._response_handler.handle_response(response)
                
            except TeaException as e:
                if TeaCore.is_retryable(e):
                    _last_exception = e
                    continue
                raise e
        
        # 重试耗尽，抛出异常
        raise UnretryableException(_last_request, _last_exception) 