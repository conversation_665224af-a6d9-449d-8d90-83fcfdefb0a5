from typing import List, Optional

from src.infrastructure.db.models.ai.AIConversation import AIConversation


class AIConversationRepository:
    @staticmethod
    async def create(conversation_data: dict) -> AIConversation:
        return await AIConversation.create(**conversation_data)

    @staticmethod
    async def update(
        conversation_id: int, update_data: dict
    ) -> Optional[AIConversation]:
        await AIConversation.filter(id=conversation_id).update(**update_data)
        return await AIConversationRepository.get_by_id(conversation_id)

    @staticmethod
    async def delete(conversation_id: int) -> int:
        return await AIConversation.filter(id=conversation_id).delete()

    @staticmethod
    async def get_all() -> List[AIConversation]:
        return await AIConversation.all()

    @staticmethod
    async def get_by_id(conversation_id: int) -> Optional[AIConversation]:
        return await AIConversation.get_or_none(id=conversation_id)

    @staticmethod
    async def get_by_title(title: str) -> List[AIConversation]:
        return await AIConversation.filter(title=title).all()

    @staticmethod
    async def get_by_user_id(uni_user_id: int, sort_by: str = "created_at") -> List[AIConversation]:
        """
        根据用户ID获取会话列表
        Args:
            uni_user_id: 用户ID
            sort_by: 排序字段，可选值为 "created_at" 或 "updated_at"，默认为 "created_at"
        """
        return (
            await AIConversation.filter(uni_user_id=uni_user_id)
            .order_by(f"-{sort_by}")
            .all()
        )
