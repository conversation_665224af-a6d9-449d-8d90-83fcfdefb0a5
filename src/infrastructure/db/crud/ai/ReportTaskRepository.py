"""
报告任务Repository
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from src.infrastructure.db.models.ai.ReportTask import ReportTask, ReportTaskStatus


class ReportTaskRepository:
    """报告任务数据访问层"""

    @staticmethod
    async def create(task_data: Dict[str, Any]) -> ReportTask:
        """创建报告任务"""
        task = await ReportTask.create(**task_data)
        return task

    @staticmethod
    async def get_by_task_id(task_id: str) -> Optional[ReportTask]:
        """根据任务ID获取任务"""
        return await ReportTask.filter(task_id=task_id).first()

    @staticmethod
    async def get_by_sae_job_task_id(sae_job_task_id: str) -> Optional[ReportTask]:
        """根据SAE JOB任务ID获取任务（从extra_data中查找）"""
        # 使用JSON查询在extra_data中查找sae_job_task_id
        tasks = await ReportTask.filter(
            extra_data__contains={"sae_job_task_id": sae_job_task_id}
        ).all()
        return tasks[0] if tasks else None

    @staticmethod
    async def get_by_id(id: int) -> Optional[ReportTask]:
        """根据主键ID获取任务"""
        return await ReportTask.filter(id=id).first()

    @staticmethod
    async def get_by_conversation_id(
        conversation_id: int, status: Optional[ReportTaskStatus] = None
    ) -> List[ReportTask]:
        """根据会话ID获取任务列表"""
        query = ReportTask.filter(conversation_id=conversation_id)
        if status:
            query = query.filter(status=status)
        return await query.order_by("-created_at").all()

    @staticmethod
    async def get_completed_tasks_by_conversation(
        conversation_id: int,
    ) -> List[ReportTask]:
        """获取会话中已完成的任务"""
        return await ReportTaskRepository.get_by_conversation_id(
            conversation_id, status=ReportTaskStatus.COMPLETED
        )

    @staticmethod
    async def get_processing_tasks_by_conversation(
        conversation_id: int,
    ) -> List[ReportTask]:
        """获取会话中正在处理的任务"""
        return await ReportTaskRepository.get_by_conversation_id(
            conversation_id, status=ReportTaskStatus.PROCESSING
        )

    @staticmethod
    async def update_status(task_id: str, status: ReportTaskStatus, **kwargs) -> bool:
        """更新任务状态"""
        update_data = {"status": status, "updated_at": datetime.now()}

        # 根据状态设置时间字段
        if status in [
            ReportTaskStatus.COMPLETED,
            ReportTaskStatus.FAILED,
            ReportTaskStatus.CANCELLED,
        ]:
            if "completed_at" not in kwargs:
                update_data["completed_at"] = datetime.now()

        # 添加其他更新字段
        update_data.update(kwargs)

        result = await ReportTask.filter(task_id=task_id).update(**update_data)
        return result > 0

    @staticmethod
    async def update_progress(task_id: str, progress: int) -> bool:
        """更新任务进度"""
        result = await ReportTask.filter(task_id=task_id).update(
            progress=progress, updated_at=datetime.now()
        )
        return result > 0

    @staticmethod
    async def update_sae_job_task_id(task_id: str, sae_job_task_id: str) -> bool:
        """更新SAE JOB任务ID（存储在extra_data中）"""
        # 获取当前任务
        task = await ReportTask.filter(task_id=task_id).first()
        if not task:
            return False

        # 更新extra_data中的sae_job_task_id
        extra_data = task.extra_data or {}
        extra_data["sae_job_task_id"] = sae_job_task_id

        result = await ReportTask.filter(task_id=task_id).update(
            extra_data=extra_data, updated_at=datetime.now()
        )
        return result > 0

    @staticmethod
    async def update_result(task_id: str, result: Dict[str, Any]) -> bool:
        """更新任务结果"""
        update_result = await ReportTask.filter(task_id=task_id).update(
            result=result,
            status=ReportTaskStatus.COMPLETED,
            progress=100,
            completed_at=datetime.now(),
            updated_at=datetime.now(),
        )

        return update_result > 0

    @staticmethod
    async def mark_failed(task_id: str, error_message: str) -> bool:
        """标记任务失败"""
        return await ReportTaskRepository.update_status(
            task_id=task_id, status=ReportTaskStatus.FAILED, error_message=error_message
        )

    @staticmethod
    async def cancel_task(task_id: str) -> bool:
        """取消任务"""
        return await ReportTaskRepository.update_status(
            task_id=task_id, status=ReportTaskStatus.CANCELLED
        )

    @staticmethod
    async def get_latest_task_by_conversation(
        conversation_id: int,
    ) -> Optional[ReportTask]:
        """获取会话中最新的任务"""
        return (
            await ReportTask.filter(conversation_id=conversation_id)
            .order_by("-created_at")
            .first()
        )

    @staticmethod
    async def get_tasks_by_user(user_id: int, limit: int = 50) -> List[ReportTask]:
        """获取用户的任务列表"""
        return (
            await ReportTask.filter(user_id=user_id)
            .order_by("-created_at")
            .limit(limit)
            .all()
        )

    @staticmethod
    async def delete_task(task_id: str) -> bool:
        """删除任务（物理删除）"""
        result = await ReportTask.filter(task_id=task_id).delete()
        return result > 0

    @staticmethod
    async def get_task_statistics(conversation_id: int) -> Dict[str, int]:
        """获取任务统计信息"""
        tasks = await ReportTask.filter(conversation_id=conversation_id).all()
        stats = {
            "total": len(tasks),
            "created": 0,
            "processing": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0,
        }

        for task in tasks:
            stats[task.status.value] += 1

        return stats
