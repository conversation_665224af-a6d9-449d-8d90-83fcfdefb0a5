import logging

from lucas_common_components.logging import setup_logger

from src.infrastructure.db.models.ai.ExamScoreContextualData import (
    ExamScoreContextualData,
)

logger = setup_logger(__name__)


class ExamScoreContextualDataRepository:
    """高考一分一段表数据仓库类，负责等效分计算相关查询"""

    @staticmethod
    async def get_total_candidates(
        year: int, province_name: str, subject_selection: str
    ) -> int:
        """
        获取特定年份、省份、科目组合的总考生人数

        Args:
            year: 年份
            province_name: 省份名称
            subject_selection: 科目组合

        Returns:
            总考生人数
        """
        logger = logging.getLogger(__name__)
        logger.info(
            f"查询总考生数: 年份={year}, 省份={province_name}, 科目组合={subject_selection}"
        )

        try:
            result = (
                await ExamScoreContextualData.filter(
                    query_year=year,
                    query_province_name=province_name,
                    query_subject_selection=subject_selection,
                )
                .order_by("-total_candidates")
                .first()
            )

            total = result.total_candidates if result else 0
            logger.info(f"查询结果: 总考生数={total}")
            return total
        except Exception as e:
            logger.error(f"查询总考生数失败: {str(e)}", exc_info=True)
            # 抛出异常以便上层处理
            raise

    @staticmethod
    async def get_score_by_rank(
        year: int, province_name: str, rank: int, subject_selection: str
    ) -> int:
        """
        根据位次获取分数

        Args:
            year: 年份
            province_name: 省份名称
            rank: 位次
            subject_selection: 科目组合

        Returns:
            对应位次的分数，如果未找到返回0
        """
        logger.info(
            f"根据位次查询分数: 年份={year}, 省份={province_name}, 位次={rank}, 科目组合={subject_selection}"
        )

        try:
            # 获取所有可能的记录，按分数降序排列
            all_records = (
                await ExamScoreContextualData.filter(
                    query_year=year,
                    query_province_name=province_name,
                    query_subject_selection=subject_selection,
                )
                .order_by("-examination_score")
                .all()
            )

            logger.debug(f"查询到 {len(all_records)} 条记录")

            # 遍历所有记录，查找位次在范围内的记录
            for record in all_records:
                # 检查ranking_range是否有值
                if record.ranking_range:
                    try:
                        # 尝试解析范围，可能的格式如："1000-1500"或"1000"
                        range_parts = record.ranking_range.split("-")

                        # 如果是范围形式
                        if len(range_parts) == 2:
                            min_rank = int(range_parts[0].strip())
                            max_rank = int(range_parts[1].strip())
                            if min_rank <= rank <= max_rank:
                                logger.info(
                                    f"找到匹配的ranking_range范围: {min_rank}-{max_rank}, 等效分数={record.examination_score}"
                                )
                                return int(record.examination_score)
                        # 如果是单一值形式
                        elif (
                            len(range_parts) == 1
                            and int(range_parts[0].strip()) == rank
                        ):
                            logger.info(
                                f"找到精确匹配的ranking_range: {rank}, 等效分数={record.examination_score}"
                            )
                            return int(record.examination_score)
                    except (ValueError, AttributeError) as e:
                        # 解析失败，跳过这条记录
                        logger.warning(
                            f"解析ranking_range失败: {record.ranking_range}, 错误: {str(e)}"
                        )
                        continue

                # 检查total_candidates是否有值，如果大于等于rank，则说明位次在前面
                if record.total_candidates and int(record.total_candidates) >= rank:
                    logger.info(
                        f"根据total_candidates({record.total_candidates})确定位次{rank}对应分数: {record.examination_score}"
                    )
                    return int(record.examination_score)

            # 如果仍未找到，返回默认值
            logger.warning(f"未找到位次{rank}对应的分数记录，返回默认值0")
            return 0
        except Exception as e:
            logger.error(f"根据位次查询分数失败: {str(e)}", exc_info=True)
            # 返回默认值而非抛出异常，以确保计算可以继续
            return 0
