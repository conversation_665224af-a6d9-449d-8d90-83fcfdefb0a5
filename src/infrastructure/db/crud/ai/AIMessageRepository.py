from typing import List, Optional

from src.infrastructure.db.models.ai.AIMessage import AIMessage


class AIMessageRepository:
    @staticmethod
    async def create(message_data: dict) -> AIMessage:
        return await AIMessage.create(**message_data)

    @staticmethod
    async def bulk_create(messages_data: List[dict]) -> List[AIMessage]:
        return await AIMessage.bulk_create(
            [AIMessage(**data) for data in messages_data]
        )

    @staticmethod
    async def update(message_id: int, update_data: dict) -> Optional[AIMessage]:
        await AIMessage.filter(id=message_id).update(**update_data)
        return await AIMessageRepository.get_by_id(message_id)

    @staticmethod
    async def delete(message_id: int) -> int:
        return await AIMessage.filter(id=message_id).delete()

    @staticmethod
    async def get_all() -> List[AIMessage]:
        return await AIMessage.all()

    @staticmethod
    async def get_by_id(message_id: int) -> Optional[AIMessage]:
        return await AIMessage.get_or_none(id=message_id)

    @staticmethod
    async def get_by_conversation_id(conversation_id: int) -> List[AIMessage]:
        return (
            await AIMessage.filter(conversation_id=conversation_id)
            # 消息创建时间不对，临时改为id排序
            .order_by("id")
            .all()
        )

    @staticmethod
    async def get_by_conversation_id_limit10(conversation_id: int) -> List[AIMessage]:
        return (
            await AIMessage.filter(conversation_id=conversation_id)
            .order_by("-created_at")
            .limit(10)
            .all()
        )

    @staticmethod
    async def get_by_role(role: str) -> List[AIMessage]:
        return await AIMessage.filter(role=role).all()
