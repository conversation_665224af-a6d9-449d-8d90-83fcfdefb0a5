from typing import List, Optional
from tortoise.expressions import Q

from lucas_common_components.logging import setup_logger
from src.infrastructure.db.models.ai.MajorInfo import MajorInfo

logger = setup_logger(__name__)


class MajorInfoRepository:
    @staticmethod
    async def create(major_data: dict) -> MajorInfo:
        return await MajorInfo.create(**major_data)

    @staticmethod
    async def update(major_id: int, update_data: dict) -> Optional[MajorInfo]:
        await MajorInfo.filter(id=major_id).update(**update_data)
        return await MajorInfoRepository.get_by_id(major_id)

    @staticmethod
    async def delete(major_id: int) -> int:
        return await MajorInfo.filter(id=major_id).delete()

    @staticmethod
    async def get_all() -> List[MajorInfo]:
        return await MajorInfo.all()

    @staticmethod
    async def get_by_id(major_id: int) -> Optional[MajorInfo]:
        return await MajorInfo.get_or_none(id=major_id)

    @staticmethod
    async def get_by_name(major_name: str) -> List[MajorInfo]:
        return await MajorInfo.filter(major_name=major_name).all()

    @staticmethod
    async def get_by_name_fuzzy(major_name: str) -> List[MajorInfo]:
        """
        按专业名称进行模糊查询
        支持批量查询，多个专业名称用"||"分隔

        :param major_name: 专业名称关键词或多个关键词（用||分隔）
        :return: 匹配的专业信息列表
        """
        try:
            # 检查是否为批量查询
            if "||" in major_name:
                # 批量查询模式
                major_names = major_name.split("||")
                query = MajorInfo.filter(
                    Q(
                        *[
                            Q(major_name__icontains=name.strip())
                            for name in major_names
                        ],
                        join_type="OR",
                    )
                )
            else:
                # 单一查询模式
                query = MajorInfo.filter(major_name__icontains=major_name)

            # 限制返回数量，避免过多结果
            query = query.limit(50)

            result = await query.all()
            return result
        except Exception as e:
            logger.error(f"模糊查询专业信息失败: {str(e)}")
            return []

    @staticmethod
    async def get_by_category(category: str) -> List[MajorInfo]:
        return await MajorInfo.filter(disciplinary_category=category).all()

    @staticmethod
    async def get_limited_majors(limit: int = 50, offset: int = 0) -> List[MajorInfo]:
        """
        获取限制数量的专业信息，用于性能优化
        :param limit: 返回记录数量限制
        :param offset: 分页偏移量
        :return: 专业信息列表
        """
        return await MajorInfo.all().offset(offset).limit(limit)
