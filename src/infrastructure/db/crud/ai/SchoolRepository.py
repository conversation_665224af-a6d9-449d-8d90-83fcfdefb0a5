# school_crud.py (或你选择的文件名)

from typing import List, Optional, Tuple, Dict, Any

# 确保从正确的位置导入你的模型
from src.infrastructure.db.models.ai.school_models import School


class SchoolRepository:
    @staticmethod
    async def create_school(school_data: Dict[str, Any]) -> School:
        """创建一个新的学校记录"""
        return await School.create(**school_data)

    @staticmethod
    async def get_school_by_id(data_id: str) -> Optional[School]:
        """根据data_id获取一个学校记录"""
        return await School.get_or_none(data_id=data_id)

    @staticmethod
    async def get_school_by_uuid(school_uuid: str) -> Optional[School]:
        """根据学校UUID获取一个学校记录"""
        return await School.get_or_none(school_uuid=school_uuid)

    @staticmethod
    async def get_school_by_name(college_name: str) -> Optional[School]:
        """根据学校名称获取一个学校记录"""
        return await School.get_or_none(college_name=college_name)

    @staticmethod
    async def update_school(
        data_id: str, update_data: Dict[str, Any]
    ) -> Optional[School]:
        """根据data_id更新一个学校记录"""
        await School.filter(data_id=data_id).update(**update_data)
        return await SchoolRepository.get_school_by_id(data_id)

    @staticmethod
    async def upsert_school(
        match_keys: Dict[str, Any], defaults: Dict[str, Any]
    ) -> Tuple[School, bool]:
        """
        更新或创建学校记录 (Upsert)
        match_keys: 用于查找现有记录的键值对，例如 {"college_name": "北京大学"} 或 {"data_id": "some_id"}
        defaults: 如果记录不存在则用于创建，如果记录存在则用于更新这些字段的值
        返回: (School对象, 是否创建了新记录的布尔值)
        """
        school_obj, created = await School.update_or_create(
            **match_keys, defaults=defaults
        )
        return school_obj, created

    @staticmethod
    async def delete_school(data_id: str) -> int:
        """根据data_id删除一个学校记录，返回删除的行数"""
        return await School.filter(data_id=data_id).delete()

    @staticmethod
    async def get_all_schools(
        limit: Optional[int] = None, offset: int = 0
    ) -> List[School]:
        """获取所有学校记录，支持分页"""
        query = School.filter(is_deleted=False)
        if limit is not None:
            query = query.limit(limit).offset(offset)
        return await query

    @staticmethod
    async def find_schools(
        province: Optional[str] = None,
        city: Optional[str] = None,
        college_type: Optional[str] = None,
        college_category: Optional[str] = None,
        is_985: Optional[bool] = None,
        is_211: Optional[bool] = None,
        is_dual_class: Optional[bool] = None,
        college_tags: Optional[List[str]] = None,
        limit: Optional[int] = 10,
        offset: int = 0,
        order_by: Optional[List[str]] = None,
    ) -> List[School]:
        """根据多个条件筛选学校列表"""
        filters = {"is_deleted": False}
        if province:
            filters["province__icontains"] = province
        if city:
            filters["city__icontains"] = city
        if college_type:
            filters["college_type"] = college_type
        if college_category:
            filters["college_category"] = college_category
        if is_985 is not None:
            filters["is_985"] = is_985
        if is_211 is not None:
            filters["is_211"] = is_211
        if is_dual_class is not None:
            filters["is_dual_class"] = is_dual_class
        if college_tags:
            filters["college_tags__contains"] = college_tags

        query = School.filter(**filters)
        if order_by:
            query = query.order_by(*order_by)
        if limit is not None:
            query = query.limit(limit)
        query = query.offset(offset)

        return await query

    @staticmethod
    async def count_schools(filters: Optional[Dict[str, Any]] = None) -> int:
        """统计符合条件的学校数量"""
        if filters:
            filters["is_deleted"] = False
            return await School.filter(**filters).count()
        return await School.filter(is_deleted=False).count()

    @staticmethod
    async def search_schools_by_major(major_name: str) -> List[School]:
        """根据专业名称搜索学校"""
        return await School.filter(
            major_list__contains=[{"name": major_name}], is_deleted=False
        ).all()

    @staticmethod
    async def search_schools_by_branch(branch_name: str) -> List[School]:
        """根据分校区名称搜索学校"""
        return await School.filter(
            branch_list__contains=[{"name": branch_name}], is_deleted=False
        ).all()
