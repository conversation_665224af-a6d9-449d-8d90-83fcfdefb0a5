from typing import Dict, List, Optional

from lucas_common_components.logging import setup_logger
from tortoise.exceptions import IntegrityError

from src.infrastructure.db.models.ai.EquivalentScorePrediction import (
    EquivalentScorePrediction,
)

logger = setup_logger(__name__)


class EquivalentScorePredictionRepository:
    """高考等效分预测结果数据仓库类，负责数据的CRUD操作"""

    @staticmethod
    async def save(
        school_name: str,
        major_name: str,
        predict_year: int,
        province_scores_data: List[Dict],
    ) -> EquivalentScorePrediction:
        """
        保存等效分预测结果，如果存在则更新

        Args:
            school_name: 学校名称
            major_name: 专业名称
            predict_year: 预测年份
            province_scores_data: 省份-科目组合-分数的嵌套结构
                [
                    {
                        "province": "北京",
                        "scores": [
                            {"subject_selection": "文科", "score": 600},
                            {"subject_selection": "理科", "score": 620}
                        ]
                    },
                    ...
                ]

        Returns:
            保存的等效分预测结果对象
        """

        logger.info(f"保存等效分预测数据: {school_name}-{major_name}-{predict_year}")

        try:
            # 尝试找到现有记录
            prediction = await EquivalentScorePrediction.filter(
                school_name=school_name,
                major_name=major_name,
                predict_year=predict_year,
            ).first()

            if prediction:
                # 如果找到记录则更新
                logger.info(f"已找到现有记录，进行更新: ID={prediction.id}")
                prediction.province_scores = province_scores_data
                await prediction.save()
                logger.info(f"成功更新等效分数据: {school_name}-{major_name}")
            else:
                # 如果不存在则创建新记录
                logger.info("未找到现有记录，创建新记录")
                prediction = await EquivalentScorePrediction.create(
                    school_name=school_name,
                    major_name=major_name,
                    predict_year=predict_year,
                    province_scores=province_scores_data,
                )
                logger.info(f"成功创建等效分数据: ID={prediction.id}")

            return prediction

        except IntegrityError as e:
            # 处理唯一性约束冲突
            logger.error(f"保存等效分预测时发生唯一性约束冲突: {str(e)}")
            raise
        except Exception as e:
            # 处理其他异常
            logger.error(f"保存等效分预测时发生异常: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_by_school_major(
        school_name: str, major_name: str, predict_year: int
    ) -> Optional[Dict]:
        """
        获取学校专业的等效分预测结果

        Args:
            school_name: 学校名称
            major_name: 专业名称
            predict_year: 预测年份

        Returns:
            Dict: 预测结果，如果不存在则返回None
        """

        logger.info(f"查询等效分预测数据: {school_name}-{major_name}-{predict_year}")

        prediction = await EquivalentScorePrediction.filter(
            school_name=school_name, major_name=major_name, predict_year=predict_year
        ).first()

        if prediction:
            logger.info(f"找到等效分预测数据: ID={prediction.id}")
            return {
                "school_name": prediction.school_name,
                "major_name": prediction.major_name,
                "predict_year": prediction.predict_year,
                "province_scores": prediction.province_scores,
            }

        logger.warning("未找到等效分预测数据")
        return None
