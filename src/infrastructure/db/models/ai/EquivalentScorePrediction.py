from tortoise import fields
from tortoise.models import Model


class EquivalentScorePrediction(Model):
    """
    高考专业等效分预测表数据模型，对应数据库表：equivalent_score_prediction
    """

    id = fields.IntField(pk=True, description="自增主键")
    school_name = fields.CharField(max_length=255, index=True, description="学校名称")
    major_name = fields.CharField(max_length=512, index=True, description="专业名称")
    predict_year = fields.IntField(index=True, description="预测年份")
    province_scores = fields.JSONField(description="JSONB格式存储省份与等效分映射")

    # 元数据及管理字段
    created_at = fields.DatetimeField(
        auto_now_add=True, use_tz=True, description="创建时间"
    )
    updated_at = fields.DatetimeField(
        auto_now=True, use_tz=True, description="更新时间"
    )
    deleted_at = fields.DatetimeField(null=True, use_tz=True, description="软删除标记")

    class Meta:
        table = "equivalent_score_prediction"
        table_description = "高考专业等效分预测表"
        # 定义联合唯一索引，确保数据唯一性
        unique_together = (
            "school_name",
            "major_name",
            "predict_year",
        )

    def __str__(self):
        return f"EquivalentScorePrediction(id={self.id}, school={self.school_name}, major={self.major_name}, year={self.predict_year})"
