from tortoise import fields
from tortoise.models import Model


class AIMessage(Model):
    """
    AI 消息数据模型，对应数据库表：ai_message
    """

    id = fields.BigIntField(pk=True, auto_increment=True, description="ID，主键")
    conversation = fields.ForeignKeyField(
        "models.AIConversation", related_name="messages", description="关联会话ID"
    )
    role = fields.CharField(max_length=128, default="normal", description="消息角色")
    message_type = fields.CharField(max_length=128, null=True, description="消息类型")
    message_data = fields.JSONField(null=True, description="消息数据")
    message_status = fields.JSONField(null=True, description="消息状态")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, null=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "ai_message"
        table_description = "AI 消息表"

    def __str__(self):
        return f"AIMessage(id={self.id}, role={self.role})"
