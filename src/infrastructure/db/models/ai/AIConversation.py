from tortoise import fields
from tortoise.models import Model


class AIConversation(Model):
    """
    AI 会话数据模型，对应数据库表：ai_conversation
    """

    id = fields.BigIntField(pk=True, description="ID，主键")
    title = fields.CharField(max_length=50, description="会话标题")
    context = fields.JSONField(description="上下文内容")
    uni_user_id = fields.IntField(description="统一用户ID", default=101)
    user_features = fields.JSONField(description="用户画像数据")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "ai_conversation"
        table_description = "AI 会话表"

    def __str__(self):
        return f"AIConversation(id={self.id}, title={self.title})"
