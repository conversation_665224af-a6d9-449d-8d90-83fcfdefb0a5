from tortoise import fields
from tortoise.models import Model
from enum import Enum


class ReportTaskStatus(str, Enum):
    """报告任务状态枚举"""

    CREATED = "created"  # 任务已创建
    PROCESSING = "processing"  # 任务生成中
    COMPLETED = "completed"  # 任务生成完成
    FAILED = "failed"  # 任务生成失败
    CANCELLED = "cancelled"  # 任务已取消


class ReportTask(Model):
    """
    报告生成任务数据模型，对应数据库表：report_task
    """

    id = fields.BigIntField(primary_key=True, description="ID，主键")
    task_id = fields.CharField(max_length=64, unique=True, description="任务唯一标识")
    conversation_id = fields.BigIntField(description="关联会话ID")
    user_id = fields.BigIntField(description="用户ID")
    task_name = fields.CharField(max_length=200, description="任务名称")
    task_key = fields.CharField(
        max_length=100, description="任务key（对应预设任务列表）"
    )
    status = fields.CharEnumField(
        ReportTaskStatus, default=ReportTaskStatus.CREATED, description="任务状态"
    )
    progress = fields.IntField(default=0, description="任务进度 (0-100)")
    error_message = fields.TextField(null=True, description="错误信息")
    extra_data = fields.JSONField(null=True, description="扩展字段")
    report_content = fields.TextField(null=True, description="报告内容")

    # 时间字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    completed_at = fields.DatetimeField(null=True, description="完成时间")

    class Meta:
        table = "report_task"
        table_description = "报告生成任务表"
        # 添加索引
        indexes = [
            ("conversation_id", "status"),
            ("user_id", "created_at"),
            ("task_key", "status"),
        ]

    def __str__(self):
        return f"ReportTask(id={self.id}, task_id={self.task_id}, name={self.task_name}, status={self.status})"

    @property
    def is_completed(self) -> bool:
        """检查任务是否已完成"""
        return self.status in [
            ReportTaskStatus.COMPLETED,
            ReportTaskStatus.FAILED,
            ReportTaskStatus.CANCELLED,
        ]

    @property
    def is_processing(self) -> bool:
        """检查任务是否正在处理中"""
        return self.status == ReportTaskStatus.PROCESSING

    @property
    def is_successful(self) -> bool:
        """检查任务是否成功完成"""
        return self.status == ReportTaskStatus.COMPLETED

    def to_dict(self) -> dict:
        """转换为字典格式"""
        # 从extra_data中获取sae_job_task_id
        sae_job_task_id = None
        if self.extra_data:
            sae_job_task_id = self.extra_data.get("sae_job_task_id")

        return {
            "id": self.id,
            "task_id": self.task_id,
            "sae_job_task_id": sae_job_task_id,
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "task_name": self.task_name,
            "task_key": self.task_key,
            "status": self.status.value,
            "progress": self.progress,
            "error_message": self.error_message,
            "extra_data": self.extra_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "completed_at": self.completed_at.isoformat()
            if self.completed_at
            else None,
        }
