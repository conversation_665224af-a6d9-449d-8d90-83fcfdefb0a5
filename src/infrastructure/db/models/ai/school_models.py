from tortoise import fields
from tortoise.models import Model


# --- 主表模型：School ---
class School(Model):
    """
    高校信息数据模型，对应数据库表：schools
    """

    id = fields.IntField(pk=True, description="ID，主键 (对应 SERIAL)")
    gugu_data_id = fields.CharField(
        max_length=100, unique=True, null=True, description="咕咕数据ID"
    )
    school_uuid = fields.CharField(
        max_length=100, unique=True, null=True, description="咕咕数据平台高校唯一ID"
    )
    name = fields.CharField(
        max_length=255, unique=True, description="学院名称"
    )  # NOT NULL 由 Tortoise 默认处理，除非指定 null=True
    short_name = fields.CharField(max_length=100, null=True, description="学院简称")
    old_name = fields.TextField(null=True, description="学院旧称")
    province = fields.CharField(max_length=50, description="学院所在省份")
    city = fields.CharField(max_length=50, description="学院所在城市")
    district = fields.CharField(max_length=100, null=True, description="学院所在区县")
    address = fields.TextField(null=True, description="学院地址")
    coordinate = fields.CharField(
        max_length=100, null=True, description="学院地理坐标经纬度"
    )

    college_type = fields.CharField(max_length=50, null=True, description="学院性质")
    is_985 = fields.BooleanField(default=False, description="是否为 985 院校")
    is_211 = fields.BooleanField(default=False, description="是否为 211 院校")
    is_dual_class = fields.BooleanField(default=False, description="是否为双一流院校")
    college_category = fields.CharField(
        max_length=50, null=True, description="学院类别"
    )
    # 对于 TEXT[] 类型的 college_tags，使用 JSONField 在 Tortoise 中是常见的做法，
    # 它能很好地处理 Python 列表与数据库数组之间的转换。
    college_tags = fields.JSONField(null=True, description="学院标签 (存储为JSON列表)")
    edu_level = fields.CharField(max_length=50, null=True, description="学院学制")
    college_property = fields.CharField(
        max_length=50, null=True, description="学院资质"
    )
    college_code = fields.CharField(max_length=50, null=True, description="学院编号")

    ranking = fields.IntField(null=True, description="全国排名")
    ranking_in_category = fields.CharField(
        max_length=50, null=True, description="学院所在类别下排名"
    )

    website = fields.CharField(max_length=512, null=True, description="学院官网")
    call_number = fields.CharField(
        max_length=255, null=True, description="学院招生电话"
    )
    email = fields.CharField(max_length=255, null=True, description="学院招生邮箱")
    cover_image_url = fields.CharField(
        max_length=512, null=True, description="学院校徽URL"
    )
    intro = fields.TextField(null=True, description="学院简介")
    expenses = fields.TextField(null=True, description="学院收费（仅供参考）")

    campus_environment = fields.TextField(null=True, description="校区环境描述")

    data_source = fields.CharField(
        max_length=100, default="gugudata", description="数据来源标识"
    )
    last_fetched_at = fields.DatetimeField(
        null=True, description="上次从API获取数据的时间"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    # deleted_at = fields.DatetimeField(null=True, description="删除时间") # 如果你需要软删除功能

    # 定义反向关联
    # branches: fields.ReverseRelation["models.SchoolBranch"] # 'models' 是你应用/模块名
    # major_categories: fields.ReverseRelation["models.SchoolMajorCategory"]

    class Meta:
        table = "schools"
        table_description = "高校信息表"
        # ordering = ["ranking", "name"] # 可以定义默认排序

    def __str__(self):
        return f"School(id={self.id}, name={self.name})"


# --- 辅助表模型：SchoolBranch ---
class SchoolBranch(Model):
    """
    学校分校区信息模型，对应数据库表：school_branches
    """

    id = fields.IntField(pk=True, description="ID，主键")
    school: fields.ForeignKeyRelation[School] = fields.ForeignKeyField(
        "models.School",
        related_name="branches",
        description="所属学校",
        on_delete=fields.CASCADE,
    )
    branch_info = fields.TextField(description="校区名称和地址的完整字符串")
    # 如果拆分了 branch_name 和 branch_address:
    # branch_name = fields.CharField(max_length=255, null=True)
    # branch_address = fields.TextField(null=True)

    class Meta:
        table = "school_branches"
        table_description = "学校分校区信息表"

    def __str__(self):
        return f"SchoolBranch(id={self.id}, school_id={self.school_id}, info={self.branch_info[:50]})"  # 截断显示
