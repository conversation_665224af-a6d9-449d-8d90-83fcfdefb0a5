from tortoise import fields
from tortoise.models import Model


class SchoolMajor<PERSON><PERSON>ff(Model):
    """
    高考专业录取分数线数据模型，对应数据库表：major_admission_score
    """

    id = fields.BigIntField(pk=True, description="ID，主键")
    school_uuid = fields.Char<PERSON>ield(
        max_length=64, index=True, description="咕咕数据平台高校唯一ID"
    )
    school_name = fields.CharField(max_length=128, index=True, description="高校名称")
    province_name = fields.CharField(max_length=32, index=True, description="招生省份")
    major_name = fields.CharField(max_length=512, index=True, description="专业名称")
    major_code = fields.CharField(
        max_length=32, index=True, null=True, description="专业代码"
    )
    major_standard_code = fields.CharField(
        max_length=32, index=True, null=True, description="标准专业代码"
    )
    year = fields.IntField(index=True, description="招生年份")
    highest_score = fields.Char<PERSON>ield(max_length=32, null=True, description="录取最高分")
    average_score = fields.Char<PERSON><PERSON>(max_length=32, null=True, description="录取平均分")
    lowest_score = fields.CharField(max_length=32, description="录取最低分")
    lowest_section = fields.CharField(
        max_length=32, null=True, description="录取最低位次"
    )
    batch_name = fields.CharField(max_length=64, index=True, description="录取批次")
    type_name = fields.CharField(max_length=64, index=True, description="专业所属类型")
    pro_score = fields.CharField(max_length=32, null=True, description="专业分")
    subject_selection = fields.CharField(
        max_length=128, null=True, description="选科要求"
    )

    # 元数据及管理字段 - 修改时区处理设置
    created_at = fields.DatetimeField(
        auto_now_add=True, use_tz=True, description="创建时间"
    )
    updated_at = fields.DatetimeField(
        auto_now=True, use_tz=True, description="更新时间"
    )
    deleted_at = fields.DatetimeField(null=True, use_tz=True, description="删除时间")

    class Meta:
        table = "major_admission_score"
        table_description = "高考专业录取分数线表"
        # 定义联合唯一索引，确保数据唯一性
        unique_together = (
            "school_uuid",
            "province_name",
            "major_name",
            "year",
            "batch_name",
            "type_name",
        )

    def __str__(self):
        return f"MajorAdmissionScore(id={self.id}, school={self.school_name}, major={self.major_name}, year={self.year})"
