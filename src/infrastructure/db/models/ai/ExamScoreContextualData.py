from tortoise import fields
from tortoise.models import Model


class ExamScoreContextualData(Model):
    """
    高考一分一段表数据模型，对应数据库表：exam_score_contextual_data
    """

    id = fields.IntField(pk=True, description="ID，主键")
    query_year = fields.IntField(index=True, description="查询的年份")
    query_province_name = fields.CharField(
        max_length=64, index=True, description="查询的省份名称"
    )
    query_subject_selection = fields.CharField(
        max_length=64, index=True, description="查询的科目选择类型"
    )
    examination_score = fields.CharField(
        max_length=32, null=True, description="高考分数"
    )
    candidate_count = fields.IntField(null=True, description="该分数考生人数")
    total_candidates = fields.IntField(null=True, description="累计考生人数")
    ranking_range = fields.CharField(max_length=64, null=True, description="位次范围")
    admission_batch_name = fields.CharField(
        max_length=64, null=True, index=True, description="录取批次名称"
    )
    minimum_admission_score = fields.CharField(
        max_length=32, null=True, description="最低录取控制分数线"
    )
    ranking = fields.CharField(max_length=32, null=True, description="位次")
    historical_scores = fields.JSONField(
        null=True, description="历年同分段数据 (JSON格式)"
    )

    # 元数据及管理字段
    created_at = fields.DatetimeField(
        auto_now_add=True, use_tz=True, description="创建时间"
    )

    class Meta:
        table = "exam_score_contextual_data"
        table_description = "存储高考一分一段数据，包含查询上下文（年份、省份、科目）"
        # 定义联合唯一索引，确保数据唯一性
        unique_together = (
            "query_year",
            "query_province_name",
            "query_subject_selection",
            "examination_score",
        )

    def __str__(self):
        return f"ExamScoreContextualData(id={self.id}, year={self.query_year}, province={self.query_province_name}, subject={self.query_subject_selection}, score={self.examination_score})"
