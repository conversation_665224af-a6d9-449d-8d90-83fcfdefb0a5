from tortoise import fields
from tortoise.models import Model


class MajorInfo(Model):
    """
    专业信息数据模型，对应数据库表：major_info
    """

    id = fields.BigIntField(pk=True, description="ID，主键")
    education_level = fields.CharField(max_length=50, description="学历层次")
    disciplinary_category = fields.CharField(
        max_length=100, null=True, description="学科门类"
    )
    disciplinary_subcategory = fields.CharField(
        max_length=100, null=True, description="学科专业类"
    )
    major_code = fields.CharField(max_length=50, null=True, description="专业代码")
    major_name = fields.CharField(max_length=100, null=True, description="专业名称")
    major_introduction = fields.TextField(null=True, description="专业介绍")
    course = fields.JSONField(null=True, description="课程列表")  # 结构化存储
    graduate_scale = fields.CharField(
        max_length=100, null=True, description="毕业生规模（概略数据）"
    )
    male_female_ratio = fields.Cha<PERSON><PERSON><PERSON>(
        max_length=50, null=True, description="男女比例（概略数据）"
    )
    recommend_schools = fields.JSONField(
        null=True, description="推荐院校列表"
    )  # 用JSONField存储jsonb
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "major_info"
        table_description = "专业信息表"

    def __str__(self):
        return f"MajorInfo(id={self.id}, major_name={self.major_name})"
