"""
Bocha API Configuration

This module provides configuration for the Bocha AI API client.
"""

from typing import Optional


class BochaConfig:
    """Configuration for Bocha AI API."""

    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.bochaai.com",
        timeout: int = 30,
        default_market: Optional[str] = None,
        default_safe_search: Optional[str] = None,
    ):
        """
        Initialize Bocha API configuration.

        Args:
            api_key: The API key for authentication
            base_url: The base URL for the Bocha API
            timeout: Request timeout in seconds
            default_market: Default market code for localized results
            default_safe_search: Default safe search level
        """
        self.api_key = api_key
        self.base_url = base_url
        self.timeout = timeout
        self.default_market = default_market
        self.default_safe_search = default_safe_search
