"""
Bocha API Response Models

This module contains Pydantic models for Bocha API responses.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class QueryContext(BaseModel):
    """Query context information in search response."""

    original_query: Optional[str] = Field(None, alias="originalQuery")


class WebPageItem(BaseModel):
    """Individual web page result item."""

    id: str
    name: str
    url: str
    site_name: Optional[str] = Field(None, alias="siteName")
    site_icon: Optional[str] = Field(None, alias="siteIcon")
    snippet: Optional[str] = None
    summary: Optional[str] = None
    date_published: Optional[datetime] = Field(None, alias="datePublished")


class WebPages(BaseModel):
    """Web pages collection in search response."""

    web_search_url: Optional[str] = Field(None, alias="webSearchUrl")
    total_estimated_matches: Optional[int] = Field(None, alias="totalEstimatedMatches")
    value: List[WebPageItem] = Field(default_factory=list)


class WebSearchResponse(BaseModel):
    """Bocha Web Search API response."""

    code: int
    log_id: Optional[str] = Field(None, alias="log_id")
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

    # 为了向后兼容，添加这些属性
    @property
    def response_type(self) -> str:
        """Extract response type from data field."""
        if self.data and isinstance(self.data, dict):
            return self.data.get("_type", "")
        return ""

    @property
    def query_context(self) -> Optional[QueryContext]:
        """Extract query context from data field."""
        if self.data and isinstance(self.data, dict):
            query_context_data = self.data.get("queryContext")
            if query_context_data:
                return QueryContext(**query_context_data)
        return None

    @property
    def web_pages(self) -> Optional[WebPages]:
        """Extract web pages from data field."""
        if self.data and isinstance(self.data, dict):
            web_pages_data = self.data.get("webPages")
            if web_pages_data:
                return WebPages(**web_pages_data)
        return None


class RerankResult(BaseModel):
    """Individual rerank result item."""

    document: str
    index: int
    score: Optional[float] = None


class RerankResponse(BaseModel):
    """Bocha Rerank API response."""

    results: List[RerankResult]


class AISearchResult(BaseModel):
    """Individual AI Search result item."""

    content: str
    source: Optional[str] = None
    title: Optional[str] = None
    url: Optional[str] = None
    created_at: Optional[datetime] = Field(None, alias="createdAt")


class AISearchResponse(BaseModel):
    """Bocha AI Search API response."""

    code: int
    log_id: Optional[str] = Field(None, alias="log_id")
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

    # 为了向后兼容，添加这些属性
    @property
    def query(self) -> str:
        """Extract query from data field."""
        if self.data and isinstance(self.data, dict):
            return self.data.get("query", "")
        return ""

    @property
    def results(self) -> List[AISearchResult]:
        """Extract results from data field."""
        if self.data and isinstance(self.data, dict):
            results_data = self.data.get("results", [])
            return [
                AISearchResult(**item) if isinstance(item, dict) else item
                for item in results_data
            ]
        return []

    @property
    def total(self) -> int:
        """Extract total from data field."""
        if self.data and isinstance(self.data, dict):
            return self.data.get("total", 0)
        return 0
