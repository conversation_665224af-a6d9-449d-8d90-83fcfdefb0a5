"""
Bocha AI API Client

This module provides a client for interacting with the Bocha AI API services,
including the Search API and Rerank API.
"""

from typing import Dict, List, Optional, Any
import httpx
from httpx import Response
from lucas_common_components.logging import setup_logger

from src.infrastructure.bocha.config import BochaConfig
from src.infrastructure.bocha.models import (
    WebSearchResponse,
    RerankResponse,
    AISearchResponse,
)

logger = setup_logger(__name__)


class BochaClient:
    """
    Client for interacting with Bocha AI API.

    This client provides methods to access Bocha's Search API and Rerank API services.
    """

    WEB_SEARCH_ENDPOINT = "/v1/web-search"
    RERANK_ENDPOINT = "/v1/rerank"
    AI_SEARCH_ENDPOINT = "/v1/ai-search"

    def __init__(self, config: BochaConfig):
        """
        Initialize the Bocha API client.

        Args:
            config: BochaConfig instance containing API configuration
        """
        self.config = config
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json",
        }

    async def web_search(
        self,
        query: str,
        count: int = 8,
        freshness: Optional[str] = None,
        summary: bool = True,
        market: Optional[str] = None,
        safe_search: Optional[str] = None,
    ) -> WebSearchResponse:
        """
        Perform a web search using Bocha AI Search API.

        Args:
            query: The search query
            count: Number of results to return (default: 8)
            freshness: Time filter for results (e.g., "oneDay", "oneWeek", "oneMonth", "oneYear")
            summary: Whether to generate a summary of the results (default: True)
            market: The market code for localized results (e.g., "zh-CN")
            safe_search: Safe search filter level (e.g., "Off", "Moderate", "Strict")

        Returns:
            WebSearchResponse object containing search results

        Raises:
            BochaAPIException: If an error occurs during the API call
        """
        url = f"{self.config.base_url}{self.WEB_SEARCH_ENDPOINT}"

        payload = {"query": query, "count": count, "summary": summary}

        if freshness:
            payload["freshness"] = freshness

        # Use config default if not specified
        effective_market = market or self.config.default_market
        if effective_market:
            payload["market"] = effective_market

        # Use config default if not specified
        effective_safe_search = safe_search or self.config.default_safe_search
        if effective_safe_search:
            payload["safeSearch"] = effective_safe_search

        logger.debug(f"Sending search request to Bocha API: {payload}")

        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.post(url, headers=self.headers, json=payload)
                response.raise_for_status()
                return WebSearchResponse.model_validate(response.json())
        except httpx.HTTPStatusError as e:
            logger.error(
                f"Bocha API HTTP error: {e.response.status_code} - {e.response.text}"
            )
            raise BochaAPIException(f"HTTP error: {e.response.status_code}", e.response)
        except httpx.RequestError as e:
            logger.error(f"Bocha API request error: {str(e)}")
            raise BochaAPIException(f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error when calling Bocha API: {str(e)}")
            raise BochaAPIException(f"Unexpected error: {str(e)}")

    async def ai_search(
        self,
        query: str,
        limit: int = 10,
        connection: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        context: Optional[str] = None,
    ) -> AISearchResponse:
        """
        Perform an AI-enhanced search using Bocha AI Search API.

        Args:
            query: The search query
            limit: Maximum number of results to return (default: 10)
            connection: The connection type or source to search in
            filters: Additional filters to apply to the search
            context: Additional context to provide for the search

        Returns:
            AISearchResponse object containing search results

        Raises:
            BochaAPIException: If an error occurs during the API call
        """
        url = f"{self.config.base_url}{self.AI_SEARCH_ENDPOINT}"

        payload = {"query": query, "limit": limit}

        if connection:
            payload["connection"] = connection

        if filters:
            payload["filters"] = filters

        if context:
            payload["context"] = context

        logger.debug(f"Sending AI search request to Bocha API: {payload}")

        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.post(url, headers=self.headers, json=payload)
                response.raise_for_status()
                return AISearchResponse.parse_obj(response.json())
        except httpx.HTTPStatusError as e:
            logger.error(
                f"Bocha API HTTP error: {e.response.status_code} - {e.response.text}"
            )
            raise BochaAPIException(f"HTTP error: {e.response.status_code}", e.response)
        except httpx.RequestError as e:
            logger.error(f"Bocha API request error: {str(e)}")
            raise BochaAPIException(f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error when calling Bocha API: {str(e)}")
            raise BochaAPIException(f"Unexpected error: {str(e)}")

    async def rerank(
        self,
        query: str,
        documents: List[str],
        top_k: Optional[int] = None,
        return_scores: bool = True,
    ) -> RerankResponse:
        """
        Rerank documents based on relevance to the query using Bocha Semantic Reranker API.

        Args:
            query: The query to use for ranking
            documents: List of documents to rank
            top_k: Number of top results to return
            return_scores: Whether to include relevance scores in the response

        Returns:
            RerankResponse object containing reranking results

        Raises:
            BochaAPIException: If an error occurs during the API call
        """
        url = f"{self.config.base_url}{self.RERANK_ENDPOINT}"

        payload = {
            "query": query,
            "documents": documents,
            "return_scores": return_scores,
        }

        if top_k is not None:
            payload["top_k"] = top_k

        logger.debug(
            f"Sending rerank request to Bocha API with {len(documents)} documents"
        )

        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.post(url, headers=self.headers, json=payload)
                response.raise_for_status()
                return RerankResponse.parse_obj(response.json())
        except httpx.HTTPStatusError as e:
            logger.error(
                f"Bocha API HTTP error: {e.response.status_code} - {e.response.text}"
            )
            raise BochaAPIException(f"HTTP error: {e.response.status_code}", e.response)
        except httpx.RequestError as e:
            logger.error(f"Bocha API request error: {str(e)}")
            raise BochaAPIException(f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error when calling Bocha API: {str(e)}")
            raise BochaAPIException(f"Unexpected error: {str(e)}")


class BochaAPIException(Exception):
    """Exception raised for errors in the Bocha API."""

    def __init__(self, message: str, response: Optional[Response] = None):
        self.message = message
        self.response = response
        super().__init__(self.message)

    def __str__(self):
        if self.response:
            return f"{self.message} - Status: {self.response.status_code}, Body: {self.response.text}"
        return self.message
