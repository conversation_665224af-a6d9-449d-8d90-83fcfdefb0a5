"""
雪花算法ID生成器 - 基于snowflake-id库的封装

使用成熟的第三方库替代自实现，提高稳定性和维护性。
"""

from snowflake import SnowflakeGenerator
from lucas_common_components.logging import setup_logger

logger = setup_logger(name=__name__, level="DEBUG")


class Snowflake:
    """
    雪花算法ID生成器

    基于snowflake-id库的封装，保持与原有API的兼容性。

    Args:
        worker_id (int): 工作机器ID (0-31)
        datacenter_id (int): 数据中心ID (0-31)
    """

    __TWITTER_EPOCH = 1288834974657

    def __init__(self, worker_id: int, datacenter_id: int, sequence: int = 0):
        """
        初始化雪花算法生成器

        Args:
            worker_id: 工作机器ID (0-31)
            datacenter_id: 数据中心ID (0-31)
            sequence: 序列号 (兼容参数，实际由库内部管理)
        """
        # 验证参数范围
        if worker_id < 0 or worker_id > 31:
            raise ValueError("worker_id值越界，应在0-31范围内")

        if datacenter_id < 0 or datacenter_id > 31:
            raise ValueError("datacenter_id值越界，应在0-31范围内")

        # 将worker_id和datacenter_id组合成一个实例ID
        # snowflake-id库使用单个实例ID，我们将两个ID组合
        # datacenter_id作为高位，worker_id作为低位
        instance_id = (datacenter_id << 5) | worker_id

        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 创建snowflake生成器
        self._generator = SnowflakeGenerator(instance_id, epoch=self.__TWITTER_EPOCH)

        logger.debug(
            f"初始化Snowflake生成器: worker_id={worker_id}, datacenter_id={datacenter_id}, instance_id={instance_id}"
        )

    def generate(self) -> str:
        """
        生成新的雪花算法ID

        Returns:
            str: 生成的ID字符串
        """
        try:
            # 使用snowflake-id库生成ID
            snowflake_id = next(self._generator)
            return str(snowflake_id)
        except Exception as e:
            logger.error(f"生成雪花算法ID失败: {str(e)}")
            raise Exception(f"生成雪花算法ID失败: {str(e)}")


if __name__ == "__main__":
    # 测试代码
    sn = Snowflake(1, 1)
    print("生成的ID:", sn.generate())
    print("再次生成:", sn.generate())
