import io
import re
from html.parser import HTMLParser

import markdown
from lucas_common_components.logging import setup_logger
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import (
    SimpleDocTemplate,
    Paragraph,
    Spacer,
    ListFlowable,
    ListItem,
    PageBreak,
    Table,
    TableStyle,
)
from reportlab.platypus.flowables import HRFlowable

logger = setup_logger(name=__name__, level="DEBUG")

CHINESE_FONT_NAME = "AlibabaPuHuiTi-3-55-Regular"

PRESET_STYLES: dict[str, ParagraphStyle] = {
    "Title": ParagraphStyle(
        name="Title",
        fontName=CHINESE_FONT_NAME,
        fontSize=24,
        leading=28,
        alignment=1,
        spaceAfter=20,
    ),
    "Heading1": ParagraphStyle(
        name="Heading1",
        fontName=CHINESE_FONT_NAME,
        fontSize=18,
        leading=22,
        spaceBefore=12,
        spaceAfter=6,
    ),
    "Heading2": ParagraphStyle(
        name="Heading2",
        fontName=CHINESE_FONT_NAME,
        fontSize=16,
        leading=20,
        spaceBefore=10,
        spaceAfter=6,
    ),
    "Heading3": ParagraphStyle(
        name="Heading3",
        fontName=CHINESE_FONT_NAME,
        fontSize=14,
        leading=18,
        spaceBefore=8,
        spaceAfter=6,
    ),
    "Heading4": ParagraphStyle(
        name="Heading4",
        fontName=CHINESE_FONT_NAME,
        fontSize=12,
        leading=16,
        spaceBefore=6,
        spaceAfter=4,
    ),
    "Heading5": ParagraphStyle(
        name="Heading5",
        fontName=CHINESE_FONT_NAME,
        fontSize=12,
        leading=16,
        spaceBefore=6,
        spaceAfter=4,
    ),
    "Heading6": ParagraphStyle(
        name="Heading6",
        fontName=CHINESE_FONT_NAME,
        fontSize=12,
        leading=16,
        spaceBefore=6,
        spaceAfter=4,
    ),
    "BodyText": ParagraphStyle(
        name="BodyText",
        fontName=CHINESE_FONT_NAME,
        fontSize=12,
        leading=16,
        spaceBefore=6,
        spaceAfter=6,
    ),
    "Code": ParagraphStyle(
        name="Code",
        fontName="Courier",
        fontSize=10,
        leading=14,
        spaceBefore=6,
        spaceAfter=6,
        backColor=colors.lightgrey,
        borderPadding=5,
    ),
}


async def convert_markdown_to_pdf(markdown_content: str, title: str = "报告") -> bytes:
    """
    将 Markdown 内容转换为 PDF，使用改进的 ReportLab 实现提供更好的中文支持和排版

    Args:
        markdown_content: Markdown 格式的内容
        title: PDF 文档标题

    Returns:
        PDF 文件的字节内容
    """
    try:
        # 预处理 markdown 内容，处理被 ```markdown ``` 包裹的情况
        if markdown_content.strip().startswith(
            "```markdown"
        ) and markdown_content.strip().endswith("```"):
            # 移除开头的 ```markdown 和结尾的 ```
            markdown_content = markdown_content.strip()
            markdown_content = markdown_content[len("```markdown") :].strip()
            if markdown_content.endswith("```"):
                markdown_content = markdown_content[:-3].strip()
            logger.info("检测到 markdown 内容被 ```markdown ``` 包裹，已移除包裹标记")

        # 解析 Markdown 为 HTML
        html_content = markdown.markdown(
            markdown_content, extensions=["tables", "fenced_code"]
        )

        # 创建 PDF 缓冲区
        pdf_buffer = io.BytesIO()

        # 创建文档
        doc = SimpleDocTemplate(
            pdf_buffer,
            pagesize=A4,
            title=title,
            author="YAI Chat",
            leftMargin=2 * cm,
            rightMargin=2 * cm,
            topMargin=2 * cm,
            bottomMargin=2 * cm,
        )

        # 注册中文字体
        chinese_font = register_chinese_fonts()

        # 创建样式
        styles = create_styles(chinese_font)

        # 解析 HTML 并创建 Flowables
        elements = parse_html_to_flowables(html_content, styles, title, doc.width)

        # 构建 PDF
        doc.build(elements)

        # 获取 PDF 字节内容
        pdf_buffer.seek(0)
        return pdf_buffer.getvalue()

    except Exception as e:
        logger.error(f"Markdown 转 PDF 失败: {str(e)}", exc_info=True)
        raise Exception(f"PDF 转换失败: {str(e)}")


def register_chinese_fonts():
    """注册中文字体并嵌入到 PDF 中"""

    import os

    # 默认字体
    default_font = "Helvetica"

    # 首先尝试使用项目内置的字体（优先使用内置字体确保跨平台一致性）
    try:
        # 检查项目中是否有内置字体目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        fonts_dir = os.path.join(project_root, "fonts")
        font_path = os.path.join(fonts_dir, f"{CHINESE_FONT_NAME}.ttf")

        try:
            # 注册字体并设置嵌入标志为 True
            font = TTFont(CHINESE_FONT_NAME, font_path, validate=False)
            pdfmetrics.registerFont(font)
            logger.info(f"成功注册内置中文字体: {CHINESE_FONT_NAME}")
            return CHINESE_FONT_NAME
        except Exception as e:
            logger.warning(f"注册内置字体 {CHINESE_FONT_NAME} 失败: {str(e)}")
    except Exception as e:
        logger.warning(f"尝试使用内置字体失败: {str(e)}")

    return default_font


def create_styles(chinese_font):
    """创建文档样式"""
    styles = getSampleStyleSheet()

    # 检查样式是否已存在，避免重复定义
    existing_styles = set(styles.byName.keys())

    # 修改默认样式以使用中文字体
    for style_name in styles.byName:
        styles[style_name].fontName = chinese_font

    for style, paragraph_style in PRESET_STYLES.items():
        if style in existing_styles:
            styles[style].name = paragraph_style.name
            styles[style].fontName = paragraph_style.fontName
            styles[style].fontSize = paragraph_style.fontSize
            styles[style].leading = paragraph_style.leading
            styles[style].spaceBefore = paragraph_style.spaceBefore
            styles[style].spaceAfter = paragraph_style.spaceAfter
            styles[style].backColor = paragraph_style.backColor
            styles[style].borderPadding = paragraph_style.borderPadding
            styles[style].alignment = paragraph_style.alignment
        else:
            styles.add(paragraph_style)

    # 添加引用样式
    if "Blockquote" not in existing_styles:
        styles.add(
            ParagraphStyle(
                name="Blockquote",
                parent=styles["BodyText"],
                fontName=chinese_font,
                fontSize=12,
                leading=16,
                leftIndent=20,
                rightIndent=20,
                spaceBefore=6,
                spaceAfter=6,
                borderWidth=1,
                borderColor=colors.grey,
                borderPadding=5,
                borderRadius=2,
            )
        )

    return styles


def parse_html_to_flowables(html_content, styles, title, doc_width=None):
    """解析 HTML 并创建 Flowables"""
    elements = []

    # 添加文档标题
    elements.append(Paragraph(title, styles["Title"]))
    elements.append(Spacer(1, 0.5 * inch))

    # 分割 HTML 内容
    lines = html_content.split("\n")
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        # 处理标题
        match = re.match(r"<h(\d)>(.*?)</h\1>", line.strip(), re.IGNORECASE)
        if match:
            # 提取标题级别和内容 构建样式名称并添加到元素列表
            level = match.group(1)
            text = match.group(2)
            elements.append(Paragraph(text, styles[f"Heading{level}"]))

        # 处理段落
        elif line.startswith("<p>"):
            text = strip_tags(line)
            elements.append(Paragraph(text, styles["BodyText"]))

        # 处理列表
        elif line.startswith("<ul>") or line.startswith("<ol>"):
            is_ordered = line.startswith("<ol>")
            list_items = []
            i += 1

            while i < len(lines) and not (
                lines[i].strip().startswith("</ul>")
                or lines[i].strip().startswith("</ol>")
            ):
                if lines[i].strip().startswith("<li>"):
                    text = strip_tags(lines[i].strip())
                    list_items.append(ListItem(Paragraph(text, styles["BodyText"])))
                i += 1

            elements.append(
                ListFlowable(
                    list_items,
                    bulletType="1" if is_ordered else "bullet",
                    start=1 if is_ordered else None,
                    bulletFontName=styles["BodyText"].fontName,
                    bulletFontSize=styles["BodyText"].fontSize,
                )
            )

        # 处理表格
        elif line.startswith("<table>"):
            table_data = []
            i += 1

            # 处理表头
            if i < len(lines) and lines[i].strip().startswith("<thead>"):
                i += 1
                if i < len(lines) and lines[i].strip().startswith("<tr>"):
                    i += 1
                    header_row = []
                    while i < len(lines) and not lines[i].strip().startswith("</tr>"):
                        if lines[i].strip().startswith("<th>"):
                            text = strip_tags(lines[i].strip())
                            # 限制表头单元格内容长度
                            # if len(text) > 100:
                            #     text = text[:100] + '...'
                            header_row.append(Paragraph(text, styles["BodyText"]))
                        i += 1
                    table_data.append(header_row)
                    i += 1  # 跳过 </tr>
                i += 1  # 跳过 </thead>

            # 处理表格内容
            if i < len(lines) and lines[i].strip().startswith("<tbody>"):
                i += 1
                while i < len(lines) and not lines[i].strip().startswith("</tbody>"):
                    if lines[i].strip().startswith("<tr>"):
                        i += 1
                        row = []
                        while i < len(lines) and not lines[i].strip().startswith(
                            "</tr>"
                        ):
                            if lines[i].strip().startswith("<td>"):
                                text = strip_tags(lines[i].strip())
                                # 限制表格单元格内容长度
                                # if len(text) > 100:
                                #     text = text[:100] + '...'
                                cell_style = ParagraphStyle(
                                    "CellStyle",
                                    parent=styles["BodyText"],
                                    wordWrap="CJK",  # 确保中文正确换行
                                    maxHeight=300,  # 限制单元格最大高度
                                )
                                row.append(Paragraph(text, cell_style))
                            i += 1
                        table_data.append(row)
                    i += 1

            # 创建表格
            if table_data:
                # 确保表格数据完整性
                if not all(len(row) == len(table_data[0]) for row in table_data):
                    # 修复不完整的行
                    max_cols = max(len(row) for row in table_data)
                    for row in table_data:
                        while len(row) < max_cols:
                            row.append(Paragraph("", styles["BodyText"]))

                # 限制表格大小，防止过大的表格
                MAX_ROWS = 100  # 最大行数
                MAX_COLS = 50  # 最大列数

                if len(table_data) > MAX_ROWS:
                    logger.warning(
                        f"表格行数过多 ({len(table_data)}), 截断至 {MAX_ROWS} 行"
                    )
                    table_data = table_data[:MAX_ROWS]
                    # 添加一个提示行
                    warning_row = [
                        Paragraph(
                            f"[表格过大，仅显示前 {MAX_ROWS} 行]", styles["BodyText"]
                        )
                    ]
                    warning_row.extend(
                        [Paragraph("", styles["BodyText"])] * (len(table_data[0]) - 1)
                    )
                    table_data.append(warning_row)

                # 限制列数
                if len(table_data[0]) > MAX_COLS:
                    logger.warning(
                        f"表格列数过多 ({len(table_data[0])}), 截断至 {MAX_COLS} 列"
                    )
                    for i in range(len(table_data)):
                        if i == 0:  # 表头行
                            table_data[i] = table_data[i][: MAX_COLS - 1] + [
                                Paragraph("[...]", styles["BodyText"])
                            ]
                        else:  # 数据行
                            table_data[i] = table_data[i][: MAX_COLS - 1] + [
                                Paragraph("[...]", styles["BodyText"])
                            ]

                # 限制表格宽度，确保不超过页面宽度
                available_width = doc_width if doc_width else 450  # 默认宽度
                col_widths = [available_width / len(table_data[0])] * len(table_data[0])

                try:
                    table = Table(table_data, repeatRows=1, colWidths=col_widths)
                    table.setStyle(
                        TableStyle(
                            [
                                ("BACKGROUND", (0, 0), (-1, 0), colors.lightgrey),
                                ("TEXTCOLOR", (0, 0), (-1, 0), colors.black),
                                ("ALIGN", (0, 0), (-1, -1), "CENTER"),
                                (
                                    "FONTNAME",
                                    (0, 0),
                                    (-1, 0),
                                    styles["BodyText"].fontName,
                                ),
                                (
                                    "FONTSIZE",
                                    (0, 0),
                                    (-1, 0),
                                    styles["BodyText"].fontSize,
                                ),
                                ("BOTTOMPADDING", (0, 0), (-1, 0), 12),
                                ("BACKGROUND", (0, 1), (-1, -1), colors.white),
                                ("GRID", (0, 0), (-1, -1), 1, colors.black),
                                ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 垂直居中
                                ("LEFTPADDING", (0, 0), (-1, -1), 5),  # 左侧填充
                                ("RIGHTPADDING", (0, 0), (-1, -1), 5),  # 右侧填充
                            ]
                        )
                    )
                    elements.append(table)
                    elements.append(Spacer(1, 0.2 * inch))
                except Exception as e:
                    logger.error(f"创建表格失败: {str(e)}")
                    # 如果表格创建失败，添加一个错误提示
                    elements.append(
                        Paragraph(
                            f"[表格内容过大或格式不正确，无法显示]\n错误: {str(e)}",
                            styles["BodyText"],
                        )
                    )
                    elements.append(Spacer(1, 0.2 * inch))

        # 处理代码块
        elif line.startswith("<pre><code>"):
            code_lines = []
            i += 1

            while i < len(lines) and not lines[i].strip().startswith("</code></pre>"):
                code_lines.append(lines[i])
                i += 1

            code_text = "\n".join(code_lines)
            elements.append(Paragraph(code_text, styles["Code"]))

        # 处理引用
        elif line.startswith("<blockquote>"):
            quote_lines = []
            i += 1

            # 收集引用块中的所有文本，不仅仅是 <p> 标签开头的行
            start_p = False
            current_text = ""

            while i < len(lines) and not lines[i].strip().startswith("</blockquote>"):
                line = lines[i].strip()

                # 如果遇到新的段落开始
                if line.startswith("<p>"):
                    if start_p and current_text:  # 如果已经有一个段落在处理中，先保存它
                        quote_lines.append(current_text)
                        current_text = ""

                    start_p = True
                    current_text = strip_tags(line)

                # 如果遇到段落结束
                elif line.startswith("</p>"):
                    start_p = False
                    if current_text:  # 保存当前段落
                        quote_lines.append(current_text)
                        current_text = ""

                # 如果是段落中的内容
                elif start_p:
                    current_text += " " + strip_tags(line)

                i += 1

            # 确保最后一个段落也被添加
            if current_text:
                quote_lines.append(current_text)

            # 合并所有引用文本并创建段落
            quote_text = "\n".join(quote_lines)
            if quote_text:
                elements.append(Paragraph(quote_text, styles["Blockquote"]))

        # 处理水平线
        elif line.startswith("<hr>"):
            elements.append(
                HRFlowable(
                    width="100%",
                    thickness=1,
                    color=colors.black,
                    spaceBefore=10,
                    spaceAfter=10,
                )
            )

        # 处理分页符
        elif line.startswith('<div class="page-break">') or line.startswith(
            '<div style="page-break-after: always;">'
        ):
            elements.append(PageBreak())

        i += 1

    return elements


class MLStripper(HTMLParser):
    """HTML 标签剥离器"""

    def __init__(self):
        super().__init__()
        self.reset()
        self.fed = []

    def handle_data(self, d):
        self.fed.append(d)

    def get_data(self):
        return "".join(self.fed)


def strip_tags(html):
    """移除 HTML 标签"""
    s = MLStripper()
    s.feed(html)
    return s.get_data()
