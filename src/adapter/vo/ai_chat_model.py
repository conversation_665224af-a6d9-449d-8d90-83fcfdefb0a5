from typing import Optional, Any, List, Dict
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator


class MessageVO(BaseModel):
    text: Optional[str] = None
    role: str = None
    params: Any = None


class ChatInputVO(BaseModel):
    conversation_id: str | None = None
    messages: List[MessageVO]
    interrupt_feedback: Optional[bool] = False


class CreateChatArgVO(BaseModel):
    input: str | None = None
    conversation_action: str | None = None


class CreateChatResultVO(BaseModel):
    conversation_id: str | None = None


class AiMessageHistoryArgVO(BaseModel):
    conversation_id: str | None = None


class MessageHistoryVO(BaseModel):
    text: Optional[str] = None
    custom: Optional[Any] = None
    role: str


class AiMessageHistoryResultVO(BaseModel):
    list: List[MessageHistoryVO]


class ConversationVO(BaseModel):
    id: str
    title: str
    context: dict
    created_at: str | None = None
    updated_at: str | None = None

    @field_validator("created_at", "updated_at")
    @classmethod
    def format_datetime(cls, v: str | None) -> str | None:
        if v is None:
            return None
        try:
            dt = datetime.fromisoformat(v.replace("Z", "+00:00"))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, AttributeError):
            return v


class ConversationListResultVO(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    list: List[ConversationVO] = None

    def get_grouped_conversations(self) -> Dict[str, List[ConversationVO]]:
        if not self.list:
            return {}

        today = datetime.now().date()
        grouped = {
            "today": [],
            "yesterday": [],
            "last_7_days": [],
            "last_30_days": [],
            "older": [],
        }

        for conversation in self.list:
            if not conversation.updated_at:
                continue

            try:
                # 将字符串转换为datetime对象
                updated_at = datetime.strptime(
                    conversation.updated_at, "%Y-%m-%d %H:%M:%S"
                )
                updated_date = updated_at.date()

                # 计算时间差
                delta = today - updated_date

                if delta.days == 0:
                    grouped["today"].append(conversation)
                elif delta.days == 1:
                    grouped["yesterday"].append(conversation)
                elif delta.days <= 7:
                    grouped["last_7_days"].append(conversation)
                elif delta.days <= 30:
                    grouped["last_30_days"].append(conversation)
                else:
                    grouped["older"].append(conversation)
            except ValueError:
                # 如果日期解析失败，跳过该记录
                continue

        # 对每个分组内的会话按更新时间降序排序
        for group_name in grouped:
            grouped[group_name].sort(
                key=lambda x: datetime.strptime(x.updated_at, "%Y-%m-%d %H:%M:%S")
                if x.updated_at
                else datetime.min,
                reverse=True,
            )

        # 移除空列表
        return {k: v for k, v in grouped.items() if v}


# 报告进度相关 VO 模型
class ReportProgressQueryVO(BaseModel):
    """报告进度查询请求"""

    report_section_task_id: str
