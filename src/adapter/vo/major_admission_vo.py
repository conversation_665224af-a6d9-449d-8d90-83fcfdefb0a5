from pydantic import BaseModel, Field
from typing import Optional, List


class CollectByYearVO(BaseModel):
    """采集指定年份全国高校专业录取分数线数据的VO对象"""

    years: List[int] = Field(..., description="要采集的年份，取值范围2000-2100")
    batch_size: int = Field(100, ge=10, le=500, description="每页记录数，默认100")
    max_workers: int = Field(1, ge=1, le=10, description="并行处理的工作线程数，默认1")


class CollectResultVO(BaseModel):
    """采集结果VO对象"""

    success: bool = Field(..., description="是否成功")
    total_count: int = Field(..., description="总数据量")
    saved_count: int = Field(..., description="保存数量")
    provinces_count: int = Field(..., description="省份数量")
    execution_time_seconds: float = Field(..., description="执行时间（秒）")
    message: str = Field(..., description="处理结果消息")
    provinces_results: Optional[dict] = Field(None, description="各省份处理结果详情")


class TaskStatusVO(BaseModel):
    """任务状态VO对象"""

    status: str = Field(..., description="任务状态：idle-空闲，running-运行中")
    year: Optional[int] = Field(None, description="当前采集的年份")
    elapsed_seconds: Optional[float] = Field(None, description="已运行时间（秒）")
    provinces_processed: Optional[int] = Field(None, description="已处理省份数")
    total_provinces: Optional[int] = Field(None, description="总省份数")
