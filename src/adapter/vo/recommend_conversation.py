from typing import List

from pydantic import BaseModel


class ConversationItem(BaseModel):
    id: int
    title: str
    updated_at: str


class ConversationGroup(BaseModel):
    job_id: int
    job_name: str
    conversations: List[ConversationItem]


# 查询操作响应模型
class ConversationListResponseVO(BaseModel):
    groups: List[ConversationGroup]


# 删除操作响应模型
class DeleteConversationResponseVO(BaseModel):
    deleted_id: int
    deleted_at: str


# 更新操作响应模型
class UpdateConversationResponseVO(BaseModel):
    id: int
    new_title: str
    updated_at: str
