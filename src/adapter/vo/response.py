"""
标准API响应模型

该模块定义了API响应的标准格式，用于统一API的返回结构。
"""

from typing import Generic, TypeVar, Optional, List
from pydantic import BaseModel, Field

T = TypeVar("T")


class GatewayResponse(BaseModel, Generic[T]):
    """网关响应模型

    标准的API响应格式，包含状态码、消息和数据。

    Attributes:
        code: 状态码，0表示成功，非0表示错误
        message: 响应消息
        data: 响应数据
    """

    code: int = Field(0, description="状态码，0表示成功，非0表示错误")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

    @staticmethod
    def success(data: T) -> "GatewayResponse[T]":
        """创建成功响应

        Args:
            data: 响应数据

        Returns:
            GatewayResponse: 成功响应对象
        """
        return GatewayResponse(code=0, message="success", data=data)

    @staticmethod
    def error(code: int, message: str) -> "GatewayResponse":
        """创建错误响应

        Args:
            code: 错误码
            message: 错误消息

        Returns:
            GatewayResponse: 错误响应对象
        """
        return GatewayResponse(code=code, message=message)


class PageData(BaseModel, Generic[T]):
    """分页数据模型

    用于分页接口的数据结构。

    Attributes:
        total: 总记录数
        page: 当前页码
        size: 每页大小
        items: 数据项列表
    """

    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    items: List[T] = Field(..., description="数据项列表")

    @staticmethod
    def create(
        items: List[T], total: int, page: int = 1, size: int = 10
    ) -> "PageData[T]":
        """创建分页数据

        Args:
            items: 数据项列表
            total: 总记录数
            page: 当前页码
            size: 每页大小

        Returns:
            PageData: 分页数据对象
        """
        return PageData(total=total, page=page, size=size, items=items)
