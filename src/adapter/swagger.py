from fastapi import APIRouter, Request
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

router = APIRouter(tags=["swagger"])


@router.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义Swagger UI页面"""
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title="AI JOB API 文档",
        swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
    )


@router.get("/aichat/openapi.json", include_in_schema=False)
async def get_openapi_endpoint(request: Request):
    """获取OpenAPI JSON文档"""

    app = request.app

    return get_openapi(
        title=app.title,
        version=app.version,
        routes=app.routes,
    )
