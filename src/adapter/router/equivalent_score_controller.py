from fastapi import APIRouter, Depends
from lucas_common_components.logging import setup_logger
from pydantic import BaseModel

from src.adapter.vo.gateway_response import GatewayResponse
from src.app.equivalent_score_service import EquivalentScoreService

# 创建logger
logger = setup_logger(__name__)

# 创建路由
router = APIRouter(
    prefix="/api/equivalent-scores",
    tags=["等效分计算"],
)


# VO模型定义
class CalculationRequestVO(BaseModel):
    """等效分计算请求"""

    year_old: int  # 旧年份
    year_new: int  # 新年份


class CalculationResponseVO(BaseModel):
    """等效分计算响应"""

    message: str  # 处理结果消息
    processed_count: int  # 处理数量


# 依赖注入：获取EquivalentScoreService实例
def get_equivalent_score_service():
    # 直接创建服务实例
    return EquivalentScoreService()


@router.post(
    "/calculate",
    response_model=GatewayResponse[CalculationResponseVO],
    summary="计算高考等效分",
    description="计算所有学校专业在各省份各科目组合的高考等效分",
)
async def calculate_equivalent_scores(
    vo: CalculationRequestVO,
    service: EquivalentScoreService = Depends(get_equivalent_score_service),
):
    """
    计算高考等效分

    Args:
        vo: 包含旧年份和新年份的VO对象
        service: 注入的服务实例

    Returns:
        计算结果
    """
    try:
        logger.info(f"开始计算等效分，旧年份: {vo.year_old}，新年份: {vo.year_new}")

        # 调用服务层方法
        result = await service.calculate_equivalent_scores(vo.year_old, vo.year_new)

        # 返回成功响应
        return GatewayResponse(
            code="0",
            message="等效分计算完成",
            data=CalculationResponseVO(
                message=result.get("message", "等效分计算完成"),
                processed_count=result.get("processed_count", 0),
            ),
        )
    except Exception as e:
        logger.exception(f"计算等效分时发生异常: {str(e)}")
        # 返回错误响应
        return GatewayResponse(
            code="500", message=f"计算等效分时发生异常: {str(e)}", debug=str(e)
        )
