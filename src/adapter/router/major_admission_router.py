from fastapi import APIRouter, Depends
from lucas_common_components.logging import setup_logger

from src.adapter.vo.major_admission_vo import (
    CollectByYearVO,
    CollectResultVO,
)
from src.adapter.vo.gateway_response import GatewayResponse
from src.app.major_admission_service import MajorAdmissionService
from src.infrastructure.rpc.gugudata_client import GuguDataClient
from src.exe.command.SchoolMajorCutoffCommandExe import SchoolMajorCutoffCommandExe

# 创建logger
logger = setup_logger(__name__)

# 创建路由
router = APIRouter(
    prefix="/api/major-admission",
    tags=["专业录取分数线数据"],
)


# 依赖注入：获取MajorAdmissionService实例
def get_major_admission_service():
    # 创建RPC客户端
    gugudata_client = GuguDataClient()
    # 创建命令执行器
    command_exe = SchoolMajorCutoffCommandExe()
    # 创建服务，同时注入两个依赖
    return MajorAdmissionService(gugudata_client, command_exe)


@router.post(
    "/collect-by-year",
    response_model=GatewayResponse[CollectResultVO],
    summary="按年份采集全国高校专业录取分数线数据",
    description="按省份采集指定年份的全国高校专业录取分数线数据（无断点续传功能）",
)
async def collect_by_year(
    vo: CollectByYearVO,
    service: MajorAdmissionService = Depends(get_major_admission_service),
):
    """
    按年份采集全国高校专业录取分数线数据

    Args:
        vo: 包含年份等参数的VO对象
        service: 注入的服务实例

    Returns:
        采集结果
    """
    try:
        for year in vo.years:
            logger.info(f"开始采集{year}年数据，批量大小: {vo.batch_size}")

            # 调用服务层方法
            await service.collect_all_data_by_province_no_checkpoint(
                year=year, batch_size=vo.batch_size, max_workers=vo.max_workers
            )

        # 返回成功响应
        return GatewayResponse(
            code="0",
            message="数据采集完成",
        )
    except Exception as e:
        logger.exception(f"采集数据时发生异常: {str(e)}")
        # 返回错误响应
        return GatewayResponse(
            code="500", message=f"采集数据时发生异常: {str(e)}", debug=str(e)
        )
