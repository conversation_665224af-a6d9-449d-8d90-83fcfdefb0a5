# -*- coding: utf-8 -*-
"""
TrustCall 结构化信息提取与对话图示例
=================================================

演示如何利用 TrustCall 进行：
1. 非结构化文本的结构化提取
2. 基于 LangGraph 的多轮对话工具调用
"""

import os
import traceback
import operator
from datetime import datetime
from typing import List, Optional, Union

import pytz
from fastapi import APIRouter, HTTPException, Body
from lucas_common_components.logging import setup_logger
from pydantic import BaseModel, Field, validator
from trustcall import create_extractor
from typing_extensions import Annotated, TypedDict
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import ToolNode, tools_condition

from src.infrastructure.llm.llm_client import create_llm

logger = setup_logger(__name__)
router = APIRouter()

# 1) 提取器部分 - 定义Schema ------------------------------------------------


class AnnualReportFacts(BaseModel):
    """摘选年报关键指标"""

    revenue: float = Field(..., description="本年度营收，单位：亿元")
    net_income: float = Field(..., description="本年度净利润，单位：亿元")
    employees: int = Field(..., description="期末员工总数")
    ceo: Optional[str] = Field(None, description="首席执行官姓名")


class UserInfo(BaseModel):
    """用户信息"""

    name: str = Field(..., description="用户全名")
    age: int = Field(..., description="用户年龄")
    preferences: List[str] = Field(..., description="用户偏好列表")


class ExtractRequest(BaseModel):
    """提取请求"""

    text: str = Field(..., description="待提取的文本内容")


class ExtractResponse(BaseModel):
    """提取响应"""

    type: str = Field(..., description="提取的信息类型：annual_report 或 user_info")
    data: Union[AnnualReportFacts, UserInfo] = Field(
        ..., description="提取的结构化信息"
    )


# 创建提取器
llm = create_llm(
    model_name=os.getenv("QWEN_32_B"),
    api_key=os.getenv("ALIBABA_API_KEY"),
    api_base=os.getenv("ALIBABA_BASE_URL"),
)

# 创建通用提取器
extractor = create_extractor(
    llm,
    tools=[AnnualReportFacts, UserInfo],
    tool_choice="any",  # 允许模型自动选择工具
)

# 2) 对话图部分 - 模型定义与工具函数 -----------------------------------------


class Preferences(BaseModel):
    """用户食物偏好"""

    foods: List[str] = Field(description="最喜欢的食物列表")

    @validator("foods")
    def at_least_three_foods(cls, v):
        if len(v) < 3:
            raise ValueError("必须至少包含三种喜欢的食物")
        return v


def save_user_information(preferences: Preferences):
    """保存用户信息到数据库。"""
    logger.info(f"保存用户偏好: {preferences}")
    return "用户信息已保存"


def lookup_time(tz: str) -> str:
    """查询指定时区的当前时间。"""
    try:
        # 将时区字符串转换为时区对象
        timezone = pytz.timezone(tz)
        # 获取指定时区的当前时间
        tm = datetime.now(timezone)
        return f"{tz}的当前时间是 {tm.strftime('%H:%M:%S')}"
    except pytz.UnknownTimeZoneError:
        return f"未知时区: {tz}"


class ConversationState(TypedDict):
    messages: Annotated[list, operator.add]
    thread_id: str


class ChatRequest(BaseModel):
    """聊天请求"""

    message: str = Field(..., description="用户消息")
    thread_id: str = Field(..., description="对话线程ID")


class ChatResponse(BaseModel):
    """聊天响应"""

    content: str = Field(..., description="助手回复内容")
    thread_id: str = Field(..., description="对话线程ID")


# 创建和存储对话图实例的字典
conversation_graphs = {}


def create_conversation_graph():
    """创建新的对话图实例"""
    # 使用项目中已创建的LLM实例

    # 创建使用trustcall的agent
    agent = create_extractor(llm, tools=[save_user_information, lookup_time])

    # 构建图
    builder = StateGraph(ConversationState)
    builder.add_node("agent", agent)
    builder.add_node("tools", ToolNode([save_user_information, lookup_time]))
    builder.add_edge("tools", "agent")
    builder.add_edge(START, "agent")
    builder.add_conditional_edges("agent", tools_condition)

    # 编译图
    graph = builder.compile(checkpointer=MemorySaver())
    return graph


# 3) API 路由 - 信息提取 ---------------------------------------------------


@router.post("/extract/extract", response_model=ExtractResponse, tags=["extract"])
async def extract_info(request: ExtractRequest):
    """
    从文本中提取结构化信息，自动识别是年报信息还是用户信息

    Args:
        request: 包含待提取文本的请求体

    Returns:
        提取的结构化信息，包含类型和数据
    """
    try:
        result = extractor.invoke(
            {
                "messages": [
                    {
                        "role": "system",
                        "content": """你是一个专业的信息提取助手。请根据文本内容自动判断是年报信息还是用户信息：
                    1. 如果文本包含营收、净利润、员工数等财务指标，则提取为年报信息
                    2. 如果文本包含姓名、年龄、个人偏好等信息，则提取为用户信息
                    请确保提取的信息符合相应的Schema要求。""",
                    },
                    {"role": "user", "content": request.text},
                ]
            }
        )
        logger.info(f"Extractor result: {result}")

        # 从结果中获取第一个响应
        if result["responses"] and len(result["responses"]) > 0:
            data = result["responses"][0]
            # 根据返回的数据类型确定响应类型
            if isinstance(data, AnnualReportFacts):
                return ExtractResponse(type="annual_report", data=data)
            elif isinstance(data, UserInfo):
                return ExtractResponse(type="user_info", data=data)
            else:
                raise ValueError(f"未知的响应类型: {type(data)}")
        else:
            raise ValueError("未能提取到有效信息")

    except Exception as e:
        error_msg = f"提取失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e


# 4) API 路由 - 对话系统 --------------------------------------------------


@router.post("/chat/conversation", response_model=ChatResponse, tags=["chat"])
async def chat(request: ChatRequest):
    """
    处理用户对话请求

    Args:
        request: 包含用户消息和线程ID的请求

    Returns:
        助手的回复
    """
    # 处理Body参数
    if request is None:
        request = Body(...)

    try:
        thread_id = request.thread_id

        # 检查是否存在此对话，如果不存在则创建
        if thread_id not in conversation_graphs:
            conversation_graphs[thread_id] = create_conversation_graph()

        # 获取对话图实例
        graph = conversation_graphs[thread_id]

        # 调用对话图
        config = {"configurable": {"thread_id": thread_id}}
        result = graph.invoke(
            {
                "messages": [{"role": "user", "content": request.message}],
                "thread_id": thread_id,
            },
            config,
        )

        # 获取助手回复
        last_message = result["messages"][-1]
        content = (
            last_message.content
            if hasattr(last_message, "content")
            else str(last_message)
        )

        return ChatResponse(content=content, thread_id=thread_id)

    except Exception as e:
        error_msg = f"对话处理失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e


# 5) 示例用法 ------------------------------------------------------------

"""
# 提取器示例：提取年报信息
curl -X POST "http://localhost:3000/extract/extract" \
     -H "Content-Type: application/json" \
     -d '{
         "text": "2024年，公司实现营业收入1583.9亿元，同比增长12.3%；归属于上市公司股东的净利润423.7亿元，同比提升8.6%。截至2024年12月31日，公司共有员工6,850人，其中研发人员占比34%。公司首席执行官John Q. Public在报告中表示，……"
     }'

# 提取器示例：提取用户信息
curl -X POST "http://localhost:3000/extract/extract" \
     -H "Content-Type: application/json" \
     -d '{
         "text": "我叫张三，今年28岁。我喜欢打篮球、看电影和听音乐。"
     }'

# 对话系统示例：发起对话
curl -X POST "http://localhost:3000/chat/conversation" \
     -H "Content-Type: application/json" \
     -d '{
         "message": "你好！",
         "thread_id": "12345"
     }'

# 对话系统示例：查询时间
curl -X POST "http://localhost:3000/chat/conversation" \
     -H "Content-Type: application/json" \
     -d '{
         "message": "现在北京时间是几点？",
         "thread_id": "12345"
     }'
     
# 对话系统示例：保存偏好
curl -X POST "http://localhost:3000/chat/conversation" \
     -H "Content-Type: application/json" \
     -d '{
         "message": "我喜欢吃西红柿、黄瓜、茄子、青椒和土豆",
         "thread_id": "12345"
     }'
"""
