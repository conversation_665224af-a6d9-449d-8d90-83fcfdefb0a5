from fastapi import APIRouter
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient

router = APIRouter(prefix="/health", tags=["健康检查"])


@router.get("/")
async def health_check():
    """基本健康检查"""
    return {"status": "ok", "message": "服务运行正常"}


@router.get("/db-pool")
async def db_pool_status():
    """PostgreSQL 连接池状态检查"""
    try:
        stats = await PGCheckpointClient.get_pool_stats()
        return {
            "status": "ok",
            "pool_stats": stats
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "pool_stats": None
        } 