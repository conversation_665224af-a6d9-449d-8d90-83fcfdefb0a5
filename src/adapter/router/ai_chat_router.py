from typing import Dict, List
import uuid

from fastapi import APIRouter, Depends
from fastapi.responses import Response
from lucas_common_components.logging import setup_logger
from lucas_common_components.middlewares import UniqTenantUserInfo
from lucas_common_components.middlewares.model.models import get_current_user
from sse_starlette import EventSourceResponse, ServerSentEvent

from src.adapter.vo.ai_chat_model import (
    CreateChatArgVO,
    CreateChatResultVO,
    ChatInputVO,
    ConversationVO,
    ConversationListResultVO,
    AiMessageHistoryArgVO,
    AiMessageHistoryResultVO,
)
from src.adapter.vo.gateway_response import GatewayResponse
from src.app.ai_chat_service import chat_handler, chat_process_action_handler
from src.app.ai_conversation_service import AIConversationService
from src.exe.query.AIMessageQueryExe import AIMessageQueryService
from src.utils.report_utils import convert_markdown_to_pdf
from src.infrastructure.db.crud.ai.ReportTaskRepository import ReportTaskRepository
from src.exe.query.JobProspectQueryExe import JobProspectQueryExe

logger = setup_logger(name=__name__, level="DEBUG")
router = APIRouter(
    prefix="/aichat",
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)


@router.post("/create", summary="创建会话")
async def create(
    request: CreateChatArgVO, context: UniqTenantUserInfo = Depends(get_current_user)
) -> GatewayResponse[CreateChatResultVO]:
    # 检查用户ID是否为空，为空说明是未登录用户
    if not context.uniqUserId:
        # 为未登录用户生成一个随机的conversationId
        conversation_id = f"guest_{uuid.uuid4().hex[:8]}"
        logger.info(f"未登录用户创建会话: {conversation_id}")
        return GatewayResponse(data=CreateChatResultVO(conversation_id=conversation_id))

    # 已登录用户正常处理流程
    conversation_id = await AIConversationService.create_conversation(request, context)
    if not conversation_id:
        return GatewayResponse(code="-1", message="创建会话失败")
    return GatewayResponse(data=CreateChatResultVO(conversation_id=conversation_id))


@router.post("/chat", summary="会话接口")
async def chat(
    request: ChatInputVO, context: UniqTenantUserInfo = Depends(get_current_user)
) -> EventSourceResponse:
    logger.info(f"context:{context}")

    # 检查是否为未登录用户
    if not context.uniqUserId:
        # 对于未登录用户，使用OpenSearch搜索作为回复
        logger.info("未登录用户会话，使用OpenSearch搜索")

        # 获取用户消息，处理可能的空情况
        user_message = ""
        if request.messages:
            user_message = request.messages[0].text or ""

        # 使用JobProspectQueryExe的结果作为响应
        async def search_stream():
            from src.domain.model.ai_chat_model import AiChatResultBO, CustomBO

            message_id = f"search_{uuid.uuid4().hex[:8]}"

            async for content in JobProspectQueryExe.search_job_prospect(
                user_message, conversation_id=request.conversation_id
            ):
                if content:
                    # 使用与chat_handler相同的BO模型构建响应
                    chat_result = AiChatResultBO(
                        text=content,
                        custom=CustomBO(message_type="text", message_id=message_id),
                    )
                    # 与chat_handler保持一致的输出格式
                    yield chat_result.model_dump_json(exclude_none=True)

        return EventSourceResponse(
            content=search_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
            },
            ping_message_factory=lambda: ServerSentEvent(
                event="ping", retry=1000, data=""
            ),
        )

    # 已登录用户正常处理流程
    return EventSourceResponse(
        content=chat_handler(request, context.uniqUserId),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
        ping_message_factory=lambda: ServerSentEvent(event="ping", retry=1000, data=""),
    )


@router.post("/query_progress", summary="报告query_progress接口")
async def query_progress(
    request: ChatInputVO, context: UniqTenantUserInfo = Depends(get_current_user)
) -> EventSourceResponse:
    logger.info(f"context:{context}")
    # 添加mock数据逻辑 - 检查是否有请求引导式对话
    return EventSourceResponse(
        content=chat_process_action_handler(request, context.uniqUserId),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
        ping_message_factory=lambda: ServerSentEvent(event="ping", retry=1000, data=""),
    )

@router.post("/query_task_status", summary="报告query_progress接口")
async def query_task_status(
    request: ChatInputVO, context: UniqTenantUserInfo = Depends(get_current_user)
) -> EventSourceResponse:
    logger.info(f"context:{context}")
    # 添加mock数据逻辑 - 检查是否有请求引导式对话
    return EventSourceResponse(
        content=chat_process_action_handler(request, context.uniqUserId),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
        ping_message_factory=lambda: ServerSentEvent(event="ping", retry=1000, data=""),
    )


@router.post(path="/conversationList", summary="会话列表")
async def conversation_list(
    context: UniqTenantUserInfo = Depends(get_current_user),
) -> GatewayResponse[Dict[str, List[ConversationVO]]]:
    conversations = await AIConversationService.get_conversations_by_user(context, sort_by="created_at")
    result = ConversationListResultVO(list=conversations)
    grouped_conversations = result.get_grouped_conversations()
    return GatewayResponse(data=grouped_conversations)


@router.post(path="/flatConversationList", summary="平铺会话列表")
async def flat_conversation_list(
    context: UniqTenantUserInfo = Depends(get_current_user),
) -> GatewayResponse[List[ConversationVO]]:
    conversations = await AIConversationService.get_conversations_by_user(context, sort_by="updated_at")
    return GatewayResponse(data=conversations)

@router.post(path="/aiMessageHistory", summary="获取会话消息历史")
async def aiMessageHistory(
    request: AiMessageHistoryArgVO,
    context: UniqTenantUserInfo = Depends(get_current_user),
) -> GatewayResponse[AiMessageHistoryResultVO]:
    result = await AIMessageQueryService.get_message_history(request.conversation_id)
    return GatewayResponse(data=result)


@router.get(path="/generate_report/download_pdf/{task_id}", summary="下载报告PDF")
async def download_report_pdf(
    task_id: str,
    context: UniqTenantUserInfo = Depends(get_current_user),
):
    """下载报告PDF文件"""
    try:
        logger.info(f"下载报告PDF: {task_id}")

        # 从数据库获取任务信息
        task = await ReportTaskRepository.get_by_task_id(task_id)

        if not task:
            return Response(
                content="任务不存在", status_code=404, media_type="text/plain"
            )

        if task.status != "completed":
            return Response(
                content="任务尚未完成或执行失败",
                status_code=400,
                media_type="text/plain",
            )

        if not task.report_content:
            return Response(
                content="报告内容不存在", status_code=404, media_type="text/plain"
            )

        # 从任务结果中提取 markdown 内容
        markdown_content = task.report_content
        if not markdown_content:
            return Response(
                content="报告内容为空", status_code=404, media_type="text/plain"
            )

        # 转换 Markdown 到 PDF
        pdf_content = await convert_markdown_to_pdf(markdown_content, task.task_name)

        # 处理中文文件名，使用 URL 编码
        import urllib.parse

        safe_filename = urllib.parse.quote(f"{task.task_name}.pdf", safe="")

        # 返回 PDF 响应
        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{safe_filename}"
            },
        )

    except Exception as e:
        logger.error(f"下载报告PDF失败: {str(e)}", exc_info=True)
        return Response(
            content=f"服务器内部错误: {str(e)}",
            status_code=500,
            media_type="text/plain",
        )
