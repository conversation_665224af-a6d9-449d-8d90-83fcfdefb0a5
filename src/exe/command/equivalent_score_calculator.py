from typing import Dict
import time
from datetime import datetime


from lucas_common_components.logging import setup_logger

from src.exe.query.school_major_cutoff_query_exe import SchoolMajorCutoffQueryExe
from src.infrastructure.db.crud.ai.ExamScoreContextualDataRepository import (
    ExamScoreContextualDataRepository,
)
from src.infrastructure.db.crud.ai.EquivalentScorePredictionRepository import (
    EquivalentScorePredictionRepository,
)


class EquivalentScoreCalculator:
    """等效分计算执行器"""

    def __init__(self):
        """
        初始化等效分计算执行器
        """
        # 配置日志
        self.logger = setup_logger(__name__)

        # 直接使用静态类引用
        self.school_major_repo = SchoolMajorCutoffQueryExe
        self.score_table_repo = ExamScoreContextualDataRepository
        self.prediction_repo = EquivalentScorePredictionRepository

        # 定义科目组合类别
        self.subject_selections = ["历史类", "文科", "物理类", "理科", "综合"]

        # 定义等级段及其比例 - 按照正确的比例更新
        self.grade_segments = [
            {"type": "A", "ratio": 0.03},
            {"type": "B+", "ratio": 0.07},
            {"type": "B", "ratio": 0.16},
            {"type": "C+", "ratio": 0.24},
            {"type": "C", "ratio": 0.24},
            {"type": "D+", "ratio": 0.16},
            {"type": "D", "ratio": 0.07},
            {"type": "E", "ratio": 0.03},
        ]

        self.logger.info("EquivalentScoreCalculator初始化完成")

    async def calculate_all_equivalent_scores(
        self, year_old: int, year_new: int
    ) -> Dict:
        """批量计算所有学校专业的等效分"""
        start_time = time.time()
        start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.logger.info(
            f"开始批量计算从{year_old}年到{year_new}年的等效分，开始时间：{start_datetime}"
        )
        processed_count = 0

        # 1. 获取所有学校
        self.logger.info("正在获取所有学校...")
        schools = await self.school_major_repo.get_all_schools_by_year(year_old)
        self.logger.info(f"获取到 {len(schools)} 所学校")

        # 2. 遍历每个学校
        for school_idx, school in enumerate(schools):
            school_name = school["school_name"]
            school_uuid = school["school_uuid"]
            self.logger.info(
                f"处理学校 [{school_idx + 1}/{len(schools)}]: {school_name} (UUID: {school_uuid})"
            )

            # 3. 获取该学校所有专业
            self.logger.debug(f"获取 {school_name} 的所有专业...")
            majors = await self.school_major_repo.get_school_majors_by_year(
                year_old, school_uuid
            )
            self.logger.info(f"获取到 {len(majors)} 个专业")

            for major_idx, major in enumerate(majors):
                major_name = major["major_name"]
                self.logger.info(
                    f"处理专业 [{major_idx + 1}/{len(majors)}]: {major_name}"
                )

                # 4. 获取所有招生省份
                self.logger.info(f"获取 {school_name}-{major_name} 招生省份...")
                provinces = await self.school_major_repo.get_major_provinces_data(
                    year_old, school_uuid, major_name
                )
                self.logger.info(f"获取到 {len(provinces)} 个招生省份")

                # 5. 构建等效分结果数据结构
                province_scores_data = []

                for province_idx, province in enumerate(provinces):
                    province_name = province["province_name"]
                    self.logger.debug(
                        f"处理省份 [{province_idx + 1}/{len(provinces)}]: {province_name}"
                    )

                    # 6. 该省份下各科目组合的分数
                    subject_scores = []

                    for subject_selection in self.subject_selections:
                        try:
                            # 7. 查询特定学校专业在特定省份特定科目组合下的位次
                            self.logger.debug(
                                f"查询 {school_name}-{major_name}-{province_name}-{subject_selection} 的位次"
                            )
                            rank_data = await self.school_major_repo.get_cutoff_rank(
                                year_old,
                                school_uuid,
                                major_name,
                                province_name,
                                subject_selection,
                            )

                            if rank_data and "rank" in rank_data and rank_data["rank"]:
                                try:
                                    # 尝试将rank转换为整数
                                    old_rank = int(rank_data["rank"])
                                    self.logger.debug(f"获取到位次: {old_rank}")

                                    # 8. 计算等效分
                                    score = (
                                        await self.calculate_province_equivalent_score(
                                            year_old,
                                            year_new,
                                            province_name,
                                            old_rank,
                                            subject_selection,
                                            school_name,
                                            major_name,
                                        )
                                    )
                                    self.logger.debug(f"计算得到等效分: {score}分")

                                    # 9. 添加到科目分数列表
                                    subject_scores.append(
                                        {
                                            "subject_selection": subject_selection,
                                            "score": score,
                                        }
                                    )
                                except (ValueError, TypeError) as e:
                                    self.logger.error(
                                        f"位次值 '{rank_data['rank']}' 无法转换为整数: {str(e)}"
                                    )
                            else:
                                self.logger.debug(
                                    f"{school_name}-{major_name}-{province_name}-{subject_selection} 未找到位次数据"
                                )
                        except Exception as e:
                            self.logger.error(
                                f"处理 {school_name}-{major_name}-{province_name}-{subject_selection} 出错: {str(e)}"
                            )

                    # 10. 只有有分数时才添加该省份数据
                    if subject_scores:
                        province_scores_data.append(
                            {"province": province_name, "scores": subject_scores}
                        )

                # 11. 保存整体结果
                if province_scores_data:
                    # 打印详细的数据结构
                    self.logger.info("\n" + "=" * 40)
                    self.logger.info(f"学校: {school_name}")
                    self.logger.info(f"专业: {major_name}")
                    self.logger.info(f"预测年份: {year_new}")
                    self.logger.info(
                        f"省份等效分数据: {len(province_scores_data)}个省份"
                    )

                    # 保存数据
                    self.logger.info(
                        f"保存 {school_name}-{major_name} 等效分数据到存储..."
                    )
                    await self.prediction_repo.save(
                        school_name, major_name, year_new, province_scores_data
                    )
                    processed_count += 1
                    self.logger.info(f"成功保存第 {processed_count} 条等效分数据")

        self.logger.info(f"等效分计算完成，共处理了 {processed_count} 个学校专业")
        end_time = time.time()
        end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        elapsed_time = end_time - start_time
        self.logger.info(f"结束批量计算时间: {end_datetime}")
        self.logger.info(
            f"批量计算总耗时: {elapsed_time:.2f}秒 ({elapsed_time / 60:.2f}分钟)"
        )
        return {"processed_count": processed_count}

    async def calculate_province_equivalent_score(
        self,
        year_old: int,
        year_new: int,
        province: str,
        old_rank: int,
        subject_selection: str,
        school_name: str = "",
        major_name: str = "",  # 添加学校和专业名称作为可选参数
    ) -> int:
        """
        计算特定省份特定科目组合的等效分，并输出详细日志

        Args:
            year_old: 旧年份
            year_new: 新年份
            province: 省份
            old_rank: 旧位次
            subject_selection: 科目组合
            school_name: 学校名称
            major_name: 专业名称

        Returns:
            int: 等效分数
        """
        # 添加开始分隔线
        self.logger.info(f"{'=' * 50}")

        # 简化计算信息标识
        target_info = f"{school_name}-{major_name} | {province}-{subject_selection}"
        self.logger.info(f"开始计算: {target_info} | {year_old}年位次: {old_rank}")

        # 1. 获取旧年份一分一段表总人数
        old_total_candidates = await self.score_table_repo.get_total_candidates(
            year_old, province, subject_selection
        )

        # 2. 确定旧年份位次所在等级段
        old_segment = self._determine_grade_segment(old_rank, old_total_candidates)

        # 3. 计算等级内相对位次p
        p = (
            (old_rank - old_segment["start_rank"])
            / (old_segment["end_rank"] - old_segment["start_rank"])
            if old_segment["end_rank"] > old_segment["start_rank"]
            else 0
        )

        # 4. 获取新年份一分一段表总人数
        new_total_candidates = await self.score_table_repo.get_total_candidates(
            year_new, province, subject_selection
        )

        # 5. 计算新年份对应等级段的位次范围
        new_segment = self._calculate_grade_segment_range(
            old_segment["grade_type"], new_total_candidates
        )

        # 6. 映射到新年份等级段
        new_rank = int(
            new_segment["start_rank"]
            + p * (new_segment["end_rank"] - new_segment["start_rank"])
        )

        # 整合计算过程信息为一条日志
        self.logger.info(
            f"计算过程: 等级段:{old_segment['grade_type']} | "
            f"原始位次:{old_rank}/{old_total_candidates} | "
            f"相对位次:{p:.4f} | "
            f"新位次:{new_rank}/{new_total_candidates}"
        )

        # 7. 查找对应分数
        score = await self.score_table_repo.get_score_by_rank(
            year_new, province, new_rank, subject_selection
        )

        # 简化最终结果输出
        self.logger.info(
            f"计算结果: {target_info} | {year_old}→{year_new}年等效分: {score}分"
        )

        # 添加结束分隔线
        self.logger.info(f"{'=' * 50}")

        return score

    def _determine_grade_segment(self, rank: int, total_candidates: int) -> Dict:
        """
        根据位次和总人数确定等级段

        Args:
            rank: 位次
            total_candidates: 总考生人数

        Returns:
            Dict: 包含等级类型和位次范围的字典
        """
        cumulative_ratio = 0
        prev_end_rank = 0

        for segment in self.grade_segments:
            segment_ratio = segment["ratio"]
            cumulative_ratio += segment_ratio

            # 计算当前等级段的结束位次
            end_rank = int(total_candidates * cumulative_ratio)

            # 如果位次在当前等级段范围内
            if rank <= end_rank:
                return {
                    "grade_type": segment["type"],
                    "start_rank": prev_end_rank + 1,
                    "end_rank": end_rank,
                }

            prev_end_rank = end_rank

        # 如果位次超出所有等级段，归入最后一个等级段
        return {
            "grade_type": self.grade_segments[-1]["type"],
            "start_rank": prev_end_rank + 1,
            "end_rank": total_candidates,
        }

    def _calculate_grade_segment_range(
        self, grade_type: str, total_candidates: int
    ) -> Dict:
        """
        计算特定等级在给定总人数下的位次范围

        Args:
            grade_type: 等级类型
            total_candidates: 总考生人数

        Returns:
            Dict: 包含位次范围的字典
        """
        cumulative_ratio = 0
        prev_end_rank = 0

        for segment in self.grade_segments:
            segment_ratio = segment["ratio"]
            cumulative_ratio += segment_ratio

            # 计算当前等级段的结束位次
            end_rank = int(total_candidates * cumulative_ratio)

            if segment["type"] == grade_type:
                return {"start_rank": prev_end_rank + 1, "end_rank": end_rank}

            prev_end_rank = end_rank

        # 如果找不到对应等级段，返回最后一个等级段
        return {"start_rank": prev_end_rank + 1, "end_rank": total_candidates}
