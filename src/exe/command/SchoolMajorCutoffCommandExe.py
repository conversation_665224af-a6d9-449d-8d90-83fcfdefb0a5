from typing import Dict, Any, List

from lucas_common_components.logging import setup_logger


from src.exe.query.school_major_cutoff_query_exe import SchoolMajorCutoffQueryExe
from src.infrastructure.db.models.ai.SchoolMajorCutoff import SchoolMajorCutoff

logger = setup_logger(__name__)


class SchoolMajorCutoffCommandExe:
    """
    高考专业录取分数线命令执行器，负责数据库操作和数据转换
    """

    def __init__(self):
        """初始化命令执行器"""
        # 只保留与数据库相关的初始化
        pass

    async def save_batch_data_bulk(self, api_data_list: List[Dict[str, Any]]) -> int:
        """
        使用批量插入方式保存数据到数据库

        Args:
            api_data_list: API返回的数据列表

        Returns:
            保存的记录数量
        """
        to_insert = []

        for api_data in api_data_list:
            try:
                # 直接处理API数据字段
                # 处理可能为空的分数字段
                highest_score = api_data.get("HighestScore")
                if highest_score is None or highest_score == "-":
                    highest_score = None

                average_score = api_data.get("AverageScore")
                if average_score is None or average_score == "-":
                    average_score = None

                lowest_section = api_data.get("LowestSection")
                if lowest_section is None or lowest_section == "-":
                    lowest_section = None

                pro_score = api_data.get("ProScore")
                if pro_score is None or pro_score == "-":
                    pro_score = None

                # 处理最低分
                lowest_score = api_data.get("LowestScore")
                if lowest_score is None or lowest_score == "-":
                    lowest_score = None

                # 创建数据字典，符合数据库模型格式
                score_data = {
                    "school_uuid": api_data.get("SchoolUUID"),
                    "school_name": api_data.get("SchoolName"),
                    "province_name": api_data.get("ProvinceName"),
                    "major_name": api_data.get("MajorName"),
                    "major_code": str(api_data.get("MajorCode"))
                    if api_data.get("MajorCode")
                    else None,
                    "major_standard_code": api_data.get("MajorStandardCode"),
                    "year": api_data.get("Year"),
                    "highest_score": highest_score,
                    "average_score": average_score,
                    "lowest_score": lowest_score,
                    "lowest_section": lowest_section,
                    "batch_name": api_data.get("BatchName"),
                    "type_name": api_data.get("TypeName"),
                    "pro_score": pro_score,
                    "subject_selection": api_data.get("subjectSelection"),
                }

                # 检查记录是否已存在
                existing = await SchoolMajorCutoffQueryExe.check_cutoff_exists(
                    school_uuid=score_data["school_uuid"],
                    province_name=score_data["province_name"],
                    major_name=score_data["major_name"],
                    year=score_data["year"],
                    batch_name=score_data["batch_name"],
                    type_name=score_data["type_name"],
                )

                if not existing:
                    # 将不存在的记录添加到批量插入列表
                    to_insert.append(score_data)
                # TODO 改成update
                else:
                    logger.debug(
                        f"记录已存在，跳过: {score_data['school_name']}/{score_data['major_name']}"
                    )
            except Exception as e:
                logger.error(f"处理数据项时出错: {str(e)}")

        # 批量插入数据
        saved_count = 0
        if to_insert:
            try:
                await self.bulk_create_from_dicts(to_insert)
                saved_count = len(to_insert)
                logger.info(f"批量插入成功，共保存 {saved_count} 条数据")
            except Exception as e:
                logger.error(f"批量插入数据时出错: {str(e)}")

        return saved_count

    @staticmethod
    async def save_cutoff(cutoff: SchoolMajorCutoff) -> SchoolMajorCutoff:
        """
        保存专业录取分数线记录

        Args:
            cutoff: 专业录取分数线对象

        Returns:
            保存后的专业录取分数线对象
        """
        await cutoff.save()
        return cutoff

    @staticmethod
    async def bulk_save_cutoffs(cutoffs: List[SchoolMajorCutoff]) -> List[SchoolMajorCutoff]:
        """
        批量保存专业录取分数线记录

        Args:
            cutoffs: 专业录取分数线对象列表

        Returns:
            保存后的专业录取分数线对象列表
        """
        for cutoff in cutoffs:
            await cutoff.save()
        return cutoffs

    @staticmethod
    async def create_from_dict(score_data: Dict[str, Any]) -> SchoolMajorCutoff:
        """
        从字典创建专业录取分数线记录

        Args:
            score_data: 专业录取分数线数据字典

        Returns:
            创建的专业录取分数线对象
        """
        return await SchoolMajorCutoff.create(**score_data)

    @staticmethod
    async def bulk_create_from_dicts(
        score_data_list: List[Dict[str, Any]],
    ) -> List[SchoolMajorCutoff]:
        """
        从字典列表批量创建专业录取分数线记录

        Args:
            score_data_list: 专业录取分数线数据字典列表

        Returns:
            创建的专业录取分数线对象列表
        """
        return await SchoolMajorCutoff.bulk_create(
            [SchoolMajorCutoff(**data) for data in score_data_list]
        )
