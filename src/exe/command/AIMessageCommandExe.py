from typing import Optional

from src.domain.model.ai_chat_model import CustomBO
from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository
from src.infrastructure.db.models.ai.AIMessage import AIMessage
from src.infrastructure.llm.llm_client import sf


class AIMessageCommandExe:
    @staticmethod
    async def save_ai_message_by_user(conversation_id, request):
        for message in request.messages:
            if message.text:
                message_id = int(sf.generate())
                content = {
                    "text": message.text,
                }
                message = {
                    "id": message_id,
                    "conversation_id": int(conversation_id),
                    "role": message.role or "user",
                    "message_type": "text",
                    "message_data": content,
                }
                await AIMessageRepository.create(message)

    @staticmethod
    async def save_text_message(text, conv_id, msg_id):
        if text:
            await AIMessageCommandExe.save_ai_message_by_ai(text, conv_id, msg_id)

    @staticmethod
    async def save_ai_message_by_ai_custom(
        conversation_id, prepare_ai_message_id, custom: CustomBO
    ):
        ai_message_id = int(prepare_ai_message_id)
        ai_message = await AIMessageRepository.get_by_id(ai_message_id)

        if ai_message:
            await AIMessageRepository.update(
                ai_message_id,
                {
                    "message_type": custom.message_type,
                    "message_status": custom.message_status,
                    "message_data": custom.message_data,
                },
            )
        else:
            ai_message_data = {
                "id": ai_message_id,
                "conversation_id": int(conversation_id),
                "role": "ai",
                "message_type": custom.message_type,
                "message_status": custom.message_status,
                "message_data": custom.message_data,
            }
            await AIMessageRepository.create(ai_message_data)

    @staticmethod
    async def save_ai_message_by_ai(
        ai_response_text, conversation_id, prepare_ai_message_id
    ):
        # 存储AI响应到数据库
        if ai_response_text:
            ai_message_id = int(prepare_ai_message_id)
            ai_message = await AIMessageRepository.get_by_id(ai_message_id)
            content = {
                "text": ai_response_text,
            }
            if ai_message:
                await AIMessageRepository.update(
                    ai_message_id,
                    {
                        "message_type": "text",
                        "message_data": content,
                    },
                )
            else:
                ai_message_data = {
                    "id": ai_message_id,
                    "conversation_id": int(conversation_id),
                    "role": "ai",
                    "message_type": "text",
                    "message_data": content,
                }
                await AIMessageRepository.create(ai_message_data)

    @staticmethod
    async def update(message_id: int, update_data: dict) -> Optional[AIMessage]:
        return await AIMessageRepository.update(
            message_id=message_id, update_data=update_data
        )
