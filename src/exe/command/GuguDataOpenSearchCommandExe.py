import asyncio
import time
import json
import uuid
from typing import Dict, Any, List, Optional

from lucas_common_components.logging import setup_logger

from src.exe.command.SchoolMajorCutoffCommandExe import SchoolMajorCutoffCommandExe
from src.infrastructure.opensearch import Config
from src.infrastructure.opensearch import AliyunOpenSearchLLMClient

logger = setup_logger(__name__)


class GuguDataOpenSearchCommandExe:
    """
    将GuguData数据导入到OpenSearch的执行器类
    """

    def __init__(self, opensearch_config: Dict[str, Any]):
        """
        初始化执行器

        Args:
            opensearch_config: OpenSearch配置信息，包含endpoint, access_key_id, access_key_secret, app_name等
        """
        self.school_major_exe = SchoolMajorCutoffCommandExe()

        # OpenSearch配置
        self.endpoint = opensearch_config.get(
            "endpoint", "opensearch-cn-shanghai.aliyuncs.com"
        )
        self.protocol = opensearch_config.get("protocol", "HTTP")
        self.access_key_id = opensearch_config.get("access_key_id")
        self.access_key_secret = opensearch_config.get("access_key_secret")
        self.app_name = opensearch_config.get("app_name")

        # 初始化OpenSearch客户端
        self.opensearch_client = self._init_opensearch_client()

        # 配置批处理参数
        self.batch_size = 20  # 每批处理的数据量

    def _init_opensearch_client(self) -> AliyunOpenSearchLLMClient:
        """
        初始化OpenSearch客户端

        Returns:
            AliyunOpenSearchLLMClient实例
        """
        config = Config(
            endpoint=self.endpoint,
            protocol=self.protocol,
            access_key_id=self.access_key_id,
            access_key_secret=self.access_key_secret,
        )
        return AliyunOpenSearchLLMClient(config)

    def _prepare_document(self, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备OpenSearch文档数据

        Args:
            api_data: GuguData API返回的数据项

        Returns:
            格式化的OpenSearch文档
        """
        # 从API数据中提取所需字段
        school_name = api_data.get("SchoolName", "")
        province_name = api_data.get("ProvinceName", "")
        major_name = api_data.get("MajorName", "")

        # 生成唯一ID - 使用UUID保证唯一性
        unique_id = str(uuid.uuid4())

        # 构建标题
        title = f"{school_name}_{province_name}_{major_name}"

        # 将原始数据转换为JSON字符串作为内容
        content = json.dumps(api_data, ensure_ascii=False)

        # 构建文档
        document = {
            "fields": {
                "id": unique_id,  # 使用唯一ID
                "title": title,
                "url": "",
                "content": content,
                "category": "",
                "timestamp": int(time.time() * 1000),  # 当前时间戳（毫秒）
                "score": 0.0,
            },
            "cmd": "ADD",
        }

        return document

    def _generate_curl_command(
        self, app_name: str, doc_content: List[Dict[str, Any]]
    ) -> str:
        """
        生成等效的curl命令

        Args:
            app_name: OpenSearch应用名称
            doc_content: 要推送的文档内容

        Returns:
            curl命令字符串
        """
        # 构建URL
        url = f"{self.protocol.lower()}://{self.endpoint}/v3/openapi/apps/{app_name}/actions/knowledge-bulk"

        # 构建请求体
        body = json.dumps(doc_content, ensure_ascii=False)

        # 构建curl命令
        curl_cmd = f"""curl -X POST "{url}" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Basic {self.access_key_id}:{self.access_key_secret}" \\
  -d '{body}'"""

        return curl_cmd

    async def push_to_opensearch(
        self, api_data_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        将GuguData数据推送到OpenSearch

        Args:
            api_data_list: GuguData API返回的数据列表

        Returns:
            推送结果
        """
        try:
            # 将API数据转换为OpenSearch文档格式
            documents = [self._prepare_document(item) for item in api_data_list]

            # 批量处理
            results = []
            curl_commands = []

            for i in range(0, len(documents), self.batch_size):
                batch = documents[i : i + self.batch_size]
                logger.info(
                    f"推送批次 {i // self.batch_size + 1}/{(len(documents) + self.batch_size - 1) // self.batch_size}，数据量: {len(batch)}"
                )

                try:
                    # 生成curl命令并记录
                    curl_cmd = self._generate_curl_command(self.app_name, batch)
                    curl_commands.append(curl_cmd)

                    # 实际发送请求
                    response = self.opensearch_client.docBulk(
                        app_name=self.app_name, doc_content=batch
                    )
                    results.append(response)
                    logger.info(f"批次 {i // self.batch_size + 1} 推送成功")

                    # 添加延迟，避免请求过于频繁
                    await asyncio.sleep(0.5)
                except Exception as e:
                    logger.error(f"批次 {i // self.batch_size + 1} 推送失败: {str(e)}")
                    raise

            # 打印curl命令示例
            logger.info(f"推送数据的curl命令示例 (共{len(curl_commands)}个批次):")
            logger.info(
                curl_commands[0][:1000] + "..."
                if len(curl_commands) > 0 and len(curl_commands[0]) > 1000
                else curl_commands[0]
                if len(curl_commands) > 0
                else "无curl命令"
            )

            # 将所有curl命令保存到文件
            curl_file_path = f"opensearch_curl_commands_{int(time.time())}.txt"
            with open(curl_file_path, "w", encoding="utf-8") as f:
                for i, cmd in enumerate(curl_commands):
                    f.write(f"# 批次 {i + 1}\n")
                    f.write(f"{cmd}\n\n")

            logger.info(f"所有curl命令已保存到文件: {curl_file_path}")

            return {
                "success": True,
                "message": "数据推送成功",
                "total_count": len(documents),
                "results": results,
                "curl_file": curl_file_path,
            }
        except Exception as e:
            logger.exception(f"数据推送异常: {str(e)}")
            return {
                "success": False,
                "message": f"数据推送异常: {str(e)}",
                "total_count": len(api_data_list),
                "saved_count": 0,
            }

    async def fetch_and_push(
        self,
        enrollprovince: Optional[str] = None,
        schoolname: Optional[str] = None,
        majorname: Optional[str] = None,
        year: Optional[int] = None,
        batch_size: int = 50,
    ) -> Dict[str, Any]:
        """
        直接从GuguData获取数据并推送到OpenSearch，不保存到数据库

        Args:
            enrollprovince: 招生省份
            schoolname: 学校名称
            majorname: 专业名称
            year: 年份
            batch_size: 每页记录数

        Returns:
            包含采集和推送结果的字典
        """
        try:
            # 构建请求参数
            params = {"pagesize": batch_size, "pageindex": 1}

            # 添加可选参数
            if enrollprovince:
                params["enrollprovince"] = enrollprovince
            if schoolname:
                params["schoolname"] = schoolname
            if majorname:
                params["majorname"] = majorname
            if year:
                params["year"] = year

            logger.info(f"开始从GuguData获取数据, 参数: {params}")

            # 直接使用fetch_data获取数据，不保存到数据库
            first_result = await self.school_major_exe.fetch_data(params)

            if first_result.get("DataStatus", {}).get("StatusCode") != 100:
                error_msg = first_result.get("DataStatus", {}).get(
                    "StatusDescription", "请求失败"
                )
                logger.error(f"首次请求失败: {error_msg}")
                return {
                    "success": False,
                    "message": error_msg,
                    "total_count": 0,
                    "saved_count": 0,
                }

            # 获取总数据量
            total_count = first_result.get("DataStatus", {}).get("DataTotalCount", 0)
            logger.info(f"总数据量: {total_count}")

            # 处理首页数据
            all_data = first_result.get("Data", [])

            # 计算总页数
            total_pages = (total_count + batch_size - 1) // batch_size

            # 如果有多页，继续获取
            tasks = []
            for page in range(2, total_pages + 1):
                params_copy = params.copy()
                params_copy["pageindex"] = page
                # 添加延迟以避免请求过于频繁
                await asyncio.sleep(0.3)
                tasks.append(self.school_major_exe.fetch_data(params_copy))

            # 等待所有请求完成
            if tasks:
                results = await asyncio.gather(*tasks)
                for result in results:
                    if result.get("DataStatus", {}).get("StatusCode") == 100:
                        page_data = result.get("Data", [])
                        all_data.extend(page_data)

            logger.info(f"获取完成，共获取 {len(all_data)} 条数据")

            # 如果没有数据，直接返回
            if not all_data:
                return {
                    "success": True,
                    "message": "没有数据需要推送",
                    "total_count": 0,
                    "saved_count": 0,
                }

            # 推送到OpenSearch
            logger.info("开始推送数据到OpenSearch...")
            push_result = await self.push_to_opensearch(all_data)

            return {
                "success": push_result.get("success", False),
                "message": push_result.get("message", ""),
                "fetch_result": {
                    "total_count": total_count,
                    "fetched_count": len(all_data),
                },
                "push_result": push_result,
            }
        except Exception as e:
            logger.exception(f"获取和推送数据异常: {str(e)}")
            return {
                "success": False,
                "message": f"获取和推送数据异常: {str(e)}",
            }

    async def fetch_school_and_push(
        self, school_name: str, year: int, batch_size: int = 100
    ) -> Dict[str, Any]:
        """
        根据学校名称直接获取数据并推送到OpenSearch，不保存到数据库

        Args:
            school_name: 学校名称
            year: 年份
            batch_size: 每页记录数

        Returns:
            包含采集和推送结果的字典
        """
        return await self.fetch_and_push(
            schoolname=school_name, year=year, batch_size=batch_size
        )
