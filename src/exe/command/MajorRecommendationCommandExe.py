import json
import os
import re
from typing import List

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from lucas_common_components.logging import setup_logger
from pydantic import Field, BaseModel

from src.infrastructure.llm.llm_client import async_create_llm

logger = setup_logger(name=__name__, level="DEBUG")

MAJOR_RECOMMENDATION_PROMPT = """
# 角色
你是一个高考志愿填报智能机器人，专注于根据用户信息和喜好推荐最适合的专业。

# 任务
基于以下信息，为用户推荐5-8个最适合的专业：

## 用户基础信息
{user_info}

## 就业愿景（职业倾向问卷回答）
{career_aspiration}

## 用户专业喜好
{user_preferences}

## 填报策略参考
{admission_strategy}

# 要求
1. 推荐5-8个专业，确保专业多样性
2. 可以推荐1-2个仅满足用户喜好但分数略有不足的专业（match_type为"preference"），并说明需要努力提高分数
3. 推荐专业列表根据与用户专业喜好相关程度排序，相关程度高的排在前列

# 输出格式
请以JSON格式输出推荐的专业列表：
{format_instructions}
"""


class RecommendMajor(BaseModel):
    id: int = Field(description="专业ID")

    name: str = Field(description="专业名称")

    # reason: str = Field(description="推荐理由")


class RecommendMajorResult(BaseModel):
    """专业推荐结果。
    Attributes:
        recommend_majors: 专业推荐列表
    """

    recommend_majors: List[RecommendMajor] = Field(description="专业推荐列表")


class MajorRecommendationCommandExe:
    """
    专业推荐命令执行器

    提供专业推荐相关的功能，包括：
    1. 根据用户分数和偏好获取可选专业列表
    2. 从用户职业倾向中提取专业偏好
    3. 生成专业推荐
    """

    @staticmethod
    def extract_user_preferences(career_aspiration: str) -> List[str]:
        """
        从用户职业倾向问卷回答中提取专业喜好

        Args:
            career_aspiration: 用户职业倾向问卷回答

        Returns:
            List[str]: 提取的专业喜好列表
        """
        if not career_aspiration or len(career_aspiration.strip()) == 0:
            return []

        # 专业领域关键词词库
        major_keywords = {
            "计算机": [
                "计算机",
                "软件",
                "编程",
                "人工智能",
                "大数据",
                "网络",
                "信息技术",
                "IT",
                "互联网",
            ],
            "医学": ["医学", "临床医学", "药学", "护理", "医疗", "康复", "健康"],
            "经济": ["经济", "金融", "会计", "财务", "投资", "贸易", "商业"],
            "教育": ["教育", "师范", "教学", "教师", "幼教"],
            "工程": ["工程", "土木", "建筑", "机械", "电气", "自动化", "材料"],
            "艺术": ["艺术", "设计", "音乐", "美术", "传媒", "影视", "动画"],
            "文学": ["文学", "汉语", "外语", "翻译", "新闻", "传播", "出版"],
            "法学": ["法学", "法律", "政治", "行政", "公共管理"],
            "理学": ["数学", "物理", "化学", "生物", "地理", "天文", "环境"],
            "农学": ["农学", "林学", "园艺", "畜牧", "水产", "食品"],
        }

        # 提取专业关键词
        preferences = []
        text = career_aspiration.lower()

        # 1. 直接匹配专业名称
        for category, keywords in major_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    # 检查是否有强烈喜好的表述
                    strong_preference = False
                    for phrase in [
                        "非常喜欢",
                        "特别喜欢",
                        "很喜欢",
                        "理想专业",
                        "梦想专业",
                        "感兴趣",
                    ]:
                        if phrase in text and keyword in text.split(phrase)[1][:20]:
                            strong_preference = True
                            break

                    # 如果是强烈喜好或者是主要类别，添加到偏好列表
                    if strong_preference or keyword == category:
                        if keyword not in preferences:
                            preferences.append(keyword)

        # 2. 使用正则表达式提取更复杂的专业表述
        # 匹配"喜欢xxx专业"或"对xxx专业感兴趣"等模式
        patterns = [
            r"喜欢(?:的|)\s*([^，。,.]*)\s*专业",
            r"对\s*([^，。,.]*)\s*专业感兴趣",
            r"想学\s*([^，。,.]*)\s*专业",
            r"选择\s*([^，。,.]*)\s*专业",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if (
                    match
                    and len(match.strip()) > 1
                    and match.strip() not in preferences
                ):
                    preferences.append(match.strip())

        # 限制返回的偏好数量，优先保留强烈喜好的专业
        return preferences[:5] if preferences else []

    @staticmethod
    async def generate_major_recommendations(user_data: dict) -> RecommendMajorResult:
        """
        生成专业推荐

        Args:
            user_data: 用户信息

        Returns:
            RecommendMajorResult: 推荐的专业列表，每个专业包含id、name和reason字段
        """

        user_preferences = (
            user_data.get("user_preferences")
            if user_data.get("user_preferences")
            else MajorRecommendationCommandExe.extract_user_preferences(
                user_data["career_aspiration"]
            )
        )
        # 准备LLM提示词
        result_parser = PydanticOutputParser(pydantic_object=RecommendMajorResult)

        # 调用LLM
        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_PLUS_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

        prompts = ChatPromptTemplate.from_messages(
            [SystemMessagePromptTemplate.from_template(MAJOR_RECOMMENDATION_PROMPT)]
        )
        chain = prompts | llm | result_parser

        # 解析LLM响应
        response = await chain.ainvoke(
            {
                "format_instructions": result_parser.get_format_instructions(),
                "user_info": user_data["user_info"],
                "career_aspiration": user_data["career_aspiration"],
                "admission_strategy": user_data["admission_strategy"],
                # "available_majors": json.dumps(user_data["available_majors"]),
                # "admission_scores": user_data["admission_scores"],
                "user_preferences": json.dumps(user_preferences),
            }
        )

        return response
