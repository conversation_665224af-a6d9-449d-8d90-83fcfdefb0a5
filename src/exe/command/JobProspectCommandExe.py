import requests
import json
import base64
from typing import Dict, List  # 引入 List
import os
import time  # 引入 time
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from pathlib import Path

from lucas_common_components.logging import setup_logger

# 使用绝对导入
from src.infrastructure.llm.EmbeddingClient import Embedding<PERSON>lient
from src.infrastructure.llm.llm_client import async_create_llm
from src.utils.snow_flake import Snowflake  # 确保路径正确

# 全局 snowflake 实例，worker_id 和 datacenter_id 可以根据实际情况调整
# 如果有多个 importer 实例或进程，需要确保它们使用的 worker_id 不同以避免冲突
snowflake = Snowflake(
    worker_id=1, datacenter_id=1
)  # 与之前的 importer 使用不同的 worker_id

logger = setup_logger(__name__)


class JobProspectCommandExe:
    """
    A class for importing prospect analysis documents (like output.md) into OpenSearch.
    Targets the 'yai_prospect' index.
    """

    # 配置信息从环境变量读取或作为参数传入，避免硬编码
    _OPENSEARCH_BASE_URL = "http://ha-cn-s1148yz8r03.ha.aliyuncs.com"
    _TARGET_INDEX_NAME = "yai_prospect"
    _access_username = "test_admin"
    _access_password = "FDVoWV+3z%1P%"

    # LLM 系统提示词保持不变，因为它用于通用的 Markdown 转换
    SYSTEM_PROMPT_MARKDOWN_CONVERSION = """
    # 角色
    你是一个专业的文档格式转换助手
    # 任务
    将输入的文本内容转换为规范的Markdown格式。要求：
    1. 保持原文的层级结构和内容完整性
    2. 使用适当的Markdown语法（标题、列表、表格等）
    3. 确保格式美观、易读
    4. 保持原文的表格结构，使用Markdown表格语法
    5. 对于特殊格式（如加粗、斜体等），使用对应的Markdown语法
    # 输入
    {text_content}
    """

    @staticmethod
    def _generate_headers() -> Dict[str, str]:
        """Generate headers with authorization"""
        # 使用从环境变量或配置中获取的凭证
        realm_str = f"{JobProspectCommandExe._access_username}:{JobProspectCommandExe._access_password}"
        authorization = base64.b64encode(realm_str.encode("utf-8")).decode("utf-8")

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Authorization": f"Basic {authorization}",
            "Connection": "keep-alive",
            "Content-Type": "application/json;charset=UTF-8",  # 明确UTF-8
            "User-Agent": "ProspectDocumentImporter/1.0",  # 更具体的User-Agent
            "X-Opensearch-Swift-PK-Field": "id",  # 添加主键字段
        }

    @staticmethod
    async def _convert_to_markdown(content: str) -> str:
        """
        Process the content through LLM to convert/refine to markdown format.
        """
        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(
                    JobProspectCommandExe.SYSTEM_PROMPT_MARKDOWN_CONVERSION
                ),
                (
                    "user",
                    "请仔细检查以下文本内容，并将其转换为结构清晰、语法正确的Markdown格式，特别注意保持表格和列表的原始结构和准确性：\n\n{text_content}",
                ),
            ]
        )

        llm = await async_create_llm(
            model_name=os.getenv("QWEN_32_B"),  # 从环境变量获取
            api_key=os.getenv("ALIBABA_API_KEY"),
            api_base=os.getenv("ALIBABA_BASE_URL"),
            temperature=0.1,  # 对于格式转换，较低的温度通常更好
        )

        chain = prompts | llm
        response = await chain.ainvoke({"text_content": content})
        return response.content.strip()  # 移除首尾空白

    @staticmethod
    async def import_report_document(
        file_path: str,
        report_title: str,
        report_source: str,
        publish_date: str,
        tags: List[str],
        summary: str,
        file_name_for_os: str,
        force_markdown_conversion: bool = False,
    ) -> bool:
        try:
            logger.info(f"--- Starting import for: {file_path} ---")
            # 1. Read the document content
            with open(file_path, "r", encoding="utf-8") as f:
                raw_content = f.read()

            # 2. Process content through LLM to convert/refine to markdown (if needed)
            if force_markdown_conversion:
                logger.info(
                    f"Processing content of '{file_path}' with LLM for Markdown conversion..."
                )
                markdown_content = await JobProspectCommandExe._convert_to_markdown(
                    raw_content
                )
            else:
                markdown_content = raw_content
                logger.info(
                    f"Using raw content of '{file_path}' as Markdown (LLM conversion skipped or not forced)."
                )

            # 3. Generate vector embedding
            logger.info("Generating vector embedding (ensure dimension is 128)...")
            vector_embedding = EmbeddingClient.get_embedding(markdown_content)
            if not isinstance(vector_embedding, list) or not all(
                isinstance(x, float) for x in vector_embedding
            ):
                raise ValueError(
                    "EmbeddingUtils.get_embedding did not return a list of floats."
                )

            # 4. Prepare the document for OpenSearch
            doc_id = str(snowflake.generate())
            logger.info(f"Generated document ID: {doc_id}")

            # 构建与 'yai_prospect' 索引字段匹配的 fields
            document_fields = {
                "id": doc_id,
                "report_title": report_title,
                "report_source": report_source,
                "publish_date": publish_date,
                "tags": tags,
                "summar1": summary,
                "content_markdown": markdown_content,
                "content_vector": vector_embedding,
                "last_imported_at": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "file_name": file_name_for_os,
            }

            # 5. Send the request
            bulk_url = f"{JobProspectCommandExe._OPENSEARCH_BASE_URL}/update/{JobProspectCommandExe._TARGET_INDEX_NAME}/actions/bulk"
            logger.info(f"Sending document to OpenSearch: {bulk_url}")

            # 构建请求体 - 使用更简单的格式
            request_body = [{"cmd": "add", "fields": document_fields}]

            # 打印请求体以便调试
            logger.info("Request body:")
            logger.info(json.dumps(request_body, ensure_ascii=False, indent=2))

            headers = JobProspectCommandExe._generate_headers()
            logger.info("Request headers:")
            logger.info(json.dumps(headers, indent=2))

            # 使用 requests 的 json 参数自动处理 JSON 序列化
            response = requests.post(
                bulk_url,
                headers=headers,
                json=request_body,  # 直接发送数组
                timeout=60,
            )

            # 打印响应内容
            logger.info(f"Response status code: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")
            logger.info(f"Response content: {response.text}")

            response.raise_for_status()

            # 检查 OpenSearch 返回的具体状态
            if response.status_code == 200:
                # 如果响应内容为空，认为导入成功
                if not response.text:
                    logger.info(
                        f"Successfully imported document: {report_title} (ID: {doc_id})"
                    )
                    return True

                try:
                    response_data = response.json()
                    if response_data.get("status") == "OK":
                        logger.info(
                            f"Successfully imported document: {report_title} (ID: {doc_id})"
                        )
                        return True
                    else:
                        logger.info(
                            f"Failed to import document '{report_title}'. OpenSearch Status: {response_data.get('status', 'N/A')}"
                        )
                        logger.info(f"Full Response: {response.text}")
                        return False
                except json.JSONDecodeError:
                    # 如果响应不是有效的 JSON，但状态码是 200，也认为导入成功
                    logger.info(
                        f"Successfully imported document: {report_title} (ID: {doc_id})"
                    )
                    return True
            else:
                logger.info(
                    f"Failed to import document '{report_title}'. Status code: {response.status_code}"
                )
                logger.info(f"Full Response: {response.text}")
                return False

        except requests.exceptions.HTTPError as http_err:
            logger.info(f"HTTP error during import of '{file_path}': {http_err}")
            if hasattr(http_err, "response") and http_err.response is not None:
                logger.info(f"Response status code: {http_err.response.status_code}")
                logger.info(f"Response headers: {dict(http_err.response.headers)}")
                logger.info(f"Response content: {http_err.response.text}")
            return False
        except ValueError as val_err:
            logger.info(f"Value error during import of '{file_path}': {val_err}")
            return False
        except Exception as e:
            logger.info(
                f"An unexpected error occurred importing '{file_path}': {str(e)}"
            )
            import traceback

            traceback.logger.info_exc()
            return False


# --- 调用示例 ---
async def run_output_md_import():
    # 加载环境变量 (确保在调用任何依赖环境变量的函数之前)
    from dotenv import load_dotenv

    load_dotenv()

    # 检查必要的环境变量是否已设置
    required_env_vars = [
        "OPENSEARCH_USERNAME",
        "OPENSEARCH_PASSWORD",
        "QWEN_32_B",
        "ALIBABA_API_KEY",
        "ALIBABA_BASE_URL",
    ]
    for var in required_env_vars:
        if not os.getenv(var):
            logger.info(f"Error: Environment variable '{var}' is not set.")
            return

    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    output_md_file = project_root / "ref" / "docs" / "output.md"

    if not output_md_file.exists():
        logger.info(f"Error: File '{output_md_file}' not found.")
        return

    # 准备导入所需的数据
    title = "中国大陆高校专业就业前景、行业趋势及人才需求分析报告"
    source = "内部研究与数据整理"
    p_date = "2025-05-16"
    doc_tags = ["就业前景", "行业趋势", "人才需求", "高校专业", "2025", "中国大陆"]
    doc_summary = "本报告旨在全面深入研究中国大陆地区当前及未来几年内各大学专业的就业前景、行业发展趋势及对人才的需求..."

    success = await JobProspectCommandExe.import_report_document(
        file_path=str(output_md_file),
        report_title=title,
        report_source=source,
        publish_date=p_date,
        tags=doc_tags,
        summary=doc_summary,
        file_name_for_os=output_md_file.name,
        force_markdown_conversion=False,
    )
    if success:
        logger.info("output.md imported successfully.")
    else:
        logger.info("Failed to import output.md.")


if __name__ == "__main__":
    import asyncio

    asyncio.run(run_output_md_import())
