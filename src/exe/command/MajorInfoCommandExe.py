import httpx
from typing import List, Dict, Any
from src.infrastructure.db.crud.ai.MajorInfoRepository import MajorInfoRepository
from src.utils.snow_flake import Snowflake
from lucas_common_components.logging import setup_logger

BASE_URL = "https://api.gugudata.com/metadata/ceemajor"
APPKEY = "J4Z54SWU8DADZTHM64XR2ACBYHGXUJ8F"
PAGESIZE = 100  # Increased page size for faster import

snowflake = Snowflake(worker_id=0, datacenter_id=0)
logger = setup_logger(name=__name__, level="DEBUG")


class MajorInfoCommandExe:
    @staticmethod
    async def fetch_data_page(pageindex: int) -> List[Dict[str, Any]]:
        """Fetch a page of data from the API"""
        params = {"appkey": APPKEY, "pagesize": PAGESIZE, "pageindex": pageindex}

        async with httpx.AsyncClient() as client:
            response = await client.get(BASE_URL, params=params)
            data = response.json()

        if data["DataStatus"]["StatusCode"] != 100:  # API returns 100 for success
            logger.info(f"Error: {data['DataStatus']['StatusDescription']}")
            return []

        return data.get("Data", [])

    @staticmethod
    async def transform_major_data(item: dict) -> dict:
        """
        将API返回的单条专业数据转换为MajorInfo模型所需的dict
        """
        # 课程直接存为结构化列表
        courses = item.get("Courses", [])

        # 推荐院校直接存为list
        recommend_schools = item.get("RecommendSchools", [])

        return {
            "id": snowflake.generate(),
            "education_level": item.get("EducationLevel"),
            "disciplinary_category": item.get("DisciplinaryCategory"),
            "disciplinary_subcategory": item.get("DisciplinarySubCategory"),
            "major_code": item.get("MajorCode"),
            "major_name": item.get("MajorName"),
            "major_introduction": item.get("MajorIntroduction"),
            "course": courses,  # 直接存结构化数据
            "graduate_scale": item.get("GraduateScale"),
            "male_female_ratio": item.get("MaleFemaleRatio"),
            "recommend_schools": recommend_schools,  # 直接存list
        }

    @staticmethod
    async def import_all_data():
        """Import all data from the API into the database"""
        page = 1
        total_imported = 0

        while True:
            logger.info(f"Fetching page {page}...")
            data = await MajorInfoCommandExe.fetch_data_page(page)

            if not data:
                logger.info("No more data to import.")
                break

            for item in data:
                try:
                    transformed_data = await MajorInfoCommandExe.transform_major_data(
                        item
                    )
                    await MajorInfoRepository.create(transformed_data)
                    logger.info(f"Imported major {item.get('MajorName')}")
                    total_imported += 1
                except Exception as e:
                    logger.error(
                        f"Error importing major {item.get('MajorName')}: {str(e)}"
                    )

            logger.info(f"Imported {len(data)} records from page {page}")
            page += 1

        logger.info(f"Import completed. Total records imported: {total_imported}")
