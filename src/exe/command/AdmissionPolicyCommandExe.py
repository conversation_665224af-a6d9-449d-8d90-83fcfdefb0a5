import requests
import json
import base64
from typing import Dict
import os
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from lucas_common_components.logging import setup_logger

from src.infrastructure.llm.EmbeddingClient import EmbeddingClient
from src.infrastructure.llm.llm_client import async_create_llm
from src.utils.snow_flake import Snowflake

snowflake = Snowflake(worker_id=0, datacenter_id=0)

logger = setup_logger(__name__)


class AdmissionPolicyCommandExe:
    """A class for importing documents into OpenSearch."""

    # Class variables for configuration
    _url = "http://ha-cn-s1148yz8r03.ha.aliyuncs.com/update/yai_admission/actions/bulk"
    _access_username = "test_admin"
    _access_password = "FDVoWV+3z%1P%"
    _table_name = "yai_admission"

    SYSTEM_PROMPT = """
    # 角色
    你是一个专业的文档格式转换助手

    # 任务
    将输入的文本内容转换为规范的Markdown格式。要求：
    1. 保持原文的层级结构和内容完整性
    2. 使用适当的Markdown语法（标题、列表、表格等）
    3. 确保格式美观、易读
    4. 保持原文的表格结构，使用Markdown表格语法
    5. 对于特殊格式（如加粗、斜体等），使用对应的Markdown语法

    # 输入
    {text_content}
    """

    @staticmethod
    def _generate_headers() -> Dict[str, str]:
        """Generate headers with authorization"""
        realm_str = f"{AdmissionPolicyCommandExe._access_username}:{AdmissionPolicyCommandExe._access_password}"
        authorization = base64.b64encode(realm_str.encode("utf-8")).decode("utf-8")

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br",
            "Authorization": f"Basic {authorization}",
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "User-Agent": "PostmanRuntime-ApipostRuntime/1.1.0",
            "X-Opensearch-Swift-PK-Field": "id",
        }

    @staticmethod
    async def _process_with_llm(content: str) -> str:
        """
        Process the content through LLM to convert to markdown format.

        Args:
            content: The original text content

        Returns:
            str: The markdown formatted content
        """
        prompts = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(
                    AdmissionPolicyCommandExe.SYSTEM_PROMPT
                ),
                ("user", "请将上述内容转换为规范的Markdown格式。"),
            ]
        )

        llm = await async_create_llm(
            **{
                "model_name": os.getenv("QWEN_32_B"),
                "api_key": os.getenv("ALIBABA_API_KEY"),
                "api_base": os.getenv("ALIBABA_BASE_URL"),
                "temperature": 0.2,
            }
        )

        chain = prompts | llm
        response = await chain.ainvoke({"text_content": content})
        return response.content

    @staticmethod
    async def import_document(
        file_path: str, key: str, doc_type: str = "admission_policy"
    ) -> bool:
        """
        Import a document into OpenSearch.

        Args:
            file_path: Path to the document file
            province: Title of the document
            doc_type: Type of the document (default: "admission_policy")

        Returns:
            bool: True if import was successful, False otherwise
        """
        try:
            # Read the document content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Process content through LLM to convert to markdown
            markdown_content = await AdmissionPolicyCommandExe._process_with_llm(
                content
            )
            logger.info(markdown_content)
            # Prepare the document for import
            fid = snowflake.generate()
            logger.info(fid)
            document = {
                "cmd": "add",
                "fields": {
                    "id": fid,  # Use file modification time as ID
                    "province": key,
                    "vector": EmbeddingClient.get_embedding(markdown_content),
                    "content": markdown_content,
                },
            }

            # Send the request
            response = requests.post(
                AdmissionPolicyCommandExe._url,
                headers=AdmissionPolicyCommandExe._generate_headers(),
                data=json.dumps([document]),  # API expects an array of documents
            )
            response.raise_for_status()

            # Check if the import was successful
            if response.status_code == 200:
                logger.info(f"Successfully imported document: {key}")
                return True
            else:
                logger.info(
                    f"Failed to import document. Status code: {response.status_code}"
                )
                logger.info(f"Response: {response.text}")
                return False

        except Exception as e:
            logger.info(f"Error importing document: {str(e)}")
            return False
