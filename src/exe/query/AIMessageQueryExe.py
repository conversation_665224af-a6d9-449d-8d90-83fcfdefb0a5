from typing import List

from lucas_common_components.logging import setup_logger

from src.adapter.vo.ai_chat_model import (
    AiMessageHistoryResultVO,
    MessageHistoryVO,
)
from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository

logger = setup_logger(name=__name__, level="DEBUG")


class AIMessageQueryService:
    @staticmethod
    async def get_message_history(conversation_id: str) -> AiMessageHistoryResultVO:
        """
        获取会话的消息历史记录

        Args:
            request: 包含conversation_id的请求对象
            context: 用户上下文信息

        Returns:
            MessageHistoryResultVO: 包含消息历史列表的结果对象
            :param cls:
        """
        messages: List[MessageHistoryVO] = []

        if not conversation_id:
            logger.warning(f"Invalid conversation_id: {conversation_id}")
            return AiMessageHistoryResultVO(list=messages)

        try:
            # 获取会话的消息记录
            db_messages = await AIMessageRepository.get_by_conversation_id(
                int(conversation_id)
            )

            # 转换数据库消息为VO对象
            for msg_po in db_messages:
                text = None
                if msg_po.message_type == "text":
                    text = msg_po.message_data.get("text", "")
                message_vo = MessageHistoryVO(
                    text=text,
                    role=msg_po.role,
                    custom={
                        "message_id": str(msg_po.id),
                        "message_type": msg_po.message_type,
                        "message_status": msg_po.message_status,
                        "message_data": msg_po.message_data,
                    },
                )
                messages.append(message_vo)

        except Exception as e:
            logger.error(f"Error retrieving message history: {e}")

        return AiMessageHistoryResultVO(list=messages)
