import asyncio
from typing import AsyncGenerator

from lucas_common_components.logging import setup_logger
from lucas_common_components.nacos.core.nacos_manager import NacosConfigManager

from src.infrastructure.opensearch import AliyunOpenSearchLLMClient
from src.config.config_model import AIChatAppConfig

logger = setup_logger(__name__)


class AdmissionPolicyQueryExe:
    # --- 配置 OpenSearch LLM 智能问答版 ---
    _search_client_instance = None

    @classmethod
    async def search_admission(
        cls, query_text: str, size: int = 3, conversation_id: str = ""
    ) -> AsyncGenerator[str, None]:
        """
        通过OpenSearch LLM智能问答版搜索招生政策，使用流式输出。

        Args:
            query_text: 用户的问题文本。
            size: 希望返回的文档数量。
            conversation_id: 会话ID。

        Yields:
            增量文本片段，适合前端实现打字机效果。
        """
        from src.infrastructure.opensearch import Config

        # 从 Nacos 获取配置
        config = AIChatAppConfig.get_instance()
        opensearch_config = config.openSearchConfig

        client = AliyunOpenSearchLLMClient(
            Config(
                endpoint=opensearch_config.endpoint,
                protocol=opensearch_config.protocol,
                access_key_id=opensearch_config.access_key_id,
                access_key_secret=opensearch_config.access_key_secret,
            )
        )
        if not client:
            logger.error("Failed to initialize LLMSearch client.")
            yield None
            return

        # 构建请求体 (Body)
        # 参考文档: https://help.aliyun.com/zh/open-search/llm-intelligent-q-a-version/search-knowledge
        body = {
            "question": {
                "text": query_text,
                "type": "TEXT",
                "session": conversation_id,
            },
            "options": {
                "chat": {
                    "stream": True,  # 开启流式响应
                },
                "retrieve": {
                    "doc": {
                        "top_n": size,
                        "disable": False,  # 确保文档召回是开启的
                        "operator": "OR",  # 根据需要调整分词后的召回逻辑
                    },
                    "return_hits": True,  # 非常重要，这样才能获取原始文档片段
                },
            },
        }

        try:
            # 使用增量流式输出方法替换原来的searchDoc
            async for increment in client.stream_incremental_search(
                app_name=opensearch_config.app_name, body=body
            ):
                if increment == "data:[done]":
                    logger.debug("Stream completed")
                    break
                # 直接返回增量内容
                yield increment

        except Exception as e:
            logger.error(
                f"Exception during admission policy search: {e}", exc_info=True
            )
            yield None


# --- 用于测试的 main 部分 ---
async def main_test():
    # 注册配置
    NacosConfigManager.get_instance().register_config(AIChatAppConfig)

    # 模拟异步调用
    test_query = "江苏省今年提前批次有哪些政策？"
    current_answer = ""
    async for increment in AdmissionPolicyQueryExe.search_admission(
        test_query, size=2, conversation_id="1725530408586"
    ):
        if increment is not None:
            # 累积显示回答，模拟打字机效果
            current_answer += increment
            logger.info(f"\n--- 新增内容 ---\n{increment}")
            logger.info(f"\n--- 当前完整回答 ---\n{current_answer}")
        elif increment == "":
            logger.info("No relevant policy content found.")
        else:
            logger.info("Search failed.")


if __name__ == "__main__":
    # 使用更现代的asyncio.run方法
    asyncio.run(main_test())
