from typing import List, Optional, Dict, Any

from lucas_common_components.logging import setup_logger
from tortoise.functions import Count, Avg, Max, Min
from tortoise.exceptions import OperationalError

from src.infrastructure.db.models.ai.SchoolMajorCutoff import SchoolMajorCutoff

logger = setup_logger(__name__)


class SchoolMajorCutoffQueryExe:
    """高考专业录取分数线查询执行器，负责处理读操作"""

    @staticmethod
    async def get_cutoff_by_id(score_id: int) -> Optional[SchoolMajorCutoff]:
        """
        根据ID获取专业录取分数线记录

        Args:
            score_id: 专业录取分数线记录ID

        Returns:
            专业录取分数线对象，如果不存在则返回None
        """
        return await SchoolMajorCutoff.filter(id=score_id).first()

    @staticmethod
    async def query_cutoff_scores(
        school_name: Optional[str] = None,
        major_name: Optional[str] = None,
        province_name: Optional[str] = None,
        year: Optional[int] = None,
        batch_name: Optional[str] = None,
        type_name: Optional[str] = None,
        limit: int = 20,
        offset: int = 0,
    ) -> List[SchoolMajorCutoff]:
        """
        查询专业录取分数线

        Args:
            school_name: 学校名称
            major_name: 专业名称
            province_name: 省份名称
            year: 年份
            batch_name: 批次名称
            type_name: 类型名称
            limit: 返回记录数量限制
            offset: 分页偏移量

        Returns:
            符合条件的专业录取分数线对象列表
        """
        query = SchoolMajorCutoff.all()

        if school_name:
            query = query.filter(school_name__contains=school_name)
        if major_name:
            query = query.filter(major_name__contains=major_name)
        if province_name:
            query = query.filter(province_name=province_name)
        if year:
            query = query.filter(year=year)
        if batch_name:
            query = query.filter(batch_name=batch_name)
        if type_name:
            query = query.filter(type_name=type_name)

        return await query.offset(offset).limit(limit).order_by("-year", "lowest_score")

    @staticmethod
    async def query_cutoff_scores_by_range(
        province_name: str,
        year: int,
        min_score: int,
        max_score: int,
        limit: int = 50,
        offset: int = 0,
    ) -> List[SchoolMajorCutoff]:
        """
        根据省份、年份和分数范围查询录取分数线

        Args:
            province_name: 省份名称
            year: 年份
            min_score: 最低分数
            max_score: 最高分数
            limit: 返回记录数量限制
            offset: 分页偏移量

        Returns:
            符合条件的专业录取分数线对象列表
        """
        query = SchoolMajorCutoff.filter(
            province_name=province_name,
            year=year,
            lowest_score__gte=min_score,
            lowest_score__lte=max_score,
        )

        return await query.offset(offset).limit(limit).order_by("lowest_score")

    @staticmethod
    async def count_cutoff_scores(
        school_name: Optional[str] = None,
        major_name: Optional[str] = None,
        province_name: Optional[str] = None,
        year: Optional[int] = None,
        batch_name: Optional[str] = None,
        type_name: Optional[str] = None,
    ) -> int:
        """
        统计符合条件的专业录取分数线数量

        Args:
            school_name: 学校名称
            major_name: 专业名称
            province_name: 省份名称
            year: 年份
            batch_name: 批次名称
            type_name: 类型名称

        Returns:
            符合条件的记录数量
        """
        query = SchoolMajorCutoff.all()

        if school_name:
            query = query.filter(school_name__contains=school_name)
        if major_name:
            query = query.filter(major_name__contains=major_name)
        if province_name:
            query = query.filter(province_name=province_name)
        if year:
            query = query.filter(year=year)
        if batch_name:
            query = query.filter(batch_name=batch_name)
        if type_name:
            query = query.filter(type_name=type_name)

        return await query.count()

    @staticmethod
    async def get_cutoff_statistics(
        school_name: Optional[str] = None,
        major_name: Optional[str] = None,
        province_name: Optional[str] = None,
        year: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        获取专业录取分数线统计信息

        Args:
            school_name: 学校名称
            major_name: 专业名称
            province_name: 省份名称
            year: 年份

        Returns:
            统计信息字典，包含平均分、最高分、最低分等
        """
        query = SchoolMajorCutoff.all()

        if school_name:
            query = query.filter(school_name__contains=school_name)
        if major_name:
            query = query.filter(major_name__contains=major_name)
        if province_name:
            query = query.filter(province_name=province_name)
        if year:
            query = query.filter(year=year)

        result = await query.annotate(
            avg_lowest_score=Avg("lowest_score"),
            max_lowest_score=Max("lowest_score"),
            min_lowest_score=Min("lowest_score"),
            count=Count("id"),
        ).first()

        if result:
            return {
                "avg_lowest_score": result.avg_lowest_score,
                "max_lowest_score": result.max_lowest_score,
                "min_lowest_score": result.min_lowest_score,
                "count": result.count,
            }
        return {
            "avg_lowest_score": 0,
            "max_lowest_score": 0,
            "min_lowest_score": 0,
            "count": 0,
        }

    @staticmethod
    async def check_cutoff_exists(
        school_uuid: str,
        province_name: str,
        major_name: str,
        year: int,
        batch_name: str,
        type_name: str,
    ) -> bool:
        """
        检查具有相同唯一约束条件的记录是否已存在

        Args:
            school_uuid: 学校UUID
            province_name: 省份名称
            major_name: 专业名称
            year: 年份
            batch_name: 批次名称
            type_name: 类型名称

        Returns:
            如果记录存在则返回True，否则返回False
        """
        return await SchoolMajorCutoff.filter(
            school_uuid=school_uuid,
            province_name=province_name,
            major_name=major_name,
            year=year,
            batch_name=batch_name,
            type_name=type_name,
        ).exists()

    @staticmethod
    async def get_cutoff_data(
        school_name: Optional[str] = None,
        province_name: Optional[str] = None,
        year: Optional[int] = None,
        major_name: Optional[str] = None,
    ) -> List[SchoolMajorCutoff]:
        """
        获取分数线数据（语义化、整洁化、简洁化）

        Args:
            school_name: 学校名称
            province_name: 省份名称
            year: 年份
            major_name: 专业名称

        Returns:
            符合条件的专业录取分数线对象列表
        """
        filters = {}
        query_description_parts = []
        if school_name:
            filters["school_name__contains"] = school_name
            query_description_parts.append(f"school_name LIKE '%{school_name}%'")
        if province_name:
            filters["province_name__contains"] = province_name
            query_description_parts.append(f"province_name = '%{province_name}%'")
        if year is not None:
            filters["year"] = year
            query_description_parts.append(f"year = {year}")
        if major_name:
            filters["major_name__contains"] = major_name
            query_description_parts.append(f"major_name LIKE '%{major_name}%'")

        # Define specific fields to select
        fields = [
            "id",
            "school_uuid",
            "school_name",
            "province_name",
            "major_name",
            "major_code",
            "major_standard_code",
            "year",
            "highest_score",
            "average_score",
            "lowest_score",
            "lowest_section",
            "batch_name",
            "type_name",
            "pro_score",
            "subject_selection",
            "created_at",
        ]

        # Use .only() to select specific fields
        query = SchoolMajorCutoff.filter(**filters).only(*fields)

        try:
            # Generate SQL query for logging
            sql_fields = ", ".join(fields)
            where_clause = (
                " AND ".join(query_description_parts)
                if query_description_parts
                else "1=1"
            )
            logger.info(
                f"[SQL GET_CUTOFF] SELECT {sql_fields} FROM major_admission_score WHERE {where_clause}"
            )
        except Exception:
            if query_description_parts:
                fallback_sql = f"SELECT {', '.join(fields)} FROM major_admission_score WHERE {' AND '.join(query_description_parts)};"
            else:
                fallback_sql = f"SELECT {', '.join(fields)} FROM major_admission_score;"
            logger.info(f"[SQL GET_CUTOFF] (Fallback) {fallback_sql}")

        try:
            results: List[SchoolMajorCutoff] = await query.all()
            return results
        except OperationalError:
            raise
        except Exception:
            raise

    @staticmethod
    async def get_all_schools_by_year(year: int) -> List[Dict]:
        """
        获取指定年份的所有学校

        Args:
            year: 年份

        Returns:
            List[Dict]: 包含school_uuid和school_name的学校列表
        """
        # 从数据库中获取指定年份的所有不同学校
        schools = (
            await SchoolMajorCutoff.filter(year=year)
            .distinct()
            .values_list("school_uuid", "school_name", flat=False)
        )

        # 将查询结果转换为字典列表
        return [{"school_uuid": uuid, "school_name": name} for uuid, name in schools]

    @staticmethod
    async def get_school_majors_by_year(year: int, school_uuid: str) -> List[Dict]:
        """
        获取指定学校和年份的所有专业

        Args:
            year: 年份
            school_uuid: 学校UUID

        Returns:
            List[Dict]: 包含major_name的专业列表
        """
        # 从数据库中获取指定学校、年份的所有不同专业
        majors = (
            await SchoolMajorCutoff.filter(year=year, school_uuid=school_uuid)
            .distinct()
            .values_list("major_name", flat=True)
        )

        # 将结果转换为字典列表
        return [{"major_name": major_name} for major_name in majors]

    @staticmethod
    async def get_major_provinces_data(
        year: int, school_uuid: str, major_name: str
    ) -> List[Dict]:
        """
        获取专业招生的省份数据

        Args:
            year: 年份
            school_uuid: 学校UUID
            major_name: 专业名称

        Returns:
            List[Dict]: 包含province_name的省份列表
        """
        # 从数据库中获取指定学校、专业、年份招生的所有省份
        provinces = (
            await SchoolMajorCutoff.filter(
                year=year, school_uuid=school_uuid, major_name=major_name
            )
            .distinct()
            .values_list("province_name", flat=True)
        )

        # 将结果转换为字典列表
        return [{"province_name": province} for province in provinces]

    @staticmethod
    async def get_cutoff_rank(
        year: int,
        school_uuid: str,
        major_name: str,
        province_name: str,
        subject_selection: str,
    ) -> Optional[Dict]:
        """
        获取特定条件下的位次

        Args:
            year: 年份
            school_uuid: 学校UUID
            major_name: 专业名称
            province_name: 省份名称
            subject_selection: 科目组合

        Returns:
            Optional[Dict]: 包含rank的字典，如果不存在返回None
        """
        # 查询满足条件的分数记录
        try:
            # 使用type_name字段而非subject_selection字段
            record = await SchoolMajorCutoff.filter(
                year=year,
                school_uuid=school_uuid,
                major_name=major_name,
                province_name=province_name,
                type_name=subject_selection,  # 使用type_name字段
            ).first()

            # 如果找到记录并有位次数据，返回位次
            if record and record.lowest_section:
                return {"rank": record.lowest_section}

            return None

        except Exception as e:
            logger.error(f"查询位次时出错: {str(e)}")
            return None 