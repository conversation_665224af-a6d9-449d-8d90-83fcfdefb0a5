"""
AI聊天Gateway实现

在Exe层实现Domain层定义的AIChatGateway接口。
负责具体的数据访问和外部服务调用逻辑。
"""

from typing import Optional, List, Dict, Any
from langchain_core.runnables import RunnableConfig
from lucas_common_components.logging import setup_logger

from src.domain.gateway.ai_chat_gateway import AIChatGateway
from src.domain.model.ai_chat_model import CustomBO
from src.infrastructure.db.crud.ai.AIConversationRepository import AIConversationRepository
from src.infrastructure.db.crud.ai.AIMessageRepository import AIMessageRepository
from src.infrastructure.db.crud.ai.ReportTaskRepository import ReportTaskRepository
from src.infrastructure.rpc.account_client import AccountClient
from src.infrastructure.db.PGCheckpointClient import PGCheckpointClient

logger = setup_logger(name=__name__, level="DEBUG")


class AIChatGatewayImpl(AIChatGateway):
    """AI聊天Gateway实现类
    
    实现Domain层定义的AIChatGateway接口，
    负责具体的数据访问和外部服务调用。
    """

    def __init__(self):
        """初始化Gateway实现"""
        self.account_client = AccountClient()

    async def get_conversation_by_id(self, conversation_id: int) -> Optional[Any]:
        """根据ID获取会话信息
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Optional[Any]: 会话对象，如果不存在则返回None
        """
        try:
            return await AIConversationRepository.get_by_id(conversation_id)
        except Exception as e:
            logger.error(f"获取会话失败 conversation_id={conversation_id}: {str(e)}")
            return None

    async def update_conversation_timestamp(self, conversation_id: int) -> None:
        """更新会话时间戳
        
        Args:
            conversation_id: 会话ID
        """
        try:
            conversation = await AIConversationRepository.get_by_id(conversation_id)
            if conversation:
                logger.info(f"尝试更新会话 {conversation_id} 的 updated_at 字段...")
                await conversation.save()
                logger.info(f"会话 {conversation_id} updated_at 字段更新完成。")
        except Exception as e:
            logger.error(f"更新会话时间戳失败 conversation_id={conversation_id}: {str(e)}")

    async def get_messages_by_conversation_id(self, conversation_id: int, limit: int = 10) -> List[Any]:
        """根据会话ID获取消息列表
        
        Args:
            conversation_id: 会话ID
            limit: 限制数量
            
        Returns:
            List[Any]: 消息列表
        """
        try:
            if limit == 10:
                return await AIMessageRepository.get_by_conversation_id_limit10(conversation_id)
            else:
                # 如果需要其他限制数量，可以扩展Repository方法
                return await AIMessageRepository.get_by_conversation_id(conversation_id)
        except Exception as e:
            logger.error(f"获取消息列表失败 conversation_id={conversation_id}: {str(e)}")
            return []

    async def save_user_message(self, conversation_id: int, messages: List[Dict[str, Any]]) -> None:
        """保存用户消息
        
        Args:
            conversation_id: 会话ID
            messages: 消息数据列表
        """
        try:
            for message_data in messages:
                await AIMessageRepository.create(message_data)
        except Exception as e:
            logger.error(f"保存用户消息失败 conversation_id={conversation_id}: {str(e)}")

    async def save_ai_text_message(self, text: str, conversation_id: int, message_id: int) -> None:
        """保存AI文本消息
        
        Args:
            text: 文本内容
            conversation_id: 会话ID
            message_id: 消息ID
        """
        try:
            if text:
                # 检查消息是否已存在
                ai_message = await AIMessageRepository.get_by_id(message_id)
                
                if ai_message:
                    # 更新现有消息
                    await AIMessageRepository.update(
                        message_id,
                        {
                            "message_type": "text",
                            "message_data": {"text": text},
                        },
                    )
                else:
                    # 创建新消息
                    ai_message_data = {
                        "id": message_id,
                        "conversation_id": conversation_id,
                        "role": "ai",
                        "message_type": "text",
                        "message_data": {"text": text},
                    }
                    await AIMessageRepository.create(ai_message_data)
        except Exception as e:
            logger.error(f"保存AI文本消息失败 message_id={message_id}: {str(e)}")

    async def save_ai_custom_message(self, conversation_id: int, message_id: int, custom: CustomBO) -> None:
        """保存AI自定义消息
        
        Args:
            conversation_id: 会话ID
            message_id: 消息ID
            custom: 自定义消息内容
        """
        try:
            # 检查消息是否已存在
            ai_message = await AIMessageRepository.get_by_id(message_id)

            if ai_message:
                # 更新现有消息
                await AIMessageRepository.update(
                    message_id,
                    {
                        "message_type": custom.message_type,
                        "message_status": custom.message_status,
                        "message_data": custom.message_data,
                    },
                )
            else:
                # 创建新消息
                ai_message_data = {
                    "id": message_id,
                    "conversation_id": conversation_id,
                    "role": "ai",
                    "message_type": custom.message_type,
                    "message_status": custom.message_status,
                    "message_data": custom.message_data,
                }
                await AIMessageRepository.create(ai_message_data)
        except Exception as e:
            logger.error(f"保存AI自定义消息失败 message_id={message_id}: {str(e)}")

    async def get_report_task_by_id(self, task_id: str) -> Optional[Any]:
        """根据任务ID获取报告任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Any]: 报告任务对象，如果不存在则返回None
        """
        try:
            return await ReportTaskRepository.get_by_task_id(task_id)
        except Exception as e:
            logger.error(f"获取报告任务失败 task_id={task_id}: {str(e)}")
            return None

    async def get_user_profile(self, uniq_user_id: str) -> Optional[Any]:
        """获取用户档案信息
        
        Args:
            uniq_user_id: 唯一用户ID
            
        Returns:
            Optional[Any]: 用户档案信息，如果不存在则返回None
        """
        try:
            return await self.account_client.get_profile(uniq_user_id=uniq_user_id)
        except Exception as e:
            logger.error(f"获取用户档案失败 uniq_user_id={uniq_user_id}: {str(e)}")
            return None

    async def get_checkpoint_data(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取检查点数据
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Optional[Dict[str, Any]]: 检查点数据，如果不存在则返回None
        """
        try:
            checkpointer = await PGCheckpointClient.get_checkpoint()
            config = RunnableConfig(configurable={"thread_id": conversation_id})
            checkpointer_data = await checkpointer.aget_tuple(config)
            
            checkpointer_state_dict = {"bizRuntimeBO": None}
            if checkpointer_data and hasattr(checkpointer_data, "checkpoint"):
                checkpointer_state_dict = checkpointer_data.checkpoint.get(
                    "channel_values", checkpointer_state_dict
                )
            
            return checkpointer_state_dict
        except Exception as e:
            logger.error(f"获取检查点数据失败 conversation_id={conversation_id}: {str(e)}")
            return None 