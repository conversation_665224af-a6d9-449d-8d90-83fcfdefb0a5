#!/usr/bin/env python3
"""
DDD 架构约束检查脚本
用于检查项目是否遵循 DDD 分层架构规范
"""

import ast
import sys
from pathlib import Path
from typing import List

# DDD 分层架构规则定义
LAYER_RULES = {
    "adapter": {
        "can_import": ["app", "domain", "exe", "infrastructure"],
        "cannot_import": [],
        "can_be_imported_by": [],
        "cannot_be_imported_by": ["app", "domain", "exe", "infrastructure"],
    },
    "app": {
        "can_import": ["domain", "exe", "infrastructure"],
        "cannot_import": ["adapter"],
        "can_be_imported_by": ["adapter"],
        "cannot_be_imported_by": ["domain", "exe", "infrastructure"],
    },
    "domain": {
        "can_import": [],  # 只能导入标准库和自身
        "cannot_import": ["adapter", "app", "exe", "infrastructure"],
        "can_be_imported_by": ["adapter", "app", "exe", "infrastructure"],
        "cannot_be_imported_by": [],
    },
    "exe": {
        "can_import": ["domain", "infrastructure"],
        "cannot_import": ["adapter", "app", "exe"],  # 禁止 exe 之间相互调用
        "can_be_imported_by": ["adapter", "app"],
        "cannot_be_imported_by": ["domain", "infrastructure"],
    },
    "infrastructure": {
        "can_import": ["domain"],  # 仅接口
        "cannot_import": ["adapter", "app", "exe"],
        "can_be_imported_by": ["adapter", "app", "exe"],
        "cannot_be_imported_by": ["domain"],
    },
}


class ArchitectureChecker:
    def __init__(self, src_path: str = "src"):
        self.src_path = Path(src_path)
        self.violations = []

    def check_file(self, file_path: Path) -> List[str]:
        """检查单个文件的导入是否违反架构规则"""
        violations = []

        # 确定文件所属的层
        layer = self.get_layer_from_path(file_path)
        if not layer:
            return violations

        # 解析文件中的导入语句
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                tree = ast.parse(f.read())
        except Exception as e:
            return [f"无法解析文件 {file_path}: {e}"]

        # 检查每个导入语句
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                imported_modules = self.extract_imports(node)
                for imported_module in imported_modules:
                    imported_layer = self.get_layer_from_module(imported_module)
                    if imported_layer and self.is_violation(layer, imported_layer):
                        violations.append(
                            f"{file_path}:{node.lineno} - "
                            f"{layer} 层禁止导入 {imported_layer} 层: {imported_module}"
                        )

        return violations

    def get_layer_from_path(self, file_path: Path) -> str:
        """从文件路径确定所属层"""
        parts = file_path.parts
        if "src" in parts:
            src_index = parts.index("src")
            if src_index + 1 < len(parts):
                layer = parts[src_index + 1]
                if layer in LAYER_RULES:
                    return layer
        return ""

    def get_layer_from_module(self, module_name: str) -> str:
        """从模块名确定所属层"""
        if module_name.startswith("src."):
            parts = module_name.split(".")
            if len(parts) >= 2 and parts[1] in LAYER_RULES:
                return parts[1]
        return ""

    def extract_imports(self, node) -> List[str]:
        """提取导入的模块名"""
        imports = []
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.append(node.module)
        return imports

    def is_violation(self, from_layer: str, to_layer: str) -> bool:
        """检查是否违反架构规则"""
        if from_layer == to_layer and from_layer == "exe":
            # exe 层禁止相互调用
            return True

        rules = LAYER_RULES.get(from_layer, {})
        cannot_import = rules.get("cannot_import", [])
        return to_layer in cannot_import

    def check_project(self) -> bool:
        """检查整个项目"""
        print("🔍 开始检查 DDD 架构约束...")

        if not self.src_path.exists():
            print(f"❌ 源码目录不存在: {self.src_path}")
            return False

        # 遍历所有 Python 文件
        python_files = list(self.src_path.rglob("*.py"))
        if not python_files:
            print("⚠️  未找到 Python 文件")
            return True

        total_violations = 0
        for file_path in python_files:
            violations = self.check_file(file_path)
            if violations:
                print(f"\n❌ {file_path}:")
                for violation in violations:
                    print(f"   {violation}")
                total_violations += len(violations)

        if total_violations == 0:
            print("✅ 架构约束检查通过！")
            return True
        else:
            print(f"\n❌ 发现 {total_violations} 个架构违规问题")
            return False


def main():
    """主函数"""
    checker = ArchitectureChecker()
    success = checker.check_project()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
