#!/usr/bin/env python3
"""
任务完成处理测试脚本

验证任务完成后正确通知客户端连接，避免客户端无限等待。
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_task_completion_signals():
    """测试任务完成信号"""
    print("🔍 测试任务完成信号...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_completion():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建成功完成的处理器
            async def successful_processor(request, user_id, conversation_id):
                for i in range(3):
                    await asyncio.sleep(0.1)
                    yield f"Processing step {i+1}"
                yield "Task completed successfully"
            
            # 创建连接
            connection = await connection_manager.add_connection("test_conv", "test_user")
            
            # 创建请求
            request = ChatInputVO(
                conversation_id="test_conv",
                messages=[MessageVO(text="test task", role="user")]
            )
            
            # 提交任务
            await session_manager.submit_session_task(
                request, "test_user", connection.connection_id, successful_processor
            )
            
            # 收集所有响应
            responses = []
            completion_signal_received = False
            end_signal_received = False
            
            while True:
                try:
                    response = await asyncio.wait_for(
                        connection.response_queue.get(), 
                        timeout=2.0
                    )
                    
                    if response is None:  # 结束信号
                        end_signal_received = True
                        break
                    
                    responses.append(response)
                    
                    # 检查是否是完成信号
                    if isinstance(response, dict) and response.get("task_completed"):
                        completion_signal_received = True
                        
                except asyncio.TimeoutError:
                    break
            
            # 验证响应
            assert len(responses) >= 4, f"应该收到至少4个响应，实际收到{len(responses)}个"
            assert completion_signal_received, "应该收到任务完成信号"
            assert end_signal_received, "应该收到结束信号"
            
            # 验证完成信号的内容
            completion_signals = [r for r in responses if isinstance(r, dict) and r.get("task_completed")]
            assert len(completion_signals) == 1, "应该只有一个完成信号"
            
            completion_signal = completion_signals[0]
            assert "task_id" in completion_signal, "完成信号应该包含task_id"
            assert "conversation_id" in completion_signal, "完成信号应该包含conversation_id"
            assert "completed_at" in completion_signal, "完成信号应该包含completed_at"
            
            print("    ✅ 任务完成信号验证通过")
            
            # 清理
            await connection_manager.close_all_connections()
            await session_manager.shutdown()
        
        asyncio.run(test_completion())
        return True
        
    except Exception as e:
        print(f"❌ 任务完成信号测试失败: {e}")
        traceback.print_exc()
        return False


def test_task_error_signals():
    """测试任务错误信号"""
    print("\n🔍 测试任务错误信号...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_error():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建会出错的处理器
            async def error_processor(request, user_id, conversation_id):
                for i in range(2):
                    await asyncio.sleep(0.1)
                    yield f"Processing step {i+1}"
                raise Exception("Simulated processing error")
            
            # 创建连接
            connection = await connection_manager.add_connection("error_conv", "test_user")
            
            # 创建请求
            request = ChatInputVO(
                conversation_id="error_conv",
                messages=[MessageVO(text="error task", role="user")]
            )
            
            # 提交任务
            await session_manager.submit_session_task(
                request, "test_user", connection.connection_id, error_processor
            )
            
            # 收集所有响应
            responses = []
            error_signal_received = False
            end_signal_received = False
            
            while True:
                try:
                    response = await asyncio.wait_for(
                        connection.response_queue.get(), 
                        timeout=2.0
                    )
                    
                    if response is None:  # 结束信号
                        end_signal_received = True
                        break
                    
                    responses.append(response)
                    
                    # 检查是否是错误信号
                    if isinstance(response, dict) and response.get("error"):
                        error_signal_received = True
                        
                except asyncio.TimeoutError:
                    break
            
            # 验证响应
            assert len(responses) >= 3, f"应该收到至少3个响应，实际收到{len(responses)}个"
            assert error_signal_received, "应该收到错误信号"
            assert end_signal_received, "应该收到结束信号"
            
            # 验证错误信号的内容
            error_signals = [r for r in responses if isinstance(r, dict) and r.get("error")]
            assert len(error_signals) == 1, "应该只有一个错误信号"
            
            error_signal = error_signals[0]
            assert "task_id" in error_signal, "错误信号应该包含task_id"
            assert "conversation_id" in error_signal, "错误信号应该包含conversation_id"
            assert "message" in error_signal, "错误信号应该包含错误消息"
            assert "failed_at" in error_signal, "错误信号应该包含失败时间"
            
            print("    ✅ 任务错误信号验证通过")
            
            # 清理
            await connection_manager.close_all_connections()
            await session_manager.shutdown()
        
        asyncio.run(test_error())
        return True
        
    except Exception as e:
        print(f"❌ 任务错误信号测试失败: {e}")
        traceback.print_exc()
        return False


def test_connection_response_reading():
    """测试连接响应读取"""
    print("\n🔍 测试连接响应读取...")
    
    try:
        from src.app.ai_chat_service import _read_connection_responses
        from src.infrastructure.session import ConnectionManager, ConnectionStatus
        
        async def test_reading():
            connection_manager = ConnectionManager()
            
            # 创建连接
            connection = await connection_manager.add_connection("read_conv", "test_user")
            
            # 模拟发送一些响应
            test_responses = [
                "Response 1",
                "Response 2", 
                {"task_completed": True, "task_id": "test_task", "conversation_id": "read_conv"},
                None  # 结束信号
            ]
            
            # 将响应放入队列
            for response in test_responses:
                await connection.response_queue.put(response)
            
            # 读取响应
            collected_responses = []
            async for response in _read_connection_responses(connection):
                collected_responses.append(response)
            
            # 验证读取的响应
            assert len(collected_responses) == 3, f"应该读取到3个响应，实际读取到{len(collected_responses)}个"
            assert collected_responses[0] == "Response 1", "第一个响应不正确"
            assert collected_responses[1] == "Response 2", "第二个响应不正确"
            assert isinstance(collected_responses[2], dict), "第三个响应应该是字典"
            assert collected_responses[2]["task_completed"] == True, "第三个响应应该是完成信号"
            
            # 验证连接状态
            assert connection.status == ConnectionStatus.CLOSED, "连接应该被标记为关闭"
            
            print("    ✅ 连接响应读取验证通过")
            
            # 清理
            await connection_manager.close_all_connections()
        
        asyncio.run(test_reading())
        return True
        
    except Exception as e:
        print(f"❌ 连接响应读取测试失败: {e}")
        traceback.print_exc()
        return False


def test_multiple_connections_completion():
    """测试多连接任务完成"""
    print("\n🔍 测试多连接任务完成...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_multiple():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建处理器
            async def shared_processor(request, user_id, conversation_id):
                for i in range(3):
                    await asyncio.sleep(0.1)
                    yield f"Shared step {i+1}"
                yield "Shared task completed"
            
            # 创建多个连接到同一会话
            connections = []
            for i in range(3):
                conn = await connection_manager.add_connection("shared_conv", f"user_{i}")
                connections.append(conn)
            
            # 创建请求
            request = ChatInputVO(
                conversation_id="shared_conv",
                messages=[MessageVO(text="shared task", role="user")]
            )
            
            # 提交任务
            await session_manager.submit_session_task(
                request, "user_0", connections[0].connection_id, shared_processor
            )
            
            # 从所有连接读取响应
            async def read_from_connection(conn, conn_index):
                responses = []
                completion_received = False
                end_received = False
                
                while True:
                    try:
                        response = await asyncio.wait_for(
                            conn.response_queue.get(), 
                            timeout=2.0
                        )
                        
                        if response is None:
                            end_received = True
                            break
                        
                        responses.append(response)
                        
                        if isinstance(response, dict) and response.get("task_completed"):
                            completion_received = True
                            
                    except asyncio.TimeoutError:
                        break
                
                return responses, completion_received, end_received
            
            # 并发读取所有连接
            tasks = [
                read_from_connection(conn, i) 
                for i, conn in enumerate(connections)
            ]
            
            results = await asyncio.gather(*tasks)
            
            # 验证所有连接都收到了相同的响应
            for i, (responses, completion_received, end_received) in enumerate(results):
                assert len(responses) >= 4, f"连接{i}应该收到至少4个响应"
                assert completion_received, f"连接{i}应该收到完成信号"
                assert end_received, f"连接{i}应该收到结束信号"
            
            # 验证响应内容一致性
            first_responses = results[0][0]
            for i, (responses, _, _) in enumerate(results[1:], 1):
                assert len(responses) == len(first_responses), f"连接{i}的响应数量与连接0不一致"
                for j, response in enumerate(responses):
                    if isinstance(response, dict) and isinstance(first_responses[j], dict):
                        # 对于字典类型，检查关键字段
                        if "task_completed" in response:
                            assert response["task_completed"] == first_responses[j]["task_completed"], f"连接{i}的完成信号不一致"
                    else:
                        assert response == first_responses[j], f"连接{i}的响应{j}与连接0不一致"
            
            print("    ✅ 多连接任务完成验证通过")
            
            # 清理
            await connection_manager.close_all_connections()
            await session_manager.shutdown()
        
        asyncio.run(test_multiple())
        return True
        
    except Exception as e:
        print(f"❌ 多连接任务完成测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 任务完成处理测试")
    print("=" * 60)
    
    success = True
    
    # 运行测试
    success &= test_task_completion_signals()
    success &= test_task_error_signals()
    success &= test_connection_response_reading()
    success &= test_multiple_connections_completion()
    
    # 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有任务完成处理测试都通过了！")
        print("\n📊 任务完成处理特性:")
        print("  ✅ 任务成功完成信号")
        print("  ✅ 任务错误完成信号")
        print("  ✅ 结束信号 (None) 通知客户端停止等待")
        print("  ✅ 连接响应读取正确处理信号")
        print("  ✅ 多连接广播完成信号")
        print("  ✅ 连接状态正确更新")
        print("\n🎯 解决的问题:")
        print("  ❌ 客户端无限等待服务端响应")
        print("  ✅ 任务完成后及时通知客户端")
        print("  ✅ 错误情况下也能正确结束")
        print("  ✅ 多连接同步接收完成信号")
        print("\n💡 信号类型:")
        print("  1. 处理结果: 正常的处理输出")
        print("  2. 完成信号: {task_completed: true, task_id, conversation_id, completed_at}")
        print("  3. 错误信号: {error: true, message, task_id, conversation_id, failed_at}")
        print("  4. 结束信号: None (通知客户端停止等待)")
        print("\n🔄 客户端处理流程:")
        print("  1. 接收处理结果并显示")
        print("  2. 收到完成/错误信号时更新状态")
        print("  3. 收到结束信号 (None) 时停止等待")
        print("  4. 连接被标记为关闭")
    else:
        print("❌ 任务完成处理测试失败，需要进一步修复")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        traceback.print_exc()
        sys.exit(1)
