#!/usr/bin/env python3
"""
分段锁性能测试脚本

验证分段锁优化后，不同会话的任务提交不会互相阻塞。
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_concurrent_task_submission():
    """测试并发任务提交性能"""
    print("🔍 测试并发任务提交性能...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_performance():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建模拟处理器
            async def mock_processor(request, user_id, conversation_id):
                await asyncio.sleep(0.1)  # 模拟处理时间
                yield f"Result for {conversation_id}"
            
            # 测试参数
            num_conversations = 10  # 10个不同会话
            tasks_per_conversation = 5  # 每个会话5个任务
            
            # 为每个会话创建连接
            connections = {}
            for i in range(num_conversations):
                conv_id = f"conv_{i}"
                conn = await connection_manager.add_connection(conv_id, f"user_{i}")
                connections[conv_id] = conn
            
            # 并发提交任务的函数
            async def submit_tasks_for_conversation(conv_id: str, task_count: int):
                start_time = time.time()
                tasks = []
                
                for j in range(task_count):
                    request = ChatInputVO(
                        conversation_id=conv_id,
                        messages=[MessageVO(text=f"Task {j} for {conv_id}", role="user")]
                    )
                    
                    task = session_manager.submit_session_task(
                        request, f"user_{conv_id}", connections[conv_id].connection_id, mock_processor
                    )
                    tasks.append(task)
                
                # 等待所有任务提交完成
                await asyncio.gather(*tasks)
                
                end_time = time.time()
                return conv_id, end_time - start_time
            
            # 并发测试：所有会话同时提交任务
            print("    开始并发任务提交测试...")
            overall_start = time.time()
            
            submission_tasks = [
                submit_tasks_for_conversation(f"conv_{i}", tasks_per_conversation)
                for i in range(num_conversations)
            ]
            
            results = await asyncio.gather(*submission_tasks)
            
            overall_end = time.time()
            overall_time = overall_end - overall_start
            
            # 分析结果
            total_tasks = num_conversations * tasks_per_conversation
            avg_submission_time = sum(result[1] for result in results) / len(results)
            max_submission_time = max(result[1] for result in results)
            min_submission_time = min(result[1] for result in results)
            
            print(f"    总任务数: {total_tasks}")
            print(f"    总耗时: {overall_time:.3f}s")
            print(f"    平均每会话提交时间: {avg_submission_time:.3f}s")
            print(f"    最大会话提交时间: {max_submission_time:.3f}s")
            print(f"    最小会话提交时间: {min_submission_time:.3f}s")
            print(f"    任务提交吞吐量: {total_tasks/overall_time:.1f} tasks/s")
            
            # 验证性能指标
            # 如果是全局锁，所有会话的提交时间应该接近串行总和
            # 如果是分段锁，提交时间应该接近最慢的单个会话时间
            expected_serial_time = avg_submission_time * num_conversations
            parallelism_efficiency = expected_serial_time / overall_time
            
            print(f"    并行效率: {parallelism_efficiency:.1f}x")
            
            # 验证分段锁效果
            assert parallelism_efficiency > 3.0, f"并行效率应该 > 3x，实际: {parallelism_efficiency:.1f}x"
            assert max_submission_time - min_submission_time < 0.1, "不同会话的提交时间差异应该很小"
            
            print("    ✅ 并发任务提交性能验证通过")
            
            # 清理
            await session_manager.shutdown()
            await connection_manager.close_all_connections()
        
        asyncio.run(test_performance())
        return True
        
    except Exception as e:
        print(f"❌ 并发任务提交性能测试失败: {e}")
        traceback.print_exc()
        return False


def test_lock_contention():
    """测试锁竞争情况"""
    print("\n🔍 测试锁竞争情况...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_contention():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建处理器
            async def quick_processor(request, user_id, conversation_id):
                yield f"Quick result for {conversation_id}"
            
            # 测试场景：多个任务同时提交到不同会话
            num_concurrent_submissions = 20
            
            # 创建连接
            connections = []
            for i in range(num_concurrent_submissions):
                conv_id = f"contention_conv_{i}"
                conn = await connection_manager.add_connection(conv_id, f"user_{i}")
                connections.append((conv_id, conn))
            
            # 并发提交函数
            async def submit_single_task(conv_id: str, connection_id: str, task_index: int):
                start_time = time.time()
                
                request = ChatInputVO(
                    conversation_id=conv_id,
                    messages=[MessageVO(text=f"Contention test {task_index}", role="user")]
                )
                
                await session_manager.submit_session_task(
                    request, f"user_{conv_id}", connection_id, quick_processor
                )
                
                end_time = time.time()
                return task_index, end_time - start_time
            
            # 同时提交所有任务
            print(f"    同时提交 {num_concurrent_submissions} 个任务到不同会话...")
            start_time = time.time()
            
            submission_tasks = [
                submit_single_task(conv_id, conn.connection_id, i)
                for i, (conv_id, conn) in enumerate(connections)
            ]
            
            results = await asyncio.gather(*submission_tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 分析锁竞争
            submission_times = [result[1] for result in results]
            avg_time = sum(submission_times) / len(submission_times)
            max_time = max(submission_times)
            min_time = min(submission_times)
            std_dev = (sum((t - avg_time) ** 2 for t in submission_times) / len(submission_times)) ** 0.5
            
            print(f"    总提交时间: {total_time:.3f}s")
            print(f"    平均单任务提交时间: {avg_time:.3f}s")
            print(f"    最大提交时间: {max_time:.3f}s")
            print(f"    最小提交时间: {min_time:.3f}s")
            print(f"    标准差: {std_dev:.3f}s")
            print(f"    时间变异系数: {std_dev/avg_time:.2f}")
            
            # 验证锁竞争程度
            # 分段锁应该有很低的时间变异性
            assert std_dev / avg_time < 0.5, f"时间变异系数应该 < 0.5，实际: {std_dev/avg_time:.2f}"
            assert total_time < 0.5, f"总提交时间应该 < 0.5s，实际: {total_time:.3f}s"
            
            print("    ✅ 锁竞争测试验证通过")
            
            # 清理
            await session_manager.shutdown()
            await connection_manager.close_all_connections()
        
        asyncio.run(test_contention())
        return True
        
    except Exception as e:
        print(f"❌ 锁竞争测试失败: {e}")
        traceback.print_exc()
        return False


def test_same_conversation_serialization():
    """测试同一会话内的串行化"""
    print("\n🔍 测试同一会话内的串行化...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_serialization():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建处理器
            async def serial_processor(request, user_id, conversation_id):
                await asyncio.sleep(0.1)  # 模拟处理时间
                yield f"Serial result for {conversation_id}"
            
            # 创建单个会话的多个连接
            conv_id = "serial_test_conv"
            connections = []
            for i in range(3):
                conn = await connection_manager.add_connection(conv_id, f"user_{i}")
                connections.append(conn)
            
            # 记录任务提交和开始时间
            task_events = []
            
            async def submit_task_with_timing(connection_id: str, task_index: int):
                submit_time = time.time()
                
                request = ChatInputVO(
                    conversation_id=conv_id,
                    messages=[MessageVO(text=f"Serial task {task_index}", role="user")]
                )
                
                await session_manager.submit_session_task(
                    request, f"user_{task_index}", connection_id, serial_processor
                )
                
                task_events.append({
                    "task_index": task_index,
                    "submit_time": submit_time,
                    "connection_id": connection_id
                })
            
            # 快速连续提交多个任务到同一会话
            print(f"    快速连续提交 3 个任务到同一会话...")
            
            submission_tasks = [
                submit_task_with_timing(conn.connection_id, i)
                for i, conn in enumerate(connections)
            ]
            
            await asyncio.gather(*submission_tasks)
            
            # 等待所有任务处理完成
            await asyncio.sleep(0.5)
            
            # 验证任务是串行处理的
            # 检查会话状态
            session_stats = await session_manager.get_manager_stats()
            print(f"    处理完成后的统计: {session_stats}")
            
            # 验证串行化
            submit_times = [event["submit_time"] for event in task_events]
            time_span = max(submit_times) - min(submit_times)
            
            print(f"    任务提交时间跨度: {time_span:.3f}s")
            print(f"    任务提交间隔: {time_span/len(task_events):.3f}s")
            
            # 同一会话的任务应该能快速提交（分段锁不阻塞提交）
            assert time_span < 0.1, f"同一会话任务提交应该很快，实际跨度: {time_span:.3f}s"
            
            print("    ✅ 同一会话串行化验证通过")
            
            # 清理
            await session_manager.shutdown()
            await connection_manager.close_all_connections()
        
        asyncio.run(test_serialization())
        return True
        
    except Exception as e:
        print(f"❌ 同一会话串行化测试失败: {e}")
        traceback.print_exc()
        return False


def test_lock_cleanup():
    """测试锁清理机制"""
    print("\n🔍 测试锁清理机制...")
    
    try:
        from src.infrastructure.session import ConnectionManager, SessionManager
        from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO
        
        async def test_cleanup():
            connection_manager = ConnectionManager()
            session_manager = SessionManager(connection_manager)
            
            # 创建处理器
            async def cleanup_processor(request, user_id, conversation_id):
                yield f"Cleanup test for {conversation_id}"
            
            # 创建多个会话
            conversations = []
            for i in range(5):
                conv_id = f"cleanup_conv_{i}"
                conn = await connection_manager.add_connection(conv_id, f"user_{i}")
                conversations.append((conv_id, conn))
            
            # 提交任务到所有会话
            for conv_id, conn in conversations:
                request = ChatInputVO(
                    conversation_id=conv_id,
                    messages=[MessageVO(text=f"Cleanup task", role="user")]
                )
                
                await session_manager.submit_session_task(
                    request, f"user_{conv_id}", conn.connection_id, cleanup_processor
                )
            
            # 检查锁的数量
            initial_lock_count = len(session_manager._conversation_locks)
            print(f"    初始锁数量: {initial_lock_count}")
            
            # 等待任务完成
            await asyncio.sleep(0.2)
            
            # 取消部分会话
            for i in range(3):
                conv_id = f"cleanup_conv_{i}"
                await session_manager.cancel_conversation_tasks(conv_id)
            
            # 检查锁清理
            after_cancel_lock_count = len(session_manager._conversation_locks)
            print(f"    取消后锁数量: {after_cancel_lock_count}")
            
            # 关闭管理器
            await session_manager.shutdown()
            
            # 检查最终锁清理
            final_lock_count = len(session_manager._conversation_locks)
            print(f"    关闭后锁数量: {final_lock_count}")
            
            # 验证锁清理
            assert after_cancel_lock_count < initial_lock_count, "取消任务后应该清理部分锁"
            assert final_lock_count == 0, "关闭后应该清理所有锁"
            
            print("    ✅ 锁清理机制验证通过")
            
            # 清理连接
            await connection_manager.close_all_connections()
        
        asyncio.run(test_cleanup())
        return True
        
    except Exception as e:
        print(f"❌ 锁清理机制测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 分段锁性能测试")
    print("=" * 60)
    
    success = True
    
    # 运行测试
    success &= test_concurrent_task_submission()
    success &= test_lock_contention()
    success &= test_same_conversation_serialization()
    success &= test_lock_cleanup()
    
    # 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有分段锁性能测试都通过了！")
        print("\n📊 分段锁优化效果:")
        print("  ✅ 不同会话并发提交不互相阻塞")
        print("  ✅ 锁竞争大幅减少")
        print("  ✅ 同一会话内保持串行化")
        print("  ✅ 锁资源正确清理")
        print("  ✅ 并发性能显著提升")
        print("\n🎯 性能对比:")
        print("  🔴 全局锁: 所有会话串行提交")
        print("  🟢 分段锁: 不同会话并行提交")
        print("  📈 性能提升: 3-10x (取决于会话数量)")
        print("\n💡 技术特点:")
        print("  - 每个会话独立的锁")
        print("  - 双重检查锁定模式")
        print("  - 自动锁清理机制")
        print("  - 统计信息快照读取")
        print("\n🔧 适用场景:")
        print("  - 高并发多会话环境")
        print("  - 需要保证会话内串行的场景")
        print("  - 对响应时间敏感的应用")
    else:
        print("❌ 分段锁性能测试失败，需要进一步修复")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        traceback.print_exc()
        sys.exit(1)
