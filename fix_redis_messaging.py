#!/usr/bin/env python3
"""
Redis 消息接收修复脚本

快速诊断和修复 chat_processor 中 Redis 消息接收的问题。
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def quick_diagnosis():
    """快速诊断 Redis 消息接收问题"""
    print("🔍 快速诊断 Redis 消息接收问题...")
    
    try:
        from src.infrastructure.session.chat_processor import ChatProcessor
        
        # 获取 ChatProcessor 实例
        chat_processor = ChatProcessor.get_instance()
        
        # 等待初始化完成
        print("⏳ 等待 ChatProcessor 初始化...")
        max_wait = 15.0
        wait_interval = 1.0
        waited = 0.0
        
        while waited < max_wait:
            if chat_processor._initialized:
                print("✅ ChatProcessor 初始化完成")
                break
            await asyncio.sleep(wait_interval)
            waited += wait_interval
            print(f"   等待中... ({waited:.1f}s/{max_wait}s)")
        else:
            print("⚠️ ChatProcessor 初始化超时，继续诊断...")
        
        # 运行诊断
        diagnosis = await chat_processor.diagnose_redis_messaging()
        
        print("\n📊 诊断结果:")
        for key, value in diagnosis.items():
            status = "✅" if value else "❌"
            if isinstance(value, list):
                status = "✅" if value else "❌"
                print(f"  {key}: {status} {value}")
            elif isinstance(value, bool):
                print(f"  {key}: {status}")
            else:
                print(f"  {key}: {value}")
        
        return diagnosis
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        traceback.print_exc()
        return None


async def test_message_flow():
    """测试消息流"""
    print("\n🔍 测试消息流...")
    
    try:
        from src.infrastructure.session.chat_processor import ChatProcessor
        
        chat_processor = ChatProcessor.get_instance()
        connection_manager = chat_processor.connection_manager
        
        # 创建测试连接
        test_conv_id = f"test_conv_{int(time.time())}"
        connection = await connection_manager.add_connection(test_conv_id, "test_user")
        
        print(f"📱 创建测试连接: {connection.connection_id}")
        
        # 收集消息
        received_messages = []
        
        async def message_collector():
            try:
                timeout_count = 0
                max_timeouts = 3
                
                while timeout_count < max_timeouts:
                    try:
                        message = await asyncio.wait_for(
                            connection.response_queue.get(), 
                            timeout=2.0
                        )
                        received_messages.append(message)
                        print(f"📨 收到消息: {message}")
                        
                        if message is None:  # 结束信号
                            break
                        
                        timeout_count = 0  # 重置超时计数
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        print(f"⏰ 等待消息超时 ({timeout_count}/{max_timeouts})")
                        
            except Exception as e:
                print(f"❌ 消息收集异常: {e}")
        
        # 启动消息收集
        collector_task = asyncio.create_task(message_collector())
        
        # 等待一下确保收集器启动
        await asyncio.sleep(0.5)
        
        # 发送测试消息
        test_messages = [
            {"type": "test", "content": "Hello Redis!", "timestamp": time.time()},
            {"type": "test", "content": "Message 2", "timestamp": time.time()},
            {"type": "test", "content": "Message 3", "timestamp": time.time()}
        ]
        
        for i, msg in enumerate(test_messages):
            print(f"📤 发送测试消息 {i+1}: {msg}")
            await connection_manager.broadcast_to_conversation(test_conv_id, msg)
            await asyncio.sleep(0.5)  # 间隔发送
        
        # 发送结束信号
        await asyncio.sleep(1.0)
        await connection_manager.broadcast_to_conversation(test_conv_id, None)
        
        # 等待收集完成
        try:
            await asyncio.wait_for(collector_task, timeout=5.0)
        except asyncio.TimeoutError:
            print("⏰ 消息收集超时")
            collector_task.cancel()
        
        # 分析结果
        print(f"\n📊 消息流测试结果:")
        print(f"  发送消息数: {len(test_messages)}")
        print(f"  接收消息数: {len(received_messages)}")
        
        if len(received_messages) >= len(test_messages):
            print("✅ 消息流测试通过")
            return True
        else:
            print("❌ 消息流测试失败 - 消息丢失")
            return False
            
    except Exception as e:
        print(f"❌ 消息流测试失败: {e}")
        traceback.print_exc()
        return False


async def force_reinitialize():
    """强制重新初始化"""
    print("\n🔧 强制重新初始化 Redis 消息系统...")
    
    try:
        from src.infrastructure.session.chat_processor import ChatProcessor
        from src.infrastructure.session.redis_client import RedisClient
        
        # 获取实例
        chat_processor = ChatProcessor.get_instance()
        
        # 重新初始化连接管理器的广播器
        print("🔄 重新初始化广播器...")
        await chat_processor.connection_manager._init_broadcaster()
        
        # 强制重新订阅
        print("🔄 重新订阅消息频道...")
        await chat_processor.connection_manager._subscribe_to_conversation_message_channel()
        
        # 等待一下
        await asyncio.sleep(2.0)
        
        # 重新诊断
        diagnosis = await chat_processor.diagnose_redis_messaging()
        
        print("📊 重新初始化后的状态:")
        for key, value in diagnosis.items():
            status = "✅" if value else "❌"
            if isinstance(value, list):
                status = "✅" if value else "❌"
                print(f"  {key}: {status} {value}")
            elif isinstance(value, bool):
                print(f"  {key}: {status}")
            else:
                print(f"  {key}: {value}")
        
        return diagnosis.get("broadcaster_ready", False) and diagnosis.get("redis_connection", False)
        
    except Exception as e:
        print(f"❌ 强制重新初始化失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🚀 Redis 消息接收修复工具")
    print("=" * 50)
    
    # 1. 快速诊断
    diagnosis = await quick_diagnosis()
    
    if not diagnosis:
        print("❌ 无法进行诊断，请检查代码和配置")
        return False
    
    # 2. 检查是否需要修复
    needs_fix = (
        not diagnosis.get("chat_processor_initialized", False) or
        not diagnosis.get("broadcaster_ready", False) or
        not diagnosis.get("redis_connection", False) or
        not diagnosis.get("subscribed_channels", [])
    )
    
    if needs_fix:
        print("\n🔧 检测到问题，尝试修复...")
        
        # 3. 强制重新初始化
        fix_success = await force_reinitialize()
        
        if fix_success:
            print("✅ 修复成功")
        else:
            print("❌ 修复失败")
            return False
    else:
        print("\n✅ 系统状态正常")
    
    # 4. 测试消息流
    print("\n🧪 测试消息流...")
    flow_test_success = await test_message_flow()
    
    # 5. 总结
    print("\n" + "=" * 50)
    if flow_test_success:
        print("🎉 Redis 消息接收功能正常！")
        print("\n💡 如果仍然收不到消息，请检查:")
        print("  1. Redis 服务是否正常运行")
        print("  2. 网络连接是否正常")
        print("  3. Redis 配置是否正确")
        print("  4. 防火墙设置")
        return True
    else:
        print("❌ Redis 消息接收仍有问题")
        print("\n🔧 建议的修复步骤:")
        print("  1. 检查 Redis 服务状态: redis-cli ping")
        print("  2. 检查配置文件中的 Redis 设置")
        print("  3. 重启应用程序")
        print("  4. 查看详细日志")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 修复过程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 修复过程中出现异常: {e}")
        traceback.print_exc()
        sys.exit(1)
