# 高考志愿填报顾问工作流

这是一个基于LangGraph实现的高考志愿填报顾问系统，可以为高考学生生成详细的志愿填报建议报告。

## 功能特点

1. 分析学生的潜力和发展方向
2. 推荐适合的专业和院校
3. 制定具体的志愿填报方案
4. 规划短期和长期发展路径
5. 由"专家评审团"提供全面的建议

## 安装

1. 克隆仓库：
```bash
git clone <repository-url>
cd <repository-name>
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 设置环境变量：
```bash
export ANTHROPIC_API_KEY=your_anthropic_api_key
export GOOGLE_API_KEY=your_google_api_key
```

## 使用方法

1. 准备学生信息：
```python
student_profile = """
学生基本情况：
- 高考预估分数：650分（重点本科线上100分）
- 理科生，数学、物理较强
- 对计算机和人工智能感兴趣
- 家庭经济条件中等
- 期望就业城市：一线或新一线城市
"""
```

2. 运行工作流：

```python
from ref.docs.workflow import run_workflow

result = run_workflow(student_profile)
print(result["final_report"])
```

## 工作流程

1. 第一阶段：超预期潜力发掘
   - 分析潜在高价值方向
   - 挖掘超出预期的职业前景

2. 第二阶段：专业与院校推荐
   - 从专业到职业的分析
   - 从职业到专业的逆向推导
   - 考虑高考政策因素

3. 第三阶段：志愿填报方案
   - 传统分数优先方案
   - AI潜力最大化方案
   - 学生和家长视角的平衡

4. 第四阶段：发展规划
   - 大学阶段规划
   - 短期职业发展
   - 中长期前景分析

5. 最终阶段：专家评审会总结
   - 由20+位虚拟专家提供多角度建议
   - 综合评估和建议

## 注意事项

1. 需要有效的Anthropic API密钥和Google API密钥
2. 建议提供尽可能详细的学生信息
3. 生成的报告仅供参考，具体填报时还需结合实际情况

## 许可证

MIT License