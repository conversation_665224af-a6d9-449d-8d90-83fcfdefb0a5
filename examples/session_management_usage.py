#!/usr/bin/env python3
"""
会话管理使用示例

展示如何使用新的 ConnectionManager 和 SessionManager 基础设施组件。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.session import ConnectionManager, SessionManager
from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO


async def example_session_processor(request, user_id: str, conversation_id: str):
    """示例会话处理器
    
    Args:
        request: 请求对象
        user_id: 用户ID
        conversation_id: 会话ID
        
    Yields:
        处理结果
    """
    print(f"开始处理会话 {conversation_id} 的请求...")
    
    # 模拟处理过程
    for i in range(3):
        await asyncio.sleep(1)  # 模拟处理时间
        result = {
            "step": i + 1,
            "message": f"处理步骤 {i + 1}",
            "conversation_id": conversation_id,
            "user_id": user_id
        }
        yield result
    
    # 最终结果
    yield {
        "final": True,
        "message": "处理完成",
        "conversation_id": conversation_id
    }


async def basic_usage_example():
    """基本使用示例"""
    print("🔧 基本使用示例")
    print("-" * 40)
    
    # 创建管理器
    connection_manager = ConnectionManager()
    session_manager = SessionManager(connection_manager)
    
    try:
        # 1. 创建连接
        print("1. 创建连接...")
        connection = await connection_manager.add_connection("conv_001", "user_001")
        print(f"   连接ID: {connection.connection_id}")
        
        # 2. 创建请求
        print("2. 创建请求...")
        request = ChatInputVO(
            conversation_id="conv_001",
            messages=[MessageVO(text="Hello", role="user")]
        )
        
        # 3. 提交会话任务
        print("3. 提交会话任务...")
        task_id = await session_manager.submit_session_task(
            request, "user_001", connection.connection_id, example_session_processor
        )
        print(f"   任务ID: {task_id}")
        
        # 4. 读取处理结果
        print("4. 读取处理结果...")
        timeout = 10.0
        while True:
            try:
                result = await asyncio.wait_for(
                    connection.response_queue.get(), 
                    timeout=timeout
                )
                print(f"   收到结果: {result}")
                
                # 检查是否是最终结果
                if isinstance(result, dict) and result.get("final"):
                    break
                    
            except asyncio.TimeoutError:
                print("   等待结果超时")
                break
        
        # 5. 获取统计信息
        print("5. 获取统计信息...")
        conn_stats = await connection_manager.get_connection_stats()
        session_stats = await session_manager.get_manager_stats()
        print(f"   连接统计: {conn_stats}")
        print(f"   会话统计: {session_stats}")
        
    finally:
        # 清理资源
        await connection_manager.remove_connection(connection.connection_id)
        await session_manager.shutdown()
        await connection_manager.close_all_connections()


async def multi_connection_example():
    """多连接示例"""
    print("\n🔗 多连接示例")
    print("-" * 40)
    
    connection_manager = ConnectionManager()
    session_manager = SessionManager(connection_manager)
    
    try:
        # 创建多个连接到同一会话
        print("1. 创建多个连接到同一会话...")
        connections = []
        for i in range(3):
            conn = await connection_manager.add_connection("shared_conv", f"user_{i}")
            connections.append(conn)
            print(f"   创建连接 {i+1}: {conn.connection_id}")
        
        # 提交一个任务
        print("2. 提交会话任务...")
        request = ChatInputVO(
            conversation_id="shared_conv",
            messages=[MessageVO(text="Shared message", role="user")]
        )
        
        task_id = await session_manager.submit_session_task(
            request, "user_0", connections[0].connection_id, example_session_processor
        )
        print(f"   任务ID: {task_id}")
        
        # 所有连接都应该收到结果
        print("3. 所有连接读取结果...")
        
        async def read_from_connection(conn, conn_index):
            print(f"   连接 {conn_index+1} 开始读取...")
            results = []
            while True:
                try:
                    result = await asyncio.wait_for(
                        conn.response_queue.get(), 
                        timeout=5.0
                    )
                    results.append(result)
                    print(f"   连接 {conn_index+1} 收到: {result}")
                    
                    if isinstance(result, dict) and result.get("final"):
                        break
                        
                except asyncio.TimeoutError:
                    break
            return results
        
        # 并发读取所有连接的结果
        tasks = [
            read_from_connection(conn, i) 
            for i, conn in enumerate(connections)
        ]
        
        all_results = await asyncio.gather(*tasks)
        
        # 验证所有连接都收到了相同的结果
        print("4. 验证结果一致性...")
        first_results = all_results[0]
        for i, results in enumerate(all_results[1:], 1):
            if results == first_results:
                print(f"   连接 {i+1} 结果与连接 1 一致 ✅")
            else:
                print(f"   连接 {i+1} 结果与连接 1 不一致 ❌")
        
    finally:
        # 清理资源
        for conn in connections:
            await connection_manager.remove_connection(conn.connection_id)
        await session_manager.shutdown()
        await connection_manager.close_all_connections()


async def connection_resilience_example():
    """连接弹性示例"""
    print("\n🔄 连接弹性示例")
    print("-" * 40)
    
    connection_manager = ConnectionManager()
    session_manager = SessionManager(connection_manager)
    
    try:
        # 创建连接
        print("1. 创建连接...")
        connection = await connection_manager.add_connection("resilient_conv", "user_001")
        print(f"   连接ID: {connection.connection_id}")
        
        # 提交长时间运行的任务
        print("2. 提交长时间任务...")
        
        async def long_running_processor(request, user_id: str, conversation_id: str):
            """长时间运行的处理器"""
            for i in range(10):
                await asyncio.sleep(0.5)
                yield {
                    "step": i + 1,
                    "message": f"长时间处理步骤 {i + 1}/10",
                    "progress": (i + 1) * 10
                }
            yield {"final": True, "message": "长时间处理完成"}
        
        request = ChatInputVO(
            conversation_id="resilient_conv",
            messages=[MessageVO(text="Long task", role="user")]
        )
        
        task_id = await session_manager.submit_session_task(
            request, "user_001", connection.connection_id, long_running_processor
        )
        print(f"   任务ID: {task_id}")
        
        # 读取部分结果后模拟连接断开
        print("3. 读取部分结果...")
        for i in range(3):
            result = await connection.response_queue.get()
            print(f"   收到结果 {i+1}: {result}")
        
        # 模拟连接断开
        print("4. 模拟连接断开...")
        await connection_manager.remove_connection(connection.connection_id)
        print("   连接已断开，但任务继续处理...")
        
        # 等待一段时间让任务继续处理
        await asyncio.sleep(2)
        
        # 重新连接
        print("5. 重新连接...")
        new_connection = await connection_manager.add_connection("resilient_conv", "user_001")
        print(f"   新连接ID: {new_connection.connection_id}")
        
        # 检查是否还有处理中的任务
        session_status = await session_manager.get_session_status("resilient_conv")
        if session_status:
            print(f"   会话状态: {session_status}")
        else:
            print("   任务已完成")
        
        # 等待任务完成
        print("6. 等待任务完成...")
        await asyncio.sleep(3)
        
        final_stats = await session_manager.get_manager_stats()
        print(f"   最终统计: {final_stats}")
        
    finally:
        # 清理资源
        await session_manager.shutdown()
        await connection_manager.close_all_connections()


async def main():
    """主函数"""
    print("🚀 会话管理使用示例")
    print("=" * 60)
    
    try:
        await basic_usage_example()
        await multi_connection_example()
        await connection_resilience_example()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("\n💡 关键特性演示:")
        print("  ✅ 基本连接和会话管理")
        print("  ✅ 多连接广播机制")
        print("  ✅ 连接断开后任务继续处理")
        print("  ✅ 连接重建和状态恢复")
        print("  ✅ 统计信息和监控")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
