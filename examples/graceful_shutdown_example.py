#!/usr/bin/env python3
"""
优雅下线示例

展示如何优雅地关闭 ConnectionManager 和 SessionManager，
确保所有资源被正确清理，正在处理的任务能够完成或被正确取消。
"""

import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.session import ConnectionManager, SessionManager
from src.adapter.vo.ai_chat_model import ChatInputVO, MessageVO


class GracefulShutdownManager:
    """优雅下线管理器"""
    
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.session_manager = SessionManager(self.connection_manager)
        self.shutdown_event = asyncio.Event()
        self.is_shutting_down = False
    
    async def start(self):
        """启动服务"""
        print("🚀 启动会话管理服务...")
        
        # 注册信号处理器
        if sys.platform != 'win32':
            loop = asyncio.get_event_loop()
            for sig in (signal.SIGTERM, signal.SIGINT):
                loop.add_signal_handler(sig, self._signal_handler)
        
        print("✅ 服务启动完成，等待请求...")
        
        # 等待关闭信号
        await self.shutdown_event.wait()
    
    def _signal_handler(self):
        """信号处理器"""
        print("\n📡 收到关闭信号，开始优雅下线...")
        self.shutdown_event.set()
    
    async def shutdown(self):
        """优雅下线"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        print("🔄 开始优雅下线流程...")
        
        try:
            # 1. 停止接受新连接（在实际应用中，这里会停止HTTP服务器）
            print("1. 停止接受新连接...")
            
            # 2. 等待当前处理中的任务完成（设置超时）
            print("2. 等待当前任务完成...")
            await self._wait_for_active_tasks(timeout=30.0)
            
            # 3. 取消剩余任务
            print("3. 取消剩余任务...")
            await self._cancel_remaining_tasks()
            
            # 4. 关闭会话管理器
            print("4. 关闭会话管理器...")
            await self.session_manager.shutdown()
            
            # 5. 关闭所有连接
            print("5. 关闭所有连接...")
            await self.connection_manager.close_all_connections()
            
            print("✅ 优雅下线完成")
            
        except Exception as e:
            print(f"❌ 下线过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    async def _wait_for_active_tasks(self, timeout: float):
        """等待活跃任务完成"""
        start_time = asyncio.get_event_loop().time()
        
        while True:
            stats = await self.session_manager.get_manager_stats()
            active_conversations = stats['active_conversations']
            
            if active_conversations == 0:
                print(f"   所有任务已完成")
                break
            
            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed >= timeout:
                print(f"   等待超时，仍有 {active_conversations} 个活跃会话")
                break
            
            print(f"   等待 {active_conversations} 个活跃会话完成... ({elapsed:.1f}s/{timeout}s)")
            await asyncio.sleep(1.0)
    
    async def _cancel_remaining_tasks(self):
        """取消剩余任务"""
        stats = await self.session_manager.get_manager_stats()
        if stats['active_conversations'] > 0:
            print(f"   取消 {stats['active_conversations']} 个剩余任务...")
            
            # 获取所有活跃会话ID
            active_conversations = list(self.session_manager.active_sessions.keys())
            
            # 取消所有会话任务
            for conversation_id in active_conversations:
                await self.session_manager.cancel_conversation_tasks(conversation_id)


async def long_running_processor(request, user_id: str, conversation_id: str):
    """长时间运行的处理器"""
    try:
        for i in range(20):  # 模拟长时间处理
            await asyncio.sleep(1)
            yield {
                "step": i + 1,
                "message": f"长时间处理步骤 {i + 1}/20",
                "progress": (i + 1) * 5
            }
        
        yield {"final": True, "message": "长时间处理完成"}
        
    except asyncio.CancelledError:
        print(f"   任务 {conversation_id} 被取消")
        yield {"cancelled": True, "message": "任务被取消"}
        raise


async def simulate_workload(shutdown_manager: GracefulShutdownManager):
    """模拟工作负载"""
    print("📊 开始模拟工作负载...")
    
    # 创建多个长时间运行的任务
    tasks = []
    for i in range(3):
        conversation_id = f"conv_{i}"
        
        # 创建连接
        connection = await shutdown_manager.connection_manager.add_connection(
            conversation_id, f"user_{i}"
        )
        
        # 创建请求
        request = ChatInputVO(
            conversation_id=conversation_id,
            messages=[MessageVO(text=f"Long task {i}", role="user")]
        )
        
        # 提交任务
        task_id = await shutdown_manager.session_manager.submit_session_task(
            request, f"user_{i}", connection.connection_id, long_running_processor
        )
        
        print(f"   提交任务 {i+1}: {task_id}")
        
        # 创建读取任务
        async def read_results(conn, task_num):
            try:
                while True:
                    result = await asyncio.wait_for(conn.response_queue.get(), timeout=1.0)
                    print(f"   任务 {task_num} 结果: {result}")
                    
                    if isinstance(result, dict) and (result.get("final") or result.get("cancelled")):
                        break
            except asyncio.TimeoutError:
                pass
            except Exception as e:
                print(f"   任务 {task_num} 读取错误: {e}")
        
        task = asyncio.create_task(read_results(connection, i+1))
        tasks.append(task)
    
    return tasks


async def main():
    """主函数"""
    print("🚀 优雅下线示例")
    print("=" * 60)
    
    shutdown_manager = GracefulShutdownManager()
    
    try:
        # 启动模拟工作负载
        workload_tasks = await simulate_workload(shutdown_manager)
        
        # 模拟运行一段时间后收到关闭信号
        print("\n⏰ 模拟运行 5 秒后收到关闭信号...")
        await asyncio.sleep(5)
        
        # 触发优雅下线
        print("\n📡 触发优雅下线...")
        shutdown_manager.shutdown_event.set()
        
        # 执行优雅下线
        await shutdown_manager.shutdown()
        
        # 等待工作负载任务完成
        print("\n🔄 等待工作负载任务完成...")
        await asyncio.gather(*workload_tasks, return_exceptions=True)
        
        print("\n" + "=" * 60)
        print("🎉 优雅下线示例完成！")
        print("\n💡 优雅下线特性:")
        print("  ✅ 停止接受新连接")
        print("  ✅ 等待当前任务完成（带超时）")
        print("  ✅ 取消剩余任务")
        print("  ✅ 清理所有资源")
        print("  ✅ 信号处理支持")
        
    except KeyboardInterrupt:
        print("\n⚠️ 收到中断信号，执行优雅下线...")
        await shutdown_manager.shutdown()
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        await shutdown_manager.shutdown()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序异常退出: {e}")
        sys.exit(1)
