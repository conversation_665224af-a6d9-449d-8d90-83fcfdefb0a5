---
description: 适配器层架构规范
globs: ["src/adapter/**/*.py"]
alwaysApply: true
---
# Adapter 层特有规范

## 控制器规范

### 基本要求
- 必须只接受 VO 对象作为输入
- 必须只返回 `GatewayResponse[T]` 对象
- 必须执行基本参数验证
- 不得包含任何业务逻辑
- 必须使用全局异常处理
- 必须遵循 REST 约定

### 代码示例
```python
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from .response import GatewayResponse
from src.app.service import ExampleService

router = APIRouter()

class ExampleArgVO(BaseModel):
    name: str
    age: int

class ExampleResultVO(BaseModel):
    id: int
    name: str
    age: int
    status: str
    created_at: str
    updated_at: str

@router.post("/example", response_model=GatewayResponse[ExampleResultVO])
def create_example(arg_vo: ExampleArgVO, service: ExampleService = Depends()):
    # 必须验证输入
    # 先将 ArgVO 转换为 Cmd 对象
    example_cmd = ExampleVOConverter.to_cmd(arg_vo)
    # 然后将 Cmd 对象传递给服务
    bo = service.create_example(example_cmd)
    return GatewayResponse.success(ExampleVOConverter.to_result_vo(bo))
```

## 输入 VO 规范

### 基本要求
- 必须使用 Pydantic 的 BaseModel 作为基类
- 必须包含字段类型注解
- 必须包含字段验证规则
- 必须包含字段描述
- 命名必须以 `ArgVO` 结尾
- 用于接收客户端请求参数

### 代码示例
```python
from pydantic import BaseModel, Field

class CreateUserArgVO(BaseModel):
    name: str = Field(..., max_length=32, description="用户名")
    age: int = Field(..., ge=0, le=150, description="年龄")
    email: str = Field(..., description="邮箱地址")

class UpdateUserArgVO(BaseModel):
    id: int = Field(..., description="用户ID")
    name: str = Field(None, max_length=32, description="用户名")
    age: int = Field(None, ge=0, le=150, description="年龄")
```

## 输出 VO 规范

### 基本要求
- 必须使用 Pydantic 的 BaseModel 作为基类
- 必须包含字段类型注解
- 必须包含字段描述
- 命名必须以 `ResultVO` 结尾
- 用于返回给客户端的响应数据
- 包含完整的业务数据和系统字段（如 ID、时间戳等）

### 代码示例
```python
from typing import List, Optional
from pydantic import BaseModel, Field

class UserResultVO(BaseModel):
    id: int = Field(..., description="用户ID")
    name: str = Field(..., description="用户名")
    age: int = Field(..., description="年龄")
    email: str = Field(..., description="邮箱地址")
    status: str = Field(..., description="用户状态")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    deleted_at: Optional[str] = Field(None, description="删除时间")

class UserListResultVO(BaseModel):
    users: List[UserResultVO] = Field(..., description="用户列表")
    total: int = Field(..., description="总数量")

class OrderResultVO(BaseModel):
    id: int = Field(..., description="订单ID")
    user_id: int = Field(..., description="用户ID")
    amount: float = Field(..., description="订单金额")
    status: str = Field(..., description="订单状态")
    items: List[dict] = Field(..., description="订单项目")
    created_at: str = Field(..., description="创建时间")
```

## 转换器规范

### 基本要求
- 必须提供 ArgVO -> Cmd 的转换方法
- 必须提供 BO -> ResultVO 的转换方法
- 必须处理空值情况
- 必须处理类型转换
- 必须处理字段映射

### 代码示例
```python
from typing import List

class ExampleVOConverter:
    @staticmethod
    def to_result_vo(bo) -> ExampleResultVO:
        return ExampleResultVO(
            id=bo.id,
            name=bo.name,
            age=bo.age,
            status=bo.status,
            created_at=bo.created_at.isoformat(),
            updated_at=bo.updated_at.isoformat(),
            deleted_at=bo.deleted_at.isoformat() if bo.deleted_at else None
        )

    @staticmethod
    def to_cmd(arg_vo: ExampleArgVO) -> ExampleCmd:
        return ExampleCmd(
            name=arg_vo.name,
            age=arg_vo.age
        )

    @staticmethod
    def to_result_vo_list(bo_list: List[ExampleBO]) -> List[ExampleResultVO]:
        return [ExampleVOConverter.to_result_vo(bo) for bo in bo_list]
```

## 响应规范

### 概念说明
**重要提示**：此处的 `GatewayResponse` 不是 DDD 中的 Gateway 概念。它是来自客户端的请求与应答，经过网关层（gateway）之后的统一封装机制，用于标准化 API 响应格式。

- DDD 中的 Gateway：指领域层定义的接口，用于访问外部系统或服务
- GatewayResponse：指网关层对客户端响应的统一封装格式

### 标准响应格式
```python
from typing import Generic, TypeVar, Optional
from pydantic import BaseModel

T = TypeVar('T')

class GatewayResponse(BaseModel, Generic[T]):
    code: int
    message: str
    data: Optional[T] = None

    @staticmethod
    def success(data: T) -> 'GatewayResponse[T]':
        return GatewayResponse(code=0, message="success", data=data)

    @staticmethod
    def error(code: int, message: str) -> 'GatewayResponse':
        return GatewayResponse(code=code, message=message)
```

### 分页响应格式
```python
from typing import Generic, TypeVar, List
from pydantic import BaseModel

T = TypeVar('T')

class PageData(BaseModel, Generic[T]):
    total: int
    page: int
    size: int
    items: List[T]
```

## 异常处理规范

### 异常定义
```python
class BusinessException(Exception):
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
```

### 全局异常处理
```python
from fastapi import Request, FastAPI
from fastapi.responses import JSONResponse

@app.exception_handler(BusinessException)
async def business_exception_handler(request: Request, exc: BusinessException):
    return JSONResponse(
        status_code=400,
        content=GatewayResponse.error(exc.code, exc.message).dict()
    )
```

## 接口文档规范

### 基本要求
- 必须使用 OpenAPI 规范
- 必须包含接口描述
- 必须包含参数说明
- 必须包含响应示例

### 示例
```python
@router.post("/example", 
    response_model=GatewayResponse[ExampleVO],
    summary="创建示例",
    description="创建一个新的示例对象",
    responses={
        200: {"description": "创建成功"},
        400: {"description": "参数错误"},
        500: {"description": "服务器错误"}
    }
)
```