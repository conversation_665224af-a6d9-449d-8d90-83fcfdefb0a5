---
description: 
globs: src/infrastructure/**
alwaysApply: false
---
# Infrastructure 层特有规范

## Repository 实现规范

### 基本要求
- 必须使用 PO 进行数据持久化
- 必须只处理数据操作，不处理事务提交
- 必须不包含业务逻辑和转换逻辑

### 代码示例
```python
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from typing import List, Optional
from uuid import UUID
from .po import UserPO

class UserRepositoryImpl:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def save(self, user_po: UserPO) -> None:
        """保存用户 PO，不提交事务"""
        self.session.add(user_po)

    async def find_by_id(self, user_id: UUID) -> Optional[UserPO]:
        """根据ID查找用户"""
        stmt = select(UserPO).where(UserPO.id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def find_by_email(self, email: str) -> Optional[UserPO]:
        """根据邮箱查找用户"""
        stmt = select(UserPO).where(UserPO.email == email)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def find_active_users(self, limit: int = 100) -> List[UserPO]:
        """查找活跃用户"""
        stmt = select(UserPO).where(UserPO.status == 'active').limit(limit)
        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def update_credits(self, user_id: UUID, credits: int) -> None:
        """更新用户积分"""
        stmt = update(UserPO).where(UserPO.id == user_id).values(credits=credits)
        await self.session.execute(stmt)

    async def delete_by_id(self, user_id: UUID) -> None:
        """删除用户"""
        stmt = delete(UserPO).where(UserPO.id == user_id)
        await self.session.execute(stmt)

    async def batch_save(self, user_pos: List[UserPO]) -> None:
        """批量保存用户"""
        self.session.add_all(user_pos)

## 持久对象规范

### 基本要求
- 必须使用 SQLAlchemy 的模型定义
- 必须指定表名和列定义
- 必须包含必要的索引和约束
- 必须处理关系映射

### 代码示例
```python
from sqlalchemy import Column, String, Integer, DateTime, Boolean, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

class UserPO(Base):
    __tablename__ = "t_user"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(32), nullable=False)
    email = Column(String(128), unique=True, nullable=False)
    credits = Column(Integer, default=0, nullable=False)
    status = Column(String(20), default='active', nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 索引定义
    __table_args__ = (
        Index('idx_user_email', 'email'),
        Index('idx_user_status', 'status'),
        Index('idx_user_created_at', 'created_at'),
    )

    # 关系定义
    orders = relationship("OrderPO", back_populates="user")

class OrderPO(Base):
    __tablename__ = "t_order"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    amount = Column(Integer, nullable=False)
    status = Column(String(20), default='pending', nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # 关系定义
    user = relationship("UserPO", back_populates="orders")
```

## 外部服务集成规范

### 基本要求
- 必须实现资源管理
- 必须处理异常情况
- 必须实现重试机制
- 必须处理超时和熔断

### 代码示例
```python
from typing import Optional, Dict, Any
import aiohttp
import asyncio
from contextlib import asynccontextmanager

class ExternalServiceClient:
    def __init__(self, base_url: str, timeout: int = 30, max_retries: int = 3):
        self.base_url = base_url
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.max_retries = max_retries
        self._session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        self._session = aiohttp.ClientSession(timeout=self.timeout)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._session:
            await self._session.close()

    async def get_data(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """获取数据，带重试机制"""
        for attempt in range(self.max_retries):
            try:
                async with self._session.get(f"{self.base_url}/{endpoint}", params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 404:
                        return None
                    else:
                        response.raise_for_status()
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                if attempt == self.max_retries - 1:
                    raise ExternalServiceException(f"Failed after {self.max_retries} attempts: {str(e)}")
                await asyncio.sleep(2 ** attempt)  # 指数退避

    async def post_data(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送数据"""
        try:
            async with self._session.post(f"{self.base_url}/{endpoint}", json=data) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            raise ExternalServiceException(f"Failed to post data: {str(e)}")

class ExternalServiceException(Exception):
    """外部服务异常"""
    pass
```

## 缓存实现规范

### 基本要求
- 必须处理序列化和反序列化
- 必须实现缓存策略
- 必须处理缓存穿透和雪崩
- 必须支持批量操作

### 代码示例
```python
from typing import Optional, Any, List, Dict
import aioredis
import json
import hashlib
from contextlib import asynccontextmanager

class CacheManager:
    def __init__(self, redis_url: str, default_expire: int = 3600):
        self.redis_url = redis_url
        self.default_expire = default_expire
        self._redis: Optional[aioredis.Redis] = None

    async def __aenter__(self):
        self._redis = aioredis.from_url(self.redis_url)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._redis:
            await self._redis.close()

    def _serialize(self, value: Any) -> str:
        """序列化数据"""
        return json.dumps(value, ensure_ascii=False)

    def _deserialize(self, value: str) -> Any:
        """反序列化数据"""
        return json.loads(value)

    def _generate_key(self, prefix: str, *args) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(arg) for arg in args]
        key_string = ":".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            value = await self._redis.get(key)
            return self._deserialize(value) if value else None
        except Exception:
            return None  # 缓存失败不影响业务

    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存"""
        try:
            expire_time = expire or self.default_expire
            serialized_value = self._serialize(value)
            await self._redis.set(key, serialized_value, ex=expire_time)
            return True
        except Exception:
            return False

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            await self._redis.delete(key)
            return True
        except Exception:
            return False

    async def mget(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取"""
        try:
            values = await self._redis.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value:
                    result[key] = self._deserialize(value)
            return result
        except Exception:
            return {}
```

## 消息队列规范

### 基本要求
- 必须实现连接管理
- 必须处理消息序列化
- 必须实现消息确认和重试
- 必须支持死信队列

### 代码示例
```python
from typing import Any, Callable, Optional
import aio_pika
import json
import asyncio
from contextlib import asynccontextmanager

class MessageQueue:
    def __init__(self, connection_url: str):
        self.connection_url = connection_url
        self._connection: Optional[aio_pika.Connection] = None
        self._channel: Optional[aio_pika.Channel] = None

    async def __aenter__(self):
        self._connection = await aio_pika.connect_robust(self.connection_url)
        self._channel = await self._connection.channel()
        await self._channel.set_qos(prefetch_count=10)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._channel:
            await self._channel.close()
        if self._connection:
            await self._connection.close()

    def _serialize_message(self, data: Any) -> bytes:
        """序列化消息"""
        return json.dumps(data, ensure_ascii=False).encode('utf-8')

    def _deserialize_message(self, body: bytes) -> Any:
        """反序列化消息"""
        return json.loads(body.decode('utf-8'))

    async def declare_queue(self, queue_name: str, durable: bool = True) -> aio_pika.Queue:
        """声明队列"""
        return await self._channel.declare_queue(queue_name, durable=durable)

    async def publish(self, queue_name: str, message: Any, priority: int = 0) -> None:
        """发布消息"""
        queue = await self.declare_queue(queue_name)
        message_body = self._serialize_message(message)
        
        await self._channel.default_exchange.publish(
            aio_pika.Message(
                body=message_body,
                priority=priority,
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT
            ),
            routing_key=queue_name
        )

    async def consume(self, queue_name: str, callback: Callable[[Any], None]) -> None:
        """消费消息"""
        queue = await self.declare_queue(queue_name)
        
        async def message_handler(message: aio_pika.IncomingMessage):
            try:
                data = self._deserialize_message(message.body)
                await callback(data)
                await message.ack()
            except Exception as e:
                await message.nack(requeue=False)  # 发送到死信队列
                raise MessageProcessingException(f"Failed to process message: {str(e)}")

        await queue.consume(message_handler)

class MessageProcessingException(Exception):
    """消息处理异常"""
    pass
```

## 工具类规范

### 基本要求
- 必须提供通用功能
- 必须支持配置化
- 必须实现日志记录
- 必须处理异常情况

### 代码示例
```python
import logging
import hashlib
import uuid
from typing import Any, Optional
from datetime import datetime

class LogUtil:
    def __init__(self, name: str, level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def info(self, message: str, **kwargs: Any) -> None:
        self.logger.info(message, extra=kwargs)

    def error(self, message: str, exc_info: bool = True, **kwargs: Any) -> None:
        self.logger.error(message, exc_info=exc_info, extra=kwargs)

    def warning(self, message: str, **kwargs: Any) -> None:
        self.logger.warning(message, extra=kwargs)

class HashUtil:
    @staticmethod
    def md5(text: str) -> str:
        """生成MD5哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    @staticmethod
    def sha256(text: str) -> str:
        """生成SHA256哈希"""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()

class IDUtil:
    @staticmethod
    def generate_uuid() -> str:
        """生成UUID"""
        return str(uuid.uuid4())

    @staticmethod
    def generate_timestamp_id() -> str:
        """生成基于时间戳的ID"""
        return f"{int(datetime.now().timestamp() * 1000)}{uuid.uuid4().hex[:8]}"
```

## 核心原则

1. **纯技术实现**：Infrastructure 层只处理技术细节，不包含业务逻辑
2. **资源管理**：正确管理数据库连接、HTTP会话等资源
3. **异常处理**：定义技术层面的异常，不泄露实现细节
4. **事务中立**：Repository 不处理事务提交，由上层控制
