---
description: 
globs: src/exe/**
alwaysApply: false
---
# Exe 层特有规范

## 基本规范

- 必须处理单个业务实体操作
- 必须实现 Domain 层定义的 Gateway 接口
- 必须直接依赖 Infrastructure 层
- 不得直接进行跨 Exe 调用
- 必须专注于单一职责
- 必须使用标准返回类型：`*BO`

## Gateway 实现规范

### 基本要求
- 必须实现 Domain 层定义的 Gateway 接口
- 必须直接使用 Infrastructure 层的 Repository 实现
- 必须处理 BO 与 PO 之间的转换
- 必须处理领域异常

### 代码示例
```python
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
from typing import Optional, List
from src.domain.gateway import UserGateway
from src.domain.bo import UserBO
from src.infrastructure.repository import UserRepositoryImpl
from src.infrastructure.po import UserPO
from src.infrastructure.database import DatabaseManager

class UserGatewayImpl(UserGateway):
    def __init__(self):
        self.db_manager = DatabaseManager()

    async def save(self, user_bo: UserBO) -> None:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            user_po = self._bo_to_po(user_bo)
            await repository.save(user_po)

    async def find_by_id(self, user_id: UUID) -> Optional[UserBO]:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            user_po = await repository.find_by_id(user_id)
            return self._po_to_bo(user_po) if user_po else None

    async def find_by_email(self, email: str) -> Optional[UserBO]:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            user_po = await repository.find_by_email(email)
            return self._po_to_bo(user_po) if user_po else None

    async def find_active_users(self, limit: int = 100) -> List[UserBO]:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            pos = await repository.find_active_users(limit)
            return [self._po_to_bo(po) for po in pos]

    async def find_by_credits_range(self, min_credits: int, max_credits: int) -> List[UserBO]:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            pos = await repository.find_by_credits_range(min_credits, max_credits)
            return [self._po_to_bo(po) for po in pos]

    def _bo_to_po(self, bo: UserBO) -> UserPO:
        """BO 转 PO"""
        return UserPO(
            id=bo.id,
            name=bo.name,
            email=bo.email,
            credits=bo.credits
        )

    def _po_to_bo(self, po: UserPO) -> UserBO:
        """PO 转 BO"""
        return UserBO(
            id=po.id,
            name=po.name,
            email=po.email,
            credits=po.credits
        )
```

## 查询 Exe 规范

### 基本要求
- 必须专注于复杂查询逻辑
- 必须直接使用 Infrastructure 层
- 必须返回 BO 对象

### 代码示例
```python
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from src.domain.bo import UserBO
from src.infrastructure.repository import UserRepositoryImpl
from src.infrastructure.database import DatabaseManager

class UserQueryExe:
    def __init__(self):
        self.db_manager = DatabaseManager()

    async def query_active_users(self, limit: int = 100) -> List[UserBO]:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            pos = await repository.find_active_users(limit)
            return [self._po_to_bo(po) for po in pos]

    async def query_by_credits_range(self, min_credits: int, max_credits: int) -> List[UserBO]:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            pos = await repository.find_by_credits_range(min_credits, max_credits)
            return [self._po_to_bo(po) for po in pos]

    def _po_to_bo(self, po: UserPO) -> UserBO:
        return UserBO(
            id=po.id,
            name=po.name,
            email=po.email,
            credits=po.credits
        )
```

## 命令 Exe 规范

### 基本要求
- 必须专注于复杂命令逻辑
- 必须直接使用 Infrastructure 层

### 代码示例
```python
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
from typing import List
from src.domain.bo import UserBO
from src.infrastructure.repository import UserRepositoryImpl
from src.infrastructure.database import DatabaseManager

class UserCommandExe:
    def __init__(self):
        self.db_manager = DatabaseManager()

    async def batch_create_users(self, user_bos: List[UserBO]) -> None:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            user_pos = [self._bo_to_po(bo) for bo in user_bos]
            await repository.batch_save(user_pos)

    async def update_user_credits(self, user_id: UUID, credits: int) -> None:
        async with self.db_manager.get_session() as session:
            repository = UserRepositoryImpl(session)
            await repository.update_credits(user_id, credits)

    def _bo_to_po(self, bo: UserBO) -> UserPO:
        return UserPO(
            id=bo.id,
            name=bo.name,
            email=bo.email,
            credits=bo.credits
        )
```

