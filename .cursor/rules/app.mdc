---
description: 应用层架构规范
globs: ["src/app/**/*.py"]
alwaysApply: true
---
# App 层特有规范

## 服务规范

### 基本要求
- 必须处理业务编排
- 必须协调多个领域对象
- 必须处理事件发布
- 不得包含领域规则
- 不得直接依赖 Infrastructure 层的具体实现

### 代码示例
```python
from src.domain.bo import UserBO
from src.domain.gateway import UserGateway
from src.domain.exception import UserNotFoundException
from src.domain.domain_service import UserDomainService

class UserService:
    def __init__(self, user_gateway: UserGateway):
        self.user_gateway = user_gateway

    async def create_user_with_initial_credits(self, user_cmd: CreateUserCmd) -> UserBO:
        """创建用户并分配初始积分"""
        # 创建用户
        user_bo = UserBO.create(user_cmd.name, user_cmd.age, user_cmd.email)
        await self.user_gateway.save(user_bo)
        
        # 分配初始积分
        user_with_credits = user_bo.add_credits(100)
        await self.user_gateway.save(user_with_credits)
        
        return user_with_credits

    async def transfer_credits(self, transfer_cmd: TransferCreditsCmd) -> None:
        """积分转账"""
        from_user = await self.user_gateway.find_by_id(transfer_cmd.from_user_id)
        to_user = await self.user_gateway.find_by_id(transfer_cmd.to_user_id)
        
        if not from_user:
            raise UserNotFoundException(str(transfer_cmd.from_user_id))
        if not to_user:
            raise UserNotFoundException(str(transfer_cmd.to_user_id))
        
        # 使用领域服务处理业务规则
        updated_from, updated_to = UserDomainService.transfer_credits(
            from_user, to_user, transfer_cmd.credits
        )
        
        # 保存更新后的用户
        await self.user_gateway.save(updated_from)
        await self.user_gateway.save(updated_to)
```

## 查询对象规范

### 基本要求
- 必须使用 Pydantic 的 BaseModel 作为基类
- 必须包含查询条件
- 必须包含分页参数
- 必须包含排序参数

### 代码示例
```python
from pydantic import BaseModel, Field
from typing import Optional

class UserQry(BaseModel):
    name: Optional[str] = Field(None, description="用户名")
    age_min: Optional[int] = Field(None, ge=0, description="最小年龄")
    age_max: Optional[int] = Field(None, le=150, description="最大年龄")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")
```

## 命令对象规范

### 基本要求
- 必须使用 Pydantic 的 BaseModel 作为基类
- 必须包含命令参数
- 必须包含验证规则
- 必须包含业务约束

### 代码示例
```python
from pydantic import BaseModel, Field
from uuid import UUID

class CreateUserCmd(BaseModel):
    name: str = Field(..., max_length=32, description="用户名")
    age: int = Field(..., ge=0, le=150, description="年龄")
    email: str = Field(..., regex=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", description="邮箱")

class TransferCreditsCmd(BaseModel):
    from_user_id: UUID = Field(..., description="转出用户ID")
    to_user_id: UUID = Field(..., description="转入用户ID")
    credits: int = Field(..., gt=0, description="转账积分数量")
```

## 事件处理规范

### 基本要求
- 必须定义事件类型
- 必须实现事件发布
- 必须实现事件订阅
- 必须处理事件异常


