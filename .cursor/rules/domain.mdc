---
description: 
globs: src/domain/**
alwaysApply: false
---
# Domain 层特有规范

## 业务对象（BO）规范

### 基本要求
- 必须是聚合根或实体的数据表示
- 必须是不可变的
- 必须包含业务行为和规则
- 必须用于跨层数据传输
- 必须包含唯一标识

### 聚合根 BO 示例
```python
from dataclasses import dataclass, replace
from typing import List, Optional
from uuid import UUID, uuid4

@dataclass(frozen=True)
class UserBO:
    id: UUID
    name: str
    age: int
    email: Optional[str] = None
    credits: int = 0

    def is_adult(self) -> bool:
        return self.age >= 18

    def add_credits(self, credits: int) -> 'UserBO':
        if credits <= 0:
            raise ValueError("Credits must be positive")
        return replace(self, credits=self.credits + credits)

    def deduct_credits(self, credits: int) -> 'UserBO':
        if credits <= 0:
            raise ValueError("Credits must be positive")
        if self.credits < credits:
            raise InsufficientCreditsException(str(self.id), credits, self.credits)
        return replace(self, credits=self.credits - credits)

    @staticmethod
    def create(name: str, age: int, email: Optional[str] = None) -> 'UserBO':
        return UserBO(
            id=uuid4(),
            name=name,
            age=age,
            email=email,
            credits=0
        )
```

### 实体 BO 示例
```python
@dataclass(frozen=True)
class OrderItemBO:
    id: UUID
    product_id: UUID
    quantity: int
    unit_price: float

    def __post_init__(self):
        if self.quantity <= 0:
            raise ValueError("Quantity must be positive")
        if self.unit_price < 0:
            raise ValueError("Unit price cannot be negative")

    def calculate_total(self) -> float:
        return self.quantity * self.unit_price

    def update_quantity(self, new_quantity: int) -> 'OrderItemBO':
        if new_quantity <= 0:
            raise ValueError("Quantity must be positive")
        return replace(self, quantity=new_quantity)
```

## 值对象规范

### 基本要求
- 必须是不可变的
- 必须包含验证逻辑
- 必须实现相等性比较
- 必须不包含业务标识
- 必须是自验证的

### 代码示例
```python
from dataclasses import dataclass
import re

@dataclass(frozen=True)
class Email:
    value: str

    def __post_init__(self):
        if not self._is_valid_email(self.value):
            raise ValueError(f"Invalid email format: {self.value}")

    @staticmethod
    def _is_valid_email(email: str) -> bool:
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
```

## 领域服务规范

### 基本要求
- 必须处理跨聚合的业务规则
- 必须不包含技术实现细节
- 必须使用领域对象
- 必须返回领域对象而不是执行副作用
- 必须保持无状态

### 代码示例
```python
from typing import Tuple

class UserDomainService:
    @staticmethod
    def transfer_credits(from_user: UserBO, to_user: UserBO, credits: int) -> Tuple[UserBO, UserBO]:
        if credits <= 0:
            raise ValueError("Transfer credits must be positive")
        if from_user.id == to_user.id:
            raise InvalidTransferException("Cannot transfer credits to yourself")
        
        updated_from = from_user.deduct_credits(credits)
        updated_to = to_user.add_credits(credits)
        return updated_from, updated_to
```

## 领域事件规范

### 基本要求
- 必须包含事件类型和版本
- 必须是不可变的
- 必须支持序列化

### 代码示例
```python
from datetime import datetime

@dataclass(frozen=True)
class DomainEvent:
    event_id: UUID
    event_type: str
    event_version: str
    event_time: datetime
    event_source: UUID
    event_data: dict

@dataclass(frozen=True)
class CreditsTransferredEvent(DomainEvent):
    def __init__(self, from_user_id: UUID, to_user_id: UUID, credits: int):
        super().__init__(
            event_id=uuid4(),
            event_type="CREDITS_TRANSFERRED",
            event_version="1.0",
            event_time=datetime.utcnow(),
            event_source=from_user_id,
            event_data={"from_user_id": str(from_user_id), "to_user_id": str(to_user_id), "credits": credits}
        )
```

## Gateway 接口规范

### 基本要求
- 必须定义清晰的接口契约
- 必须使用 BO 对象
- 必须不包含实现细节

### 代码示例
```python
from abc import ABC, abstractmethod
from typing import Optional, List

class UserGateway(ABC):
    @abstractmethod
    async def save(self, user: UserBO) -> None:
        pass

    @abstractmethod
    async def find_by_id(self, user_id: UUID) -> Optional[UserBO]:
        pass

    @abstractmethod
    async def find_by_email(self, email: str) -> Optional[UserBO]:
        pass

    @abstractmethod
    async def find_active_users(self, limit: int = 100) -> List[UserBO]:
        pass

    @abstractmethod
    async def find_by_credits_range(self, min_credits: int, max_credits: int) -> List[UserBO]:
        pass
```

## 领域异常规范

### 基本要求
- 必须建立异常层次结构
- 必须包含错误代码和消息
- 必须包含业务上下文

### 代码示例
```python
class DomainException(Exception):
    def __init__(self, code: str, message: str, context: dict = None):
        self.code = code
        self.message = message
        self.context = context or {}
        super().__init__(f"[{code}] {message}")

class InsufficientCreditsException(DomainException):
    def __init__(self, user_id: str, required_credits: int, available_credits: int):
        super().__init__(
            code="INSUFFICIENT_CREDITS",
            message=f"User {user_id} has insufficient credits. Required: {required_credits}, Available: {available_credits}",
            context={"user_id": user_id, "required_credits": required_credits, "available_credits": available_credits}
        )

class InvalidTransferException(DomainException):
    def __init__(self, message: str):
        super().__init__(
            code="INVALID_TRANSFER",
            message=message,
            context={}
        )

class UserNotFoundException(DomainException):
    def __init__(self, user_id: str):
        super().__init__(
            code="USER_NOT_FOUND",
            message=f"User with id {user_id} not found",
            context={"user_id": user_id}
        )
```
