---
description: 
globs: 
alwaysApply: true
---
# Overall Cursor Rules - DDD 架构规范

## 核心原则
1. **严格遵循 DDD 分层架构**：adapter、app、domain、exe、infrastructure
2. **domain 层纯净性**：domain 层只能依赖自身和基础类库，不能依赖任何技术实现
3. **接口优先**：使用接口定义契约，实现依赖倒置

## 命名规范

### 类命名规范
- 使用 **PascalCase**（大驼峰命名法）
- 根据层级和职责添加相应后缀
- 示例：`UserService`、`OrderEntity`、`PaymentGateway`

### 文件命名规范
- 使用 **snake_case**（小写下划线命名法）
- 文件名应反映其包含的主要类的功能
- 示例：`user_service.py`、`order_entity.py`、`payment_gateway.py`

### 包/目录命名规范
- 使用 **snake_case**（小写下划线命名法）
- 按层级和功能模块组织
- 示例：`src/adapter/api/`、`src/domain/user/`、`src/infrastructure/persistence/`

## 分层职责与约束

### adapter 层（最上层）
**职责**：外部接口适配、数据转换、响应格式化

**允许依赖**：app、domain、exe、infrastructure 层（可依赖所有下层）
**禁止依赖**：无（作为最上层，可以依赖所有下层）
**允许被依赖**：无（作为最上层，不应被任何层依赖）
**禁止被依赖**：app、domain、exe、infrastructure 层（严禁所有下层调用）

**类命名**：`*Router`、`*VO`、`*VOConverter`
**文件命名**：`*_router.py`、`*_vo.py`、`*_vo_converter.py`

### app 层（应用编排层）
**职责**：业务应用编排、多领域对象协调

**允许依赖**：domain、exe、infrastructure 层
**禁止依赖**：adapter 层（严禁调用上层）
**允许被依赖**：adapter 层
**禁止被依赖**：domain、exe、infrastructure 层（严禁下层调用）

**类命名**：`*Service`、`*Cmd`、`*Qry`
**文件命名**：`*_service.py`、`*_cmd.py`、`*_qry.py`

### domain 层（领域核心层）
**职责**：业务规则、领域逻辑、接口定义

**允许依赖**：domain 层内部、基础类库（Python 标准库等）
**禁止依赖**：adapter、app、exe、infrastructure 层（严禁依赖任何其他层）
**允许被依赖**：adapter、app、exe、infrastructure 层（被所有层依赖）
**禁止被依赖**：无（domain 层作为核心层，应该被所有其他层依赖）

**类命名**：`*Entity`、`*Value`、`*BO`、`*DomainService`、`*Gateway`
**文件命名**：`*_entity.py`、`*_value.py`、`*_bo.py`、`*_domain_service.py`、`*_gateway.py`

### exe 层（原子执行层）
**职责**：单一业务实体操作、查询执行、Gateway 接口实现

**允许依赖**：domain、infrastructure 层
**禁止依赖**：adapter、app 层（严禁依赖上层）、exe 层之间（严禁相互调用）
**允许被依赖**：adapter、app 层
**禁止被依赖**：domain、infrastructure 层（严禁下层调用）

**类命名**：`*Exe`、`*QueryExe`、`*CommandExe`、`*GatewayImpl`
**文件命名**：`*_exe.py`、`*_query_exe.py`、`*_command_exe.py`、`*_gateway_impl.py`

### infrastructure 层（基础设施层）
**职责**：技术实现、外部服务集成、数据持久化

**允许依赖**：技术框架、外部服务
**禁止依赖**：adapter、app、domain、exe 层（严禁依赖上层）
**允许被依赖**：adapter、app、exe 层
**禁止被依赖**：domain 层（严禁 domain 层依赖 infrastructure 层）

**类命名**：`*RepositoryImpl`、`*PO`、`*Client`、`*Util`
**文件命名**：`*_repository_impl.py`、`*_po.py`、`*_client.py`、`*_util.py`

## 架构违规检测

### 🚨 严重违规（立即修复）
- domain 层依赖 adapter、app、exe、infrastructure 层
- 下层模块调用上层模块
- 在错误的层实现业务逻辑

### ⚠️ 设计问题（需要重构）
- 单个文件包含多层职责
- 命名不符合层级规范
- 缺少接口抽象

### ✅ 推荐模式
- 使用 Gateway 模式：domain 层定义接口，exe 层实现
- 依赖注入：通过构造函数注入依赖
- 接口隔离：每个接口职责单一

## 重构策略

### 发现违规时的处理步骤：
1. **识别违规类型**：依赖反转 vs 职责混乱 vs 命名不规范
2. **选择重构方案**：
   - 简单违规：直接移动文件到正确层级
   - 复杂违规：使用 Gateway 模式重构
   - 职责混乱：按职责拆分到不同层级
3. **保持向后兼容**：使用代理模式过渡
4. **验证架构**：确保依赖关系正确

### Gateway 模式重构模板：
```python
# domain 层：定义接口
class BusinessGateway(ABC):
    @abstractmethod
    async def business_operation(self, params) -> Result:
        pass

# exe 层：实现接口
class BusinessGatewayImpl(BusinessGateway):
    async def business_operation(self, params) -> Result:
        # 具体实现，可以调用 infrastructure 层
        pass

# 原文件：变成代理
_gateway = BusinessGatewayImpl()

def legacy_function(params):
    return _gateway.business_operation(params)
```

## 代码审查检查点

### 新增文件时检查：
- [ ] 文件放在正确的层级目录
- [ ] 文件命名使用 snake_case 格式
- [ ] 类命名使用 PascalCase 格式并符合层级后缀规范
- [ ] 依赖关系符合架构约束
- [ ] 职责单一且明确

### 修改现有文件时检查：
- [ ] 没有引入架构违规依赖
- [ ] 没有在错误的层添加业务逻辑
- [ ] 保持接口稳定性

### 重构时检查：
- [ ] 依赖方向正确
- [ ] 接口抽象合理
- [ ] 向后兼容性
- [ ] 测试覆盖完整

## 常见反模式

### ❌ 避免这些模式：
```python
# domain 层直接调用 infrastructure
from src.infrastructure.repository import UserRepositoryImpl  # 违规

# 在 domain 层包含技术实现
class UserEntity:
    def save_to_database(self):  # 违规
        pass

# 循环依赖
# A 依赖 B，B 又依赖 A  # 违规

# 错误的命名方式
class userService:  # 违规：类名应使用 PascalCase
    pass

# 文件名：UserService.py  # 违规：文件名应使用 snake_case
# 正确的文件名应该是：user_service.py
```

### ✅ 推荐这些模式：
```python
# domain 层定义接口（文件：user_gateway.py）
class UserGateway(ABC):
    @abstractmethod
    def save(self, user: UserBO) -> None:
        pass

# exe 层实现接口（文件：user_gateway_impl.py）
class UserGatewayImpl(UserGateway):
    def save(self, user: UserBO) -> None:
        # 具体实现，可以调用 infrastructure 层
        pass

# 通过依赖注入使用（文件：user_service.py）
class UserService:
    def __init__(self, user_gateway: UserGateway):
        self.user_gateway = user_gateway
```

## 记住：架构的目的是让代码更易维护、测试和扩展，而不是增加复杂性。当发现架构违规时，优先考虑最简单有效的解决方案。
