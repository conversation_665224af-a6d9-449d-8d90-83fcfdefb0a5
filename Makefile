.PHONY: check-arch check-imports install install-dev help clean test

# 变量定义
PYTHON := python
SRC_DIR := src
SCRIPTS_DIR := scripts

# 安装开发依赖
install-dev:
	pip install -r requirements-dev.txt

# 安装生产依赖
install:
	pip install -r requirements.txt

# 检查架构约束（使用 import-linter）
check-imports:
	@echo "🔍 使用 import-linter 检查架构约束..."
	lint-imports

# 检查架构约束（使用自定义脚本）
check-arch:
	@echo "🔍 使用自定义脚本检查架构约束..."
	$(PYTHON) $(SCRIPTS_DIR)/check_architecture.py

# 安装 pre-commit 钩子
install-hooks:
	pre-commit install

# 运行所有检查
check-all: check-imports check-arch
	@echo "✅ 所有架构检查完成"

# 清理缓存文件
clean:
	@echo "🧹 清理缓存文件..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	@echo "✅ 清理完成"

# 运行测试（如果有的话）
test:
	@if [ -d "tests" ]; then \
		echo "🧪 运行测试..."; \
		$(PYTHON) -m pytest tests/; \
	else \
		echo "⚠️  未找到测试目录"; \
	fi

# 显示帮助信息
help:
	@echo "可用命令:"
	@echo "  install        - 安装生产依赖"
	@echo "  install-dev    - 安装开发依赖（包含生产依赖）"
	@echo "  check-imports  - 使用 import-linter 检查架构约束"
	@echo "  check-arch     - 使用自定义脚本检查架构约束"
	@echo "  install-hooks  - 安装 pre-commit 钩子"
	@echo "  check-all      - 运行所有架构检查"
	@echo "  clean          - 清理缓存文件"
	@echo "  test           - 运行测试" 